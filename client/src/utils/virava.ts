import {
  type KeycloakConfigDefault,
  KeycloakServiceDefault,
  AuthServiceFactory,
  ServiceType,
} from 'virava';
import type * as t from 'librechat-data-provider';
import { SystemRoles } from 'librechat-data-provider';
import type { KeycloakTokenPayload } from '~/common';

// ==============================================================================
// SERVICE CONFIGURATION
// ==============================================================================

// Debug environment variables
console.log('🔍 Keycloak Environment Variables Debug:');
console.log('VITE_CLIENT_ID:', import.meta.env.VITE_CLIENT_ID);
console.log('VITE_BASE_URL:', import.meta.env.VITE_BASE_URL);
console.log('VITE_REALM:', import.meta.env.VITE_REALM);
console.log(
  'All VITE vars:',
  Object.keys(import.meta.env).filter((key) => key.startsWith('VITE_')),
);

export const authService = AuthServiceFactory.create(ServiceType.DEFAULT) as KeycloakServiceDefault;

// Helper function to get environment variable with runtime fallback
const getEnvVar = (key: string): string => {
  // First try import.meta.env (build-time)
  const buildTimeValue = import.meta.env[key];
  if (buildTimeValue) {
    return buildTimeValue;
  }

  // Fallback to runtime environment (for Kubernetes deployments)
  const runtimeEnv = (window as any).__RUNTIME_ENV__;
  if (runtimeEnv && runtimeEnv[key]) {
    console.log(`📦 Using runtime env for ${key}:`, runtimeEnv[key]);
    return runtimeEnv[key];
  }

  // Last resort: check if it's available on window.__VITE_ENV__
  const viteEnv = (window as any).__VITE_ENV__;
  if (viteEnv && viteEnv[key]) {
    console.log(`🔧 Using window.__VITE_ENV__ for ${key}:`, viteEnv[key]);
    return viteEnv[key];
  }

  console.warn(`⚠️ Environment variable ${key} not found in any source`);
  return '';
};

export const authConfig: KeycloakConfigDefault = {
  clientId: getEnvVar('VITE_CLIENT_ID'),
  baseUrl: getEnvVar('VITE_BASE_URL'),
  realm: getEnvVar('VITE_REALM'),
};

// Validate configuration
const validateAuthConfig = () => {
  const errors: string[] = [];

  if (!authConfig.clientId) {
    errors.push('VITE_CLIENT_ID is missing or empty');
  }
  if (!authConfig.baseUrl) {
    errors.push('VITE_BASE_URL is missing or empty');
  }
  if (!authConfig.realm) {
    errors.push('VITE_REALM is missing or empty');
  }

  if (errors.length > 0) {
    console.error('❌ Keycloak Configuration Errors:', errors);
    console.error('Current config:', authConfig);
    return false;
  }

  console.log('✅ Keycloak Configuration Valid:', authConfig);
  return true;
};

// Validate on module load
validateAuthConfig();

// ==============================================================================
// UTILITY FUNCTIONS
// ==============================================================================

/**
 * Detects if the current URL contains Keycloak OAuth callback parameters
 * @returns {boolean} True if callback parameters are present
 */
export const hasKeycloakCallbackParams = (): boolean => {
  const searchParams = new URLSearchParams(window.location.search);
  const hashParams = new URLSearchParams(window.location.hash.substring(1));
  return (
    searchParams.has('code') ||
    searchParams.has('state') ||
    hashParams.has('code') ||
    hashParams.has('state')
  );
};

/**
 * Creates a TUser object from a Keycloak token payload
 * @param {KeycloakTokenPayload} userToken - The decoded JWT token
 * @returns {t.TUser} User data object
 */
export const createUserDataFromToken = (userToken: KeycloakTokenPayload): t.TUser => {
  return {
    id: userToken.sub || 'unknown',
    name: userToken.name || userToken.preferred_username || 'Unknown User',
    username: userToken.preferred_username || 'unknown',
    email: userToken.email || '',
    role: SystemRoles.USER,
    provider: 'keycloak',
    avatar: '',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
};
