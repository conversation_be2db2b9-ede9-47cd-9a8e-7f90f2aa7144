import { type TFunction } from 'i18next';
import { type ReactNode } from 'react';
import { type HeaderIconConfig } from '~/components/Nav/Header';
import { type NavTabItem, type DropdownItem } from '~/common/types';
import type * as t from 'librechat-data-provider';

// ============================================================================
// CONSTANTS
// ============================================================================

/**
 * Navigation tabs configuration - constant array
 */
export const NAV_TABS: NavTabItem[] = [
  {
    id: 'chat',
    icon: '/assets/dlt_icon_deploy.svg',
    textKey: 'com_nav_chat',
  },
];

// ============================================================================
// TYPES
// ============================================================================

interface CreateHeaderIconsParams {
  t: TFunction;
  user: t.TUser | null;
  isProfileDropdownOpen: boolean;
  setIsProfileDropdownOpen: (open: boolean) => void;
  handleHelpClick: () => void;
  handleNotificationClick: () => void;
  handleUserProfile: () => void;
  handleLogout: () => void;
  profileDropdownHeader: ReactNode;
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Creates profile dropdown items
 */
export const createProfileDropdownItems = (
  _t: TFunction,
  handleUserProfile: () => void,
  handleLogout: () => void,
): DropdownItem[] => [
  {
    id: 'profile',
    icon: '/assets/dlt_icon_profile.svg',
    textKey: 'com_nav_user_profile',
    onClick: handleUserProfile,
  },
  {
    id: 'separator1',
    textKey: '',
    onClick: () => {},
    type: 'separator' as const,
  },
  {
    id: 'logout',
    icon: '/assets/dlt_icon_signout.svg',
    textKey: 'com_nav_logout',
    onClick: handleLogout,
  },
];

/**
 * Creates complete header icons configuration
 */
export const createHeaderIconsConfig = ({
  t,
  isProfileDropdownOpen,
  setIsProfileDropdownOpen,
  handleHelpClick,
  handleNotificationClick,
  handleUserProfile,
  handleLogout,
  profileDropdownHeader,
}: CreateHeaderIconsParams): HeaderIconConfig[] => {
  const profileDropdownItems = createProfileDropdownItems(t, handleUserProfile, handleLogout);

  return [
    {
      id: 'help',
      icon: '/assets/dlt_icon_help.svg',
      ariaLabel: t('com_nav_help'),
      showDropdown: false,
      onClick: handleHelpClick,
    },
    {
      id: 'notification',
      icon: '/assets/dlt_icon_notification.svg',
      ariaLabel: t('com_nav_notifications'),
      showDropdown: false,
      onClick: handleNotificationClick,
    },
    {
      id: 'profile',
      icon: '/assets/dlt_icon_profile.svg',
      ariaLabel: t('com_nav_profile'),
      showDropdown: true,
      dropdownItems: profileDropdownItems,
      dropdownHeader: profileDropdownHeader,
      isOpen: isProfileDropdownOpen,
      onOpenChange: setIsProfileDropdownOpen,
    },
  ];
};