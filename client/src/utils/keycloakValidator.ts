/**
 * Keycloak Configuration Validator
 * 
 * This utility validates Keycloak configuration and connectivity
 * to help diagnose authentication issues in production deployments.
 */

export interface KeycloakValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  config: {
    clientId: string;
    baseUrl: string;
    realm: string;
  };
  connectivity: {
    baseUrlReachable: boolean;
    realmEndpointReachable: boolean;
    wellKnownEndpointReachable: boolean;
  };
}

/**
 * Validates Keycloak configuration
 */
export async function validateKeycloakConfig(config: {
  clientId: string;
  baseUrl: string;
  realm: string;
}): Promise<KeycloakValidationResult> {
  const result: KeycloakValidationResult = {
    isValid: true,
    errors: [],
    warnings: [],
    config,
    connectivity: {
      baseUrlReachable: false,
      realmEndpointReachable: false,
      wellKnownEndpointReachable: false,
    },
  };

  // Validate configuration values
  validateConfigValues(config, result);

  // Test connectivity (only if config is valid)
  if (result.errors.length === 0) {
    await testConnectivity(config, result);
  }

  // Set overall validity
  result.isValid = result.errors.length === 0;

  return result;
}

/**
 * Validates configuration values
 */
function validateConfigValues(
  config: { clientId: string; baseUrl: string; realm: string },
  result: KeycloakValidationResult
): void {
  // Check required fields
  if (!config.clientId || config.clientId.trim() === '') {
    result.errors.push('Client ID is required but not provided');
  }

  if (!config.baseUrl || config.baseUrl.trim() === '') {
    result.errors.push('Base URL is required but not provided');
  }

  if (!config.realm || config.realm.trim() === '') {
    result.errors.push('Realm is required but not provided');
  }

  // Validate base URL format
  if (config.baseUrl) {
    try {
      const url = new URL(config.baseUrl);
      if (!['http:', 'https:'].includes(url.protocol)) {
        result.errors.push('Base URL must use HTTP or HTTPS protocol');
      }
      if (!url.hostname) {
        result.errors.push('Base URL must include a valid hostname');
      }
      
      // Ensure base URL ends with /
      if (!config.baseUrl.endsWith('/')) {
        result.warnings.push('Base URL should end with a forward slash (/)');
      }
    } catch (error) {
      result.errors.push(`Invalid base URL format: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Validate client ID format
  if (config.clientId && config.clientId.includes(' ')) {
    result.warnings.push('Client ID contains spaces, which may cause issues');
  }

  // Validate realm format
  if (config.realm && config.realm.includes(' ')) {
    result.warnings.push('Realm name contains spaces, which may cause issues');
  }
}

/**
 * Tests connectivity to Keycloak endpoints
 */
async function testConnectivity(
  config: { clientId: string; baseUrl: string; realm: string },
  result: KeycloakValidationResult
): Promise<void> {
  const timeout = 10000; // 10 seconds

  // Test base URL
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(config.baseUrl, {
      method: 'HEAD',
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    result.connectivity.baseUrlReachable = response.ok || response.status < 500;
    
    if (!result.connectivity.baseUrlReachable) {
      result.warnings.push(`Base URL returned status ${response.status}`);
    }
  } catch (error) {
    result.warnings.push(`Cannot reach base URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  // Test realm endpoint
  const realmUrl = `${config.baseUrl}realms/${config.realm}`;
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(realmUrl, {
      method: 'HEAD',
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    result.connectivity.realmEndpointReachable = response.ok;
    
    if (!result.connectivity.realmEndpointReachable) {
      result.warnings.push(`Realm endpoint returned status ${response.status}`);
    }
  } catch (error) {
    result.warnings.push(`Cannot reach realm endpoint: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }

  // Test well-known configuration endpoint
  const wellKnownUrl = `${config.baseUrl}realms/${config.realm}/.well-known/openid_configuration`;
  try {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), timeout);
    
    const response = await fetch(wellKnownUrl, {
      signal: controller.signal,
    });
    
    clearTimeout(timeoutId);
    result.connectivity.wellKnownEndpointReachable = response.ok;
    
    if (result.connectivity.wellKnownEndpointReachable) {
      // Try to parse the response to validate it's proper OIDC configuration
      try {
        const config = await response.json();
        if (!config.authorization_endpoint || !config.token_endpoint) {
          result.warnings.push('Well-known endpoint does not contain required OIDC configuration');
        }
      } catch {
        result.warnings.push('Well-known endpoint returned invalid JSON');
      }
    } else {
      result.warnings.push(`Well-known endpoint returned status ${response.status}`);
    }
  } catch (error) {
    result.warnings.push(`Cannot reach well-known endpoint: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Logs validation results to console
 */
export function logValidationResults(result: KeycloakValidationResult): void {
  console.log('🔍 Keycloak Configuration Validation Results');
  console.log('============================================');
  
  console.log('📋 Configuration:');
  console.log(`  Client ID: ${result.config.clientId || 'NOT SET'}`);
  console.log(`  Base URL: ${result.config.baseUrl || 'NOT SET'}`);
  console.log(`  Realm: ${result.config.realm || 'NOT SET'}`);
  
  console.log('\n🌐 Connectivity:');
  console.log(`  Base URL reachable: ${result.connectivity.baseUrlReachable ? '✅' : '❌'}`);
  console.log(`  Realm endpoint reachable: ${result.connectivity.realmEndpointReachable ? '✅' : '❌'}`);
  console.log(`  Well-known endpoint reachable: ${result.connectivity.wellKnownEndpointReachable ? '✅' : '❌'}`);
  
  if (result.errors.length > 0) {
    console.log('\n❌ Errors:');
    result.errors.forEach(error => console.log(`  • ${error}`));
  }
  
  if (result.warnings.length > 0) {
    console.log('\n⚠️ Warnings:');
    result.warnings.forEach(warning => console.log(`  • ${warning}`));
  }
  
  console.log(`\n🎯 Overall Status: ${result.isValid ? '✅ VALID' : '❌ INVALID'}`);
}
