import type { KeycloakServiceDefault } from 'virava';

// Constants
const CODE_UNAUTHORIZED = 401;
const CODE_FORBIDDEN = 403;

// Get auth service reference - will be set by the auth context
let authService: KeycloakServiceDefault | null = null;

export const setAuthService = (service: KeycloakServiceDefault) => {
  authService = service;
};

// Simple request interceptor function
const setAuthHeader = (
  headers: Record<string, string> = {},
  token: string | null,
): Record<string, string> => {
  return token ? { ...headers, Authorization: `Bearer ${token}` } : headers;
};

// Define error type with status code
interface ApiError {
  status?: number;
  message?: string;
}

// Define options type that gets passed to service functions
interface ServiceOptions {
  headers?: Record<string, string>;
  getAccessToken: () => string | null;
  onLogout: () => Promise<void>;
  navigate: (path: string) => void;
}

export async function interceptRequest<Type>(
  serviceFunc: (...args: unknown[]) => Promise<Type>,
  options: ServiceOptions,
  ...args: unknown[]
): Promise<Type> {
  try {
    // Set auth header and make the request
    const token = options.getAccessToken();
    const headers = setAuthHeader(options.headers, token);

    return await serviceFunc(...args, { ...options, headers });
  } catch (error) {
    const apiError = error as ApiError;
    const status = apiError?.status;

    if (status === CODE_UNAUTHORIZED) {
      try {
        console.warn('Token expired. Attempting refresh...');

        // Try to refresh token using auth service
        if (authService && typeof authService.updateToken === 'function') {
          await authService.updateToken();
          const newToken = options.getAccessToken();

          if (newToken) {
            const newHeaders = setAuthHeader(options.headers, newToken);
            return await serviceFunc(...args, { ...options, headers: newHeaders });
          }
        }

        console.error('Token refresh failed. Logging out...');
        await options.onLogout();
        throw new Error('Authentication expired. Please log in again.');
      } catch (_refreshErr) {
        await options.onLogout();
        throw new Error('Session expired. Redirecting to login...');
      }
    }

    if (status === CODE_FORBIDDEN) {
      console.error('Forbidden request. Redirecting to home...');
      options.navigate('/');
      throw new Error('You do not have permission to perform this action.');
    }

    console.error('Unhandled API error:', error);
    throw new Error('Something went wrong. Please try again later.');
  }
}

// Setup function to configure interceptor globally (if needed)
export const setupApiInterceptor = (options: {
  getAccessToken: () => string | null;
  onLogout: () => Promise<void>;
  navigate: (path: string) => void;
}) => {
  // Store options for global use if needed
  console.log('API interceptor configured with options:', {
    hasGetAccessToken: typeof options.getAccessToken === 'function',
    hasOnLogout: typeof options.onLogout === 'function',
    hasNavigate: typeof options.navigate === 'function',
  });
};
