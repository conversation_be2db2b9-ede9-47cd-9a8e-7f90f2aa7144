{"chat_direction_left_to_right": "valaminek itt kell lennie. üres volt", "chat_direction_right_to_left": "valaminek itt kell lennie. üres volt", "com_a11y_ai_composing": "Az AI még mindig írja a választ.", "com_a11y_end": "Az AI befejezte a válaszát.", "com_a11y_start": "Az AI megkezdte a válaszát.", "com_agents_allow_editing": "Engedélyezze más felhasználóknak az ügynök szerkesztését", "com_agents_by_librechat": "a LibreChat által", "com_agents_code_interpreter": "Ha engedélyezve van, az ügynök használhatja a LibreChat Kódértelmező API-t a generált kód futtatására, beleértve a fájlfeldolgozást is, biztonságosan. Ehhez érvényes API-kulcs szükséges.", "com_agents_code_interpreter_title": "Kódértelmező API", "com_agents_create_error": "Hiba történt az ügynök létrehozása során.", "com_agents_description_placeholder": "Opcionális: Itt írja le az ügynökét", "com_agents_enable_file_search": "Fájlkeresés engedélyezése", "com_agents_file_context": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (OCR)", "com_agents_file_context_disabled": "Az ügynököt először létre kell hozni, miel<PERSON>tt fájlokat tölthet fel a fájlkontextushoz.", "com_agents_file_context_info": "A „Kontextusként” feltöltött fájlokat OCR-rel dolgozzuk fel a szöveg kinyeréséhez, amelyet aztán az ügynök utasításaihoz adunk. Ideális dokumentumokhoz, szöveges képekhez vagy PDF-ekhez, ahol a teljes szövegtartalomra szükség van.", "com_agents_file_search_disabled": "Az ügynököt először létre kell hozni, miel<PERSON>tt fájlokat tölthet fel a fájlkereséshez.", "com_agents_file_search_info": "Ha engedély<PERSON><PERSON> van, az ügynök értesül az alább felsorolt pontos fájlnevekről, lehetőv<PERSON> téve s<PERSON>, hogy releváns kontextust szerezzen ezekből a fájlokból.", "com_agents_instructions_placeholder": "Az ügynök által használt rendszerutasítások", "com_agents_missing_provider_model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> szolgáltatót és modellt az ügynök létrehozása előtt.", "com_agents_name_placeholder": "Opcionális: <PERSON>z ügynök neve", "com_agents_no_access": "<PERSON><PERSON><PERSON>ga ennek az ügynöknek a szerkesztéséhez.", "com_agents_not_available": "Az ügynök nem érhető el", "com_agents_search_name": "Ügynökök keresése név szerint", "com_agents_update_error": "Hiba történt az ügynök frissítése során.", "com_assistants_action_attempt": "<PERSON>z asszisztens beszélni akar {{0}}-val", "com_assistants_actions": "Műveletek", "com_assistants_actions_disabled": "Először létre kell hoznia egy asszisztenst, mi<PERSON><PERSON>tt műveleteket adhatna hozzá.", "com_assistants_actions_info": "Engedélyezze az asszisztens számára, hogy információkat szerezzen be vagy műveleteket végezzen API-kon keresztül", "com_assistants_add_actions": "Műveletek hozzáadása", "com_assistants_add_tools": "Eszközök hozzáadása", "com_assistants_allow_sites_you_trust": "Csak megbízható webhelyeket engedélyezzen.", "com_assistants_append_date": "Aktuális dátum és idő hozzáfűzése", "com_assistants_append_date_tooltip": "Ha engedélyez<PERSON> van, az aktuális kliens dátuma és ideje hozzáfűzésre kerül az asszisztens rendszerutasításaihoz.", "com_assistants_attempt_info": "Az asszisztens a következőket szeretné elküldeni:", "com_assistants_available_actions": "Elérhető műveletek", "com_assistants_capabilities": "Képességek", "com_assistants_code_interpreter": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_assistants_code_interpreter_files": "Az alábbi fájlok kizárólag a kódértelmezőhöz tartoznak:", "com_assistants_code_interpreter_info": "A kódértelmező lehetővé teszi az asszisztens számára, hogy kódot írjon és futtasson. Ez az eszköz különböző adatokat és formátumokat tartalmazó fájlokat képes feldolgozni, valamint fájlokat, például grafikonokat generálni.", "com_assistants_completed_action": "<PERSON><PERSON><PERSON><PERSON> {{0}}-val", "com_assistants_completed_function": "Futtatta: {{0}}", "com_assistants_conversation_starters": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_assistants_conversation_starters_placeholder": "Adjon meg egy beszé<PERSON>í<PERSON>", "com_assistants_create_error": "Hiba történt az asszisztens létrehozása során.", "com_assistants_create_success": "<PERSON><PERSON><PERSON><PERSON>", "com_assistants_delete_actions_error": "Hiba történt a művelet törlése során.", "com_assistants_delete_actions_success": "A művelet sikeresen törölve az asszisztensből", "com_assistants_description_placeholder": "Opcionális: Itt írja le az asszisztenst", "com_assistants_domain_info": "Az asszisztens ezt az információt küldte el {{0}}-nak", "com_assistants_file_search": "Fájlkeresés", "com_assistants_file_search_info": "A fájlkeresés lehetővé teszi az asszisztens számára, hogy tudást szerezzen az Ön vagy felhasználói által feltöltött fájlokból. A fájl feltöltése után az asszisztens automatikusan eldönti, mikor kell tartalmat lekérni a felhasználói kérések alapján. A vektortárolók csatolása a fájlkereséshez még nem támogatott. Ezeket a Provider Playgroundból csatolhatja, vagy üzenetekhez csatolhat fájlokat a szál alapú kereséshez.", "com_assistants_function_use": "<PERSON>z asszisztens használta: {{0}}", "com_assistants_image_vision": "<PERSON><PERSON><PERSON>", "com_assistants_instructions_placeholder": "Az asszisztens által használt rendszerutasítások", "com_assistants_knowledge": "<PERSON><PERSON><PERSON>", "com_assistants_knowledge_disabled": "Az asszisztenst először létre kell hozni, és a kódértelmezőt vagy a lekérdezést engedélyezni és menteni kell, mielőtt fájlokat tölthetne fel tudásként.", "com_assistants_knowledge_info": "Ha fájlokat tölt fel a <PERSON><PERSON>á, az asszisztenssel folytatott beszélgetések tartalmazhatják a fájlok tartalmát.", "com_assistants_max_starters_reached": "Elérte a beszélgetésindítók maximális számát", "com_assistants_name_placeholder": "Opcionális: <PERSON><PERSON> ass<PERSON> neve", "com_assistants_non_retrieval_model": "A fájlkeresés nem engedélyezett ezen a modellen. <PERSON><PERSON><PERSON><PERSON><PERSON>k, válasszon másik modellt.", "com_assistants_retrieval": "Lekérdezés", "com_assistants_running_action": "Művelet futtatása", "com_assistants_search_name": "Asszisztensek keresése név szerint", "com_assistants_update_actions_error": "Hiba történt a művelet létrehozása vagy frissítése során.", "com_assistants_update_actions_success": "A művelet sikeresen létrehozva vagy frissítve", "com_assistants_update_error": "Hiba történt az asszisztens frissítése során.", "com_assistants_update_success": "<PERSON><PERSON><PERSON>n frissítve", "com_auth_already_have_account": "<PERSON><PERSON><PERSON>?", "com_auth_apple_login": "Bejelentkezés Apple-lel", "com_auth_back_to_login": "Vissza a bejelentkezéshez", "com_auth_click": "<PERSON><PERSON><PERSON><PERSON>", "com_auth_click_here": "Kat<PERSON><PERSON>on ide", "com_auth_continue": "Folytatás", "com_auth_create_account": "Fiók létrehozása", "com_auth_discord_login": "Folytat<PERSON>", "com_auth_email": "E-mail", "com_auth_email_address": "E-mail cím", "com_auth_email_max_length": "Az e-mail nem lehet hosszabb 120 karakternél", "com_auth_email_min_length": "Az e-mailnek legalább 6 karakterből kell állnia", "com_auth_email_pattern": "Érvényes e-mail címet kell megadnia", "com_auth_email_required": "E-mail megadása kötelező", "com_auth_email_resend_link": "E-mail újraküldése", "com_auth_email_resent_failed": "<PERSON><PERSON>jra elküldeni az ellenőrző e-mailt", "com_auth_email_resent_success": "<PERSON>z ellenőrző e-mail sikeresen újra elküldve", "com_auth_email_verification_failed": "Az e-mail ellenőrzése sikertelen", "com_auth_email_verification_failed_token_missing": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> a token", "com_auth_email_verification_in_progress": "E-mail ellenőrzése f<PERSON>, k<PERSON>r<PERSON><PERSON>k, várjon", "com_auth_email_verification_invalid": "Érvénytelen e-mail ellenőrzés", "com_auth_email_verification_redirecting": "Átirányí<PERSON>ás {{0}} másodpercen belül...", "com_auth_email_verification_resend_prompt": "Nem kapta meg az e-mailt?", "com_auth_email_verification_success": "E-mail sikeresen ellenőrizve", "com_auth_email_verifying_ellipsis": "Ellenőrz<PERSON>...", "com_auth_error_create": "Hiba történt a fiók regisztrálása során. K<PERSON>rj<PERSON>k, prób<PERSON><PERSON><PERSON>.", "com_auth_error_invalid_reset_token": "Ez a jelszó-visszaállítási token már nem érvényes.", "com_auth_error_login": "<PERSON>em si<PERSON> bejelentkezni az megadott adatokkal. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ellenő<PERSON>ze az adatokat és próbálja újra.", "com_auth_error_login_ban": "Fiókja ideiglenesen tiltva lett a szolgáltatásunk megsértése miatt.", "com_auth_error_login_rl": "Túl sok bejelentkezési kísérlet rövid id<PERSON> belül. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, próbá<PERSON>ja <PERSON> k<PERSON>.", "com_auth_error_login_server": "Belső szerverhiba történt. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON><PERSON><PERSON>, majd pr<PERSON><PERSON><PERSON><PERSON><PERSON>.", "com_auth_error_login_unverified": "Fiókja még nincs ellenőriz<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ellenőrizze e-mailjét az ellenőrző linkért.", "com_auth_facebook_login": "Folytatás Facebookkal", "com_auth_full_name": "Teljes név", "com_auth_github_login": "Folytatás GitHubbal", "com_auth_google_login": "Folytatás Google-lel", "com_auth_here": "ITT", "com_auth_login": "Bejelentkezés", "com_auth_login_with_new_password": "Most már beje<PERSON>t az új j<PERSON>.", "com_auth_name_max_length": "A név nem lehet hosszabb 80 karakternél", "com_auth_name_min_length": "A névnek legalább 3 karakterből kell állnia", "com_auth_name_required": "Név megadása kötelező", "com_auth_no_account": "<PERSON>nc<PERSON> még fi<PERSON>?", "com_auth_password": "Je<PERSON><PERSON><PERSON>", "com_auth_password_confirm": "<PERSON><PERSON><PERSON>ó megerősítése", "com_auth_password_forgot": "Elfelejtette a j<PERSON>zavát?", "com_auth_password_max_length": "A jelszó nem lehet hosszabb 128 karakternél", "com_auth_password_min_length": "A jelszónak legalább 8 karakterből kell állnia", "com_auth_password_not_match": "A jelszavak nem egyeznek", "com_auth_password_required": "Jelszó megadása kötelező", "com_auth_registration_success_generic": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, el<PERSON>őrizze e-mailjét az e-mail cím ellenőrzéséhez.", "com_auth_registration_success_insecure": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>.", "com_auth_reset_password": "Je<PERSON><PERSON><PERSON> v<PERSON>zaállítása", "com_auth_reset_password_if_email_exists": "Ha létezik fiók ezzel az e-mail címmel, egy e-mailt küldtünk a jelszó-visszaállítási utasításokkal. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, el<PERSON><PERSON><PERSON><PERSON> a spam mappáját is.", "com_auth_reset_password_link_sent": "E-mail elküldve", "com_auth_reset_password_success": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>zaállítása si<PERSON>", "com_auth_sign_in": "Bejelentkezés", "com_auth_sign_up": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_submit_registration": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_to_reset_your_password": "a jelszó visszaállításához.", "com_auth_to_try_again": "az újrapróbálkozáshoz.", "com_auth_two_factor": "Ellenőrizze kedvenc egyszeri jelszó alkalmazását egy kódért", "com_auth_username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ónév (opcionális)", "com_auth_username_max_length": "A felhasználónév nem lehet hosszabb 20 karakternél", "com_auth_username_min_length": "A felhasználónévnek legalább 2 karakterből kell állnia", "com_auth_verify_your_identity": "Azonosság ellenőrzése", "com_auth_welcome_back": "Üdvözöljük újra", "com_click_to_download": "(kattintson ide a letöltéshez)", "com_download_expired": "(a letöltés lejárt)", "com_download_expires": "(katti<PERSON>on ide a letöltéshez - lejár: {{0}})", "com_endpoint": "<PERSON><PERSON>g<PERSON>", "com_endpoint_agent": "Ügynök", "com_endpoint_agent_model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Ajánlott: GPT-3.5)", "com_endpoint_agent_placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, válasszon egy ügynököt", "com_endpoint_ai": "AI", "com_endpoint_anthropic_maxoutputtokens": "A válaszban generálható tokenek maximális száma. Adjon meg alacsonyabb értéket rövidebb válaszokhoz és magasabb értéket hosszabb válaszokhoz. Megjegyzés: a model<PERSON><PERSON>nak, miel<PERSON><PERSON> elérnék ezt a maximumot.", "com_endpoint_anthropic_prompt_cache": "A prompt gyorsítótárazása lehetővé teszi nagy kontextus vagy utasítások újrafelhasználását az API-hívások során, csökkentve a költségeket és a késleltetést", "com_endpoint_anthropic_temp": "0-tól 1-ig terjed. Használjon 0-hoz közelebbi értéket analitikus / feleletválasztós feladatokhoz, és 1-hez közelebbit kreatív és generatív feladatokhoz. Javasoljuk ennek vagy a Top P-nek a változtatását, de ne mindkettőt.", "com_endpoint_anthropic_thinking": "Belső érvelést tesz lehetővé a támogatott Claude model<PERSON> (3.7 Sonnet). Megjegyzés: ehhez a „Gondolkodási költségvetés” beállítása szükséges, és kisebbnek kell lennie, mint a „Maximális kimeneti tokenek”", "com_endpoint_anthropic_thinking_budget": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ho<PERSON> <PERSON> h<PERSON> tokent használhat maximum a belső érvelési folyamatához. Nagyobb költségvetés javíthatja a válasz minőségét azáltal, hogy alaposabb elemzést tesz lehetővé komplex problémákhoz, b<PERSON><PERSON> nem <PERSON>nül használja fel az összes kiosztott költségvetést, különösen 32K feletti tartományokban. Ez a beállítás kisebb kell legyen, mint a „Maximális kimeneti tokenek.”", "com_endpoint_anthropic_topk": "A Top-k megváltoztatja, hogyan választja ki a modell a kimeneti tokeneket. A Top-k 1 azt jelenti, hogy a kiválasztott token a legvalószínűbb az összes token közül a modell szókincsében (ezt nevezik mohó dekódolásnak is), míg a Top-k 3 azt jelenti, hogy a következő token a 3 legvalószínűbb token közül kerül kiválasztásra (hőmérséklet használatával).", "com_endpoint_anthropic_topp": "A Top-p megváltoztatja, hogyan választja ki a modell a kimeneti tokeneket. A tokenek a legvalószínűbb K (lásd a topK paramétert) közül kerülnek kiválasztásra a legkevésbé valószínűig, amíg az összegük eléri a Top-p értéket.", "com_endpoint_assistant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_assistant_model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_assistant_placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON> egy asszisztenst a jobb oldali panelről", "com_endpoint_completion": "Befejezés", "com_endpoint_completion_model": "Befejezési modell (Ajánlott: GPT-4)", "com_endpoint_config_click_here": "Kat<PERSON><PERSON>on ide", "com_endpoint_config_google_api_info": "A Generatív Nyelvi API kulcs megszerzéséhez (Geminihez),", "com_endpoint_config_google_api_key": "Google API kulcs", "com_endpoint_config_google_cloud_platform": "(a Google Cloud Platformról)", "com_endpoint_config_google_gemini_api": "(Gemini API)", "com_endpoint_config_google_service_key": "Google Szolgáltatási Fiók Kulcs", "com_endpoint_config_key": "API kulcs beállítása", "com_endpoint_config_key_encryption": "A kulcs titkosítva lesz és törlésre kerül ekkor:", "com_endpoint_config_key_for": "API kulcs beállítása ehhez:", "com_endpoint_config_key_google_need_to": "Szükséges", "com_endpoint_config_key_google_service_account": "Szolgáltatási fiók létrehozása", "com_endpoint_config_key_google_vertex_ai": "Vertex AI engedélyezése", "com_endpoint_config_key_google_vertex_api": "API a Google Cloudon, majd", "com_endpoint_config_key_google_vertex_api_role": "Ügyel<PERSON>n a<PERSON>, hogy a 'Létrehozás és folytatás' gombra kattintva legalább a 'Vertex AI Felhasználó' szerepkört adja meg. Végül hozzon létre egy JSON kulcsot, amelyet itt importálhat.", "com_endpoint_config_key_import_json_key": "Szolgáltatási fiók JSON kulcs importálása.", "com_endpoint_config_key_import_json_key_invalid": "Érvénytelen szolgáltatási fiók JSON kulcs, a megfelelő fájlt importálta?", "com_endpoint_config_key_import_json_key_success": "Szolgáltatási fiók JSON kulcs sikeresen importálva", "com_endpoint_config_key_name": "<PERSON><PERSON><PERSON>", "com_endpoint_config_key_never_expires": "A kulcs soha nem jár le", "com_endpoint_config_placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> be a kulcsot a fejléc menüben a csevegéshez.", "com_endpoint_config_value": "Érték megadása ehhez:", "com_endpoint_context": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_context_info": "A kontextushoz használható tokenek maximális száma. <PERSON><PERSON><PERSON>hat<PERSON>, hogy hány token kerül elküldésre kérésenként. Ha nincs megadva, a rendszer az ismert modellek kontextusmérete alapján alapértelmezett értékeket használ. Magasabb értékek beállítása hibákat és/vagy magasabb tokenköltséget eredményezhet.", "com_endpoint_context_tokens": "<PERSON><PERSON><PERSON> k<PERSON>", "com_endpoint_custom_name": "Egyéni név", "com_endpoint_default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_default_blank": "alapértelmezett: üres", "com_endpoint_default_empty": "alapértelmezett: üres", "com_endpoint_default_with_num": "alapé<PERSON><PERSON><PERSON>ett: {{0}}", "com_endpoint_deprecated": "<PERSON><PERSON><PERSON>", "com_endpoint_deprecated_info": "Ez a végpont elavult, és a jövőbeli verziókban eltávolításra kerülhet, k<PERSON><PERSON><PERSON><PERSON><PERSON>, hasz<PERSON><PERSON><PERSON> he<PERSON> az ügynök végpontot", "com_endpoint_deprecated_info_a11y": "A bővítmény végpont elavult, és a jövőbeli verziókban eltávolításra kerülhet, k<PERSON><PERSON><PERSON><PERSON><PERSON>, haszná<PERSON><PERSON> helyette az ügynök végpontot", "com_endpoint_examples": " <PERSON><PERSON><PERSON>", "com_endpoint_export": "Exportálás", "com_endpoint_export_share": "Exportálás/Megosztás", "com_endpoint_frequency_penalty": "Gyakor<PERSON><PERSON><PERSON> bü<PERSON>s", "com_endpoint_func_hover": "A bővítmények OpenAI függvényekként való hasz<PERSON>ának engedélyezése", "com_endpoint_google_custom_name_placeholder": "<PERSON>jon egyedi nevet a Google-nak", "com_endpoint_google_maxoutputtokens": "A válaszban generálható tokenek maximális száma. Adjon meg alacsonyabb értéket rövidebb válaszokhoz és magasabb értéket hosszabb válaszokhoz. Megjegyzés: a model<PERSON><PERSON>nak, miel<PERSON><PERSON> elérnék ezt a maximumot.", "com_endpoint_google_temp": "Magasabb értékek = véletlenszerűbb, alacsonyabb értékek = fókuszáltabb és determinisztikusabb. Javasoljuk ennek vagy a Top P-nek a változtatását, de ne mindkettőt.", "com_endpoint_google_topk": "A Top-k megváltoztatja, hogyan választja ki a modell a kimeneti tokeneket. A Top-k 1 azt jelenti, hogy a kiválasztott token a legvalószínűbb az összes token közül a modell szókincsében (ezt nevezik mohó dekódolásnak is), míg a Top-k 3 azt jelenti, hogy a következő token a 3 legvalószínűbb token közül kerül kiválasztásra (hőmérséklet használatával).", "com_endpoint_google_topp": "A Top-p megváltoztatja, hogyan választja ki a modell a kimeneti tokeneket. A tokenek a legvalószínűbb K (lásd a topK paramétert) közül kerülnek kiválasztásra a legkevésbé valószínűig, amíg az összegük eléri a Top-p értéket.", "com_endpoint_instructions_assistants": "Utasítások felülírása", "com_endpoint_instructions_assistants_placeholder": "Felülírja az asszisztens utasításait. Ez hasznos az egyedi futások során történő viselkedés módosításához.", "com_endpoint_max_output_tokens": "<PERSON><PERSON><PERSON> k<PERSON>", "com_endpoint_message": "Üzenet", "com_endpoint_message_new": "Üzenet {{0}}", "com_endpoint_message_not_appendable": "Szerkessze az üzenetet vagy generálja új<PERSON>.", "com_endpoint_my_preset": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_no_presets": "Még nincsenek előre be<PERSON>, használja a beállítások gombot egy létrehozásához", "com_endpoint_open_menu": "<PERSON><PERSON>", "com_endpoint_openai_custom_name_placeholder": "Adjon egyedi nevet az AI-nak", "com_endpoint_openai_detail": "A látási kérések felbontása. Az „Alacsony” olcsóbb és gyorsabb, a „Magas” részletesebb és drág<PERSON>bb, az „Automatikus” pedig a két lehetőség közül választ a kép felbontása alapján.", "com_endpoint_openai_freq": "-2.0 és 2.0 közötti szám. A pozitív értékek büntetik az új tokeneket az eddigi szövegben való meglévő gyakoriságuk alapján, csökkentve a modell azon valószínűségét, hogy ugyanazt a sort szó szerint megismételje.", "com_endpoint_openai_max": "A generálható tokenek maximális száma. A bemeneti és generált tokenek teljes hossza a modell kontextushosszúságával <PERSON>l<PERSON>.", "com_endpoint_openai_max_tokens": "Opcionális 'max_tokens' me<PERSON><PERSON>, amely a csevegés befejezésében generálható tokenek maximális számát jelenti. A bemeneti és generált tokenek teljes hossza a modell kontextushosszával korlátozott. Hibák léphetnek fel, ha ez a szám meghaladja a maximális kontextus tokeneket.", "com_endpoint_openai_pres": "-2.0 és 2.0 közötti szám. A pozitív értékek büntetik az új tokeneket az eddigi szövegben való megjelenésük alapján, növelve a modell azon v<PERSON>, hogy új témák<PERSON>ó<PERSON>.", "com_endpoint_openai_prompt_prefix_placeholder": "Adjon meg egyedi utasításokat a rendszerüzenetbe való belefoglaláshoz. Alapértelmezett: nincs", "com_endpoint_openai_reasoning_effort": "Csak o1 modellek: korlátozza az érvelési erőfeszítést az érvelési modelleknél. Az érvelési erőfeszítés csökkentése gyorsabb válaszokat és kevesebb tokenhasználatot eredményezhet az érvelésben egy válaszon belül.", "com_endpoint_openai_resend": "Az összes korábban csatolt kép újraküldése. Megjegyzés: ez jelentősen növelheti a tokenköltséget, és hibák léphetnek fel sok képi csatolmány esetén.", "com_endpoint_openai_resend_files": "Az összes korábban csatolt fájl újraküldése. Megjegyzés: ez növeli a tokenköltséget, és hibák léphetnek fel sok csatolmány esetén.", "com_endpoint_openai_stop": "Legfeljebb 4 olyan soro<PERSON>t, ahol az API leállítja a további tokenek generálását.", "com_endpoint_openai_temp": "Magasabb értékek = véletlenszerűbb, alacsonyabb értékek = fókuszáltabb és determinisztikusabb. Javasoljuk ennek vagy a Top P-nek a változtatását, de ne mindkettőt.", "com_endpoint_openai_topp": "A hőmérséklettel való mintavételezés alternatívája, az úgynevezett nukleusz mintavételezés, ahol a modell a top_p valószínűségi tömeggel rendelkező tokenek eredményeit veszi figyelembe. Tehát 0.1 azt jelenti, hogy csak a felső 10% valószínűségi tömeget alkotó tokenek kerülnek figyelembe vételre. Javasoljuk ennek vagy a hőmérsékletnek a változtatását, de ne mindkettőt.", "com_endpoint_output": "<PERSON><PERSON><PERSON>", "com_endpoint_plug_image_detail": "<PERSON><PERSON><PERSON>", "com_endpoint_plug_resend_files": "Fájlok újraküldése", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Adjon meg egyedi utasításokat a rendszerüzenetbe való belefoglaláshoz. Alapértelmezett: nincs", "com_endpoint_plug_skip_completion": "Befejezés kihagyása", "com_endpoint_plug_use_functions": "Függvények használata", "com_endpoint_presence_penalty": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_preset": "<PERSON><PERSON><PERSON>", "com_endpoint_preset_custom_name_placeholder": "valaminek itt kell lennie. üres volt", "com_endpoint_preset_default": "mostantól az alapértelmezett előre be<PERSON>.", "com_endpoint_preset_default_item": "Alapértelmezett:", "com_endpoint_preset_default_none": "<PERSON><PERSON><PERSON> aktív alapértelmezett előre be<PERSON>.", "com_endpoint_preset_default_removed": "már nem az alapértelmezett előre be<PERSON>ll<PERSON>.", "com_endpoint_preset_delete_confirm": "Biztosan törölni szeretné ezt az előre beállítottat?", "com_endpoint_preset_delete_error": "Hiba történt az előre beállított törlése során. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, pr<PERSON><PERSON><PERSON><PERSON><PERSON>.", "com_endpoint_preset_import": "Előre beállított importálva!", "com_endpoint_preset_import_error": "Hiba történt az előre beállított importálása során. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, prób<PERSON><PERSON><PERSON>.", "com_endpoint_preset_name": "<PERSON><PERSON><PERSON> neve", "com_endpoint_preset_save_error": "Hiba történt az előre beállított mentése során. K<PERSON><PERSON><PERSON><PERSON><PERSON>, pró<PERSON><PERSON><PERSON><PERSON>.", "com_endpoint_preset_selected": "Előre beállított aktiválva!", "com_endpoint_preset_selected_title": "Aktív!", "com_endpoint_preset_title": "<PERSON><PERSON><PERSON>", "com_endpoint_presets": "<PERSON><PERSON><PERSON>", "com_endpoint_presets_clear_warning": "Biztosan törölni szeretné az összes előre beállítottat? Ez visszafordíthatatlan.", "com_endpoint_prompt_cache": "Prompt gyo<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_prompt_prefix": "Egyéni u<PERSON>í<PERSON>ok", "com_endpoint_prompt_prefix_assistants": "További utasítások", "com_endpoint_prompt_prefix_assistants_placeholder": "Adjon meg további utasításokat vagy kontextust az asszisztens fő utasításaihoz. Üresen hagyva figyelmen kívül marad.", "com_endpoint_prompt_prefix_placeholder": "Adjon meg egyedi utasításokat vagy kontextust. Üresen hagyva figyelmen kívül marad.", "com_endpoint_reasoning_effort": "Érvelési <PERSON>", "com_endpoint_save_as_preset": "<PERSON><PERSON><PERSON>", "com_endpoint_search": "Végpont keresése név szerint", "com_endpoint_search_endpoint_models": "{{0}} modellek keresése...", "com_endpoint_search_models": "Modellek keresése...", "com_endpoint_search_var": "{{0}} keresése...", "com_endpoint_set_custom_name": "Adjon meg egyedi nevet, hogy megtalálhassa ezt az előre beállítottat", "com_endpoint_skip_hover": "A befejezési lépés kihagyásának engedélyezése, amely <PERSON>tte<PERSON> a végső választ és a generált lépéseket", "com_endpoint_stop": "Leállási sorozatok", "com_endpoint_stop_placeholder": "Az értékeket az `Enter` megnyomásával különítse el", "com_endpoint_temperature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_thinking": "Gondolkod<PERSON>", "com_endpoint_thinking_budget": "Gondolkodási költségvetés", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "Aktív asszisztens használata", "com_error_expired_user_key": "A megadott kulcs {{0}}-hoz lejárt {{1}}-k<PERSON><PERSON>, adjon meg egy új kul<PERSON>, és próbá<PERSON>ja <PERSON>.", "com_error_files_dupe": "Duplikált fájl észlelve.", "com_error_files_empty": "Üres fájlok nem engedélyezettek.", "com_error_files_process": "Hiba történt a fájl feldolgozása során.", "com_error_files_unsupported_capability": "<PERSON><PERSON><PERSON> en<PERSON><PERSON><PERSON><PERSON> o<PERSON>, amely tá<PERSON> ezt a fájltípust.", "com_error_files_upload": "Hiba történt a fájl feltöltése során.", "com_error_files_upload_canceled": "A fájlfeltöltési kérés megszakítva. Megjegyzés: a fájlfeltöltés még folyamatban lehet, és manuális<PERSON> kell törölni.", "com_error_files_validation": "Hiba történt a fájl ellenőrzése során.", "com_error_input_length": "A legutóbbi üzenet token sz<PERSON><PERSON> túl ho<PERSON>, megh<PERSON><PERSON><PERSON> a tokenkorlátot, vagy a tokenkorlát paraméterei rosszul vannak konfigu<PERSON><PERSON><PERSON>, ami h<PERSON><PERSON><PERSON><PERSON><PERSON> befoly<PERSON> a kontextusablakot. Tov<PERSON><PERSON>i információ: {{0}}. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON><PERSON><PERSON> le az üzenetét, <PERSON><PERSON><PERSON><PERSON><PERSON> be a maximális kontextusméretet a beszélgetési paraméterekből, vagy ágazza el a beszélgetést a folytatáshoz.", "com_error_invalid_agent_provider": "A „{{0}}” szolgáltató nem érhető el az ügynökökkel való használatra. Kérjük, lépjen az ügynök beállításaihoz, és válasszon egy jelenleg elérhető szolgáltatót.", "com_error_invalid_user_key": "Érvénytelen kulcs került megadásra. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy érvényes kulcsot, és próbálja újra.", "com_error_moderation": "<PERSON><PERSON>, a benyújtott tartalmat moder<PERSON><PERSON><PERSON> rendszerünk jelölte meg, mert nem felel meg közösségi irányelveinknek. Nem tudunk továbblépni ezzel a konkrét témával. Ha további kérdései vagy más témákban szeretne be<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, szer<PERSON>sz<PERSON> az üzenetét, vagy hozzon l<PERSON>tre új be<PERSON>.", "com_error_no_base_url": "<PERSON><PERSON> alap URL. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg e<PERSON>, és próbá<PERSON>ja <PERSON>.", "com_error_no_user_key": "<PERSON><PERSON> k<PERSON>. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adjon meg egy kul<PERSON>, és próbá<PERSON><PERSON>.", "com_files_filter": "Fájlok szűrése...", "com_files_no_results": "<PERSON><PERSON><PERSON>.", "com_files_number_selected": "{{0}} / {{1}} elem <PERSON>t<PERSON>", "com_files_table": "valaminek itt kell lennie. üres volt", "com_generated_files": "<PERSON><PERSON><PERSON>lt f<PERSON>jlo<PERSON>:", "com_hide_examples": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_2fa": "Kétfaktoros hitelesítés (2FA)", "com_nav_account_settings": "Fiókbeállítások", "com_nav_always_make_prod": "Mindig készítse el az új verziókat élesben", "com_nav_archive_created_at": "Archiválás <PERSON>", "com_nav_archive_name": "Név", "com_nav_archived_chats": "Archivált cseve<PERSON>k", "com_nav_at_command": "@-<PERSON>ncs", "com_nav_at_command_description": "„@” parancs vált<PERSON>a a végpontok, <PERSON><PERSON><PERSON>, el<PERSON><PERSON> be<PERSON>llítottak stb. váltásához", "com_nav_audio_play_error": "<PERSON>ba az audió le<PERSON>zása során: {{0}}", "com_nav_audio_process_error": "Hiba az audió feldolgozása során: {{0}}", "com_nav_auto_scroll": "Automatikus görgetés a legutóbbi üzenetre a csevegés megnyitásakor", "com_nav_auto_send_prompts": "Promptok automatikus küldése", "com_nav_auto_send_text": "Szöveg automatikus küldése", "com_nav_auto_send_text_disabled": "-1 beállítása a letiltáshoz", "com_nav_auto_transcribe_audio": "Audió automatikus átírása", "com_nav_automatic_playback": "Legutóbbi üzenet automatikus lejátszása", "com_nav_balance": "<PERSON>gy<PERSON><PERSON>", "com_nav_browser": "Böngésző", "com_nav_center_chat_input": "Csevegőbevitel középre helyezése az üdvözlőképernyőn", "com_nav_change_picture": "<PERSON><PERSON><PERSON>", "com_nav_chat_commands": "Csevegőparancsok", "com_nav_chat_commands_info": "Ezek a parancsok az üzenet elején megadott speciális karakterekkel aktiválhatók. Minden parancs a kijelölt előtaggal indul. Letilthatja őket, ha gyakran használja ezeket a karaktereket üzenetek kezdetén.", "com_nav_chat_direction": "Csevegés i<PERSON>án<PERSON>", "com_nav_clear_all_chats": "Összes csevegés törlése", "com_nav_clear_cache_confirm_message": "Biztosan törölni szeretné a gyorsítótárat?", "com_nav_clear_conversation": "Beszélgetések törlése", "com_nav_clear_conversation_confirm_message": "Biztosan törölni szeretné az összes beszélgetést? Ez visszafordíthatatlan.", "com_nav_close_sidebar": "Oldalsáv bezárás<PERSON>", "com_nav_commands": "Parancsok", "com_nav_confirm_clear": "Törlés megerősítése", "com_nav_conversation_mode": "Beszélge<PERSON><PERSON> mód", "com_nav_convo_menu_options": "Beszélgetési menü opciók", "com_nav_db_sensitivity": "Decibel érzékenység", "com_nav_delete_account": "Fiók törlése", "com_nav_delete_account_button": "Fiókom végleges törlése", "com_nav_delete_account_confirm": "Fiók törlése - biztos ben<PERSON>?", "com_nav_delete_account_email_placeholder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, adja meg fiókja e-mail címét", "com_nav_delete_cache_storage": "TTS gyorsítótár tárolásának törlése", "com_nav_delete_data_info": "Minden adata törlés<PERSON> kerü<PERSON>.", "com_nav_delete_warning": "FIGYELEM: Ez véglegesen törli fiókj<PERSON>t.", "com_nav_enable_cache_tts": "TTS gyorsítótár engedélyezése", "com_nav_enable_cloud_browser_voice": "Felhőalapú hangok használata", "com_nav_enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_engine": "Motor", "com_nav_enter_to_send": "Üzenetek küldése az Enter megnyomásával", "com_nav_export": "Exportálás", "com_nav_export_all_message_branches": "Összes üzenetág exportálása", "com_nav_export_conversation": "Beszélgetés exportálása", "com_nav_export_filename": "Fájlnév", "com_nav_export_filename_placeholder": "Adja meg a fájlnevet", "com_nav_export_include_endpoint_options": "Végpont opciók belefoglalása", "com_nav_export_recursive": "Re<PERSON><PERSON><PERSON><PERSON>", "com_nav_export_recursive_or_sequential": "Re<PERSON>rzív vagy sze<PERSON>venciális?", "com_nav_export_type": "<PERSON><PERSON><PERSON>", "com_nav_external": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_font_size": "Üzenet betűmérete", "com_nav_font_size_base": "Közepes", "com_nav_font_size_lg": "<PERSON><PERSON>", "com_nav_font_size_sm": "<PERSON><PERSON><PERSON>", "com_nav_font_size_xl": "Extra nagy", "com_nav_font_size_xs": "Extra kicsi", "com_nav_help_faq": "Segítség és GYIK", "com_nav_hide_panel": "Legjobb oldalsó panel elrejtése", "com_nav_info_code_artifacts": "Engedélyezi a kísérleti kód műtermékek megjelenítését a csevegés mellett", "com_nav_info_code_artifacts_agent": "Engedélyezi a kód műtermékek használatát ehhez az ügynökhöz. Alapértelmezés szerint a műtermékek használatára vonatkozó további utasítások hozzáadódnak, kiv<PERSON><PERSON>, ha az „Egyéni prompt mód” engedélyez<PERSON> van.", "com_nav_info_custom_prompt_mode": "<PERSON> engedély<PERSON><PERSON> van, az alapértelmezett műtermék rendszerprompt nem kerül belefoglalásra. Ebben a módban minden műtermék-generáló utasítást manuálisan kell megadni.", "com_nav_info_enter_to_send": "Ha engedély<PERSON><PERSON> van, az `ENTER` megnyomása elküldi az üzenetet. Ha letiltva van, az Enter új sort ad, és a `CTRL + ENTER` / `⌘ + ENTER` szükséges az üzenet elküldéséhez.", "com_nav_info_fork_change_default": "„Csak látható üzenetek” csak a kiválasztott üzenethez vezető közvetlen utat tartalmazza. „Kapcsolódó ágak belefoglalása” az útvonal menti ágakat is hozzáadja. „Mindent innen/ide belefoglalása” minden kapcsolódó üzenetet és ágat tartalmaz.", "com_nav_info_fork_split_target_setting": "Ha engedélyezve van, az elágazás a célüzenettől a beszélgetés legutóbbi üzenetéig kezdődik, a kiválasztott viselkedés szerint.", "com_nav_info_include_shadcnui": "Ha engedélyezve van, a shadcn/ui komponensek használatára vonatkozó utasítások belefoglalásra kerülnek. A shadcn/ui egy újrahasználható komponensek gyűjteménye, amelyet Radix UI és Tailwind CSS segítségével építettek. Megjegyzés: ezek hosszú utasítások, csak akkor engedélyezze, ha fontos az LLM-nek a helyes importok és komponensek megadása. További információ ezekről a komponensekről: https://ui.shadcn.com/", "com_nav_info_latex_parsing": "Ha engedélyezve van, az üzenetekben lévő LaTeX kód matematikai egyenletekként kerül megjelenítésre. Ennek letiltása javíthatja a teljesítményt, ha nincs szüksége LaTeX megjelenítésre.", "com_nav_info_save_badges_state": "Ha engedélyezve van, a csevegőjelvények állapota mentésre kerül. <PERSON>z azt jelenti, hogy ha új csevegést hoz létre, a jelvények ugyanabban az állapotban maradnak, mint az előző csevegésben. Ha letiltja ezt az opciót, a jelvények minden új csevegés létrehozásakor visszaállnak az alapértelmezett állapotukra.", "com_nav_info_save_draft": "Ha engedélyezve van, a csevegő űrlapban megadott szöveg és csatolmányok automatikusan helyben piszkozatként mentésre kerülnek. Ezek a piszkozatok akkor is elérhetők maradnak, ha újratölti az oldalt vagy másik beszélgetésre vált. A piszkozatok helyben tárolódnak az eszközén, és törlődnek, mi<PERSON><PERSON> az üzenet elküldésre kerül.", "com_nav_info_show_thinking": "Ha engedélyezve van, a csevegés alapértelmezés szerint nyitott gondolkodási legördülő menüket jelenít meg, lehetővé téve az AI érvelésének valós idejű megtekintését. Ha letiltva van, a gondolkodási legördülő menük alapértelmezés szerint zárva maradnak egy letisztultabb és egyszerűbb felület érdekében.", "com_nav_info_user_name_display": "Ha engedélyez<PERSON> van, a küldő felhasználóneve megjelenik minden elküldött üzenet felett. Ha letiltva van, csak „Ön” jelenik meg az üzenetei felett.", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "Automat<PERSON><PERSON> feli<PERSON>", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_chinese": "中文", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_hungarian": "<PERSON><PERSON><PERSON>", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_persian": "فار<PERSON>ی", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "Nyelv", "com_nav_latex_parsing": "LaTeX elemzése üzenetekben (hatással lehet a teljesítményre)", "com_nav_log_out": "Kijelentkezés", "com_nav_long_audio_warning": "Hosszabb szövegek feldolgozása több időt vehet igénybe.", "com_nav_maximize_chat_space": "Csevegőterület maximalizálása", "com_nav_modular_chat": "Végpontok váltásának engedélyezése beszélgetés közben", "com_nav_my_files": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_not_supported": "<PERSON><PERSON>", "com_nav_open_sidebar": "Oldalsáv me<PERSON>", "com_nav_playback_rate": "<PERSON><PERSON> le<PERSON>zási sebess<PERSON>g", "com_nav_plugin_auth_error": "Hiba történt a bővítmény hitelesítése során. Kérjük, prób<PERSON><PERSON><PERSON>.", "com_nav_plugin_install": "Telepítés", "com_nav_plugin_search": "Bővítmények keresése", "com_nav_plugin_store": "Bővítménybolt", "com_nav_plugin_uninstall": "Eltávolítás", "com_nav_plus_command": "+-<PERSON><PERSON><PERSON>", "com_nav_plus_command_description": "„+” parancs váltása a többszörös válasz beállítás hozzáadásához", "com_nav_profile_picture": "Profilkép", "com_nav_save_badges_state": "Jelvények állapotának mentése", "com_nav_save_drafts": "Piszkozatok helyi mentése", "com_nav_scroll_button": "Görgetés a végére gomb", "com_nav_search_placeholder": "Üzenetek keresése", "com_nav_send_message": "Üzenet küldése", "com_nav_setting_account": "<PERSON>ók", "com_nav_setting_chat": "Csevegés", "com_nav_setting_data": "Adatvezérlők", "com_nav_setting_general": "<PERSON><PERSON>lán<PERSON>", "com_nav_setting_speech": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_settings": "Beállítások", "com_nav_shared_links": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_show_code": "Mindig mutassa a kódot a kódértelmező használatakor", "com_nav_show_thinking": "Gondolkodási legördülők alapértelmezett megnyitása", "com_nav_slash_command": "/-<PERSON><PERSON><PERSON>", "com_nav_slash_command_description": "„/” parancs v<PERSON>a a prompt billentyűzettel történő kiválasztásához", "com_nav_speech_to_text": "Beszédből szöveg", "com_nav_stop_generating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_text_to_speech": "Szövegbő<PERSON> be<PERSON>éd", "com_nav_theme": "<PERSON><PERSON><PERSON>", "com_nav_theme_dark": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_theme_light": "<PERSON>il<PERSON><PERSON>", "com_nav_theme_system": "Rendszer", "com_nav_tool_dialog": "Asszisztens eszközök", "com_nav_tool_dialog_agents": "Ügynök eszközök", "com_nav_tool_dialog_description": "Az asszisztenst menteni kell az eszközválasztások megőrzéséhez.", "com_nav_tool_remove": "Eltávolítás", "com_nav_tool_search": "Eszközök keresése", "com_nav_user": "FELHASZNÁLÓ", "com_nav_user_msg_markdown": "Felhasználói üzenetek markdownként való megjelenítése", "com_nav_user_name_display": "Felhasználónév megjelenítése az üzenetekben", "com_nav_voice_select": "Hang", "com_show_agent_settings": "Ügynökbeállítások megjelenítése", "com_show_completion_settings": "Befejezési beállítások megjelenítése", "com_show_examples": "<PERSON><PERSON><PERSON><PERSON>", "com_sidepanel_agent_builder": "Ügynöképítő", "com_sidepanel_assistant_builder": "Asszisztensépítő", "com_sidepanel_attach_files": "Fájlok csatolása", "com_sidepanel_conversation_tags": "Könyvjelzők", "com_sidepanel_hide_panel": "Panel elrejtése", "com_sidepanel_manage_files": "Fájlok kezelése", "com_sidepanel_parameters": "Paraméterek", "com_ui_2fa_account_security": "A kétfaktoros hitelesítés további biztonsági réteget ad fiókjához", "com_ui_2fa_disable": "2FA letiltása", "com_ui_2fa_disable_error": "Hiba történt a kétfaktoros hitelesítés letiltása során", "com_ui_2fa_disabled": "A 2FA letiltva", "com_ui_2fa_enable": "2FA engedélyezése", "com_ui_2fa_enabled": "A 2FA engedélyezve", "com_ui_2fa_generate_error": "Hiba történt a kétfaktoros hitelesítési beállítások generálása során", "com_ui_2fa_invalid": "Érvénytelen kétfaktoros hitelesítési kód", "com_ui_2fa_setup": "2FA beállítása", "com_ui_2fa_verified": "A kétfaktoros hitelesítés sikeresen ellenőrizve", "com_ui_accept": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_add": "Hozzáadás", "com_ui_add_model_preset": "<PERSON><PERSON> vagy el<PERSON><PERSON> be<PERSON>tt hozzáadása további válaszhoz", "com_ui_add_multi_conversation": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_admin": "Admin", "com_ui_admin_access_warning": "Az adminisztrátori hozzáférés letiltása ehhez a funkcióhoz váratlan kezelőfelületi problémákat okozhat, amelyek frissítést igényelnek. Ha mentésre kerül, a visszaállítás egyetlen módja a librechat.yaml konfigurációs fájlban található interfész beállítás, amely minden szerepkörre hatással van.", "com_ui_admin_settings": "Adminisztrátori be<PERSON>ll<PERSON>", "com_ui_advanced": "<PERSON><PERSON><PERSON>", "com_ui_advanced_settings": "<PERSON><PERSON><PERSON>", "com_ui_agent": "Ügynök", "com_ui_agent_chain": "Ügynöklánc (Ügynökök keveréke)", "com_ui_agent_chain_info": "Lehetővé teszi ügynökök sorozatának létrehozását. Minden ügynök hozzáférhet az előző ügynökök kimeneteihez a láncban. Az „Ügynökök keveréke” architektú<PERSON><PERSON> al<PERSON>, ahol az ügynökök az előző kimeneteket segédinformációként használják.", "com_ui_agent_chain_max": "Elérte a maximum {{0}} ügynököt.", "com_ui_agent_delete_error": "Hiba történt az ügynök törlése során", "com_ui_agent_deleted": "Ügynök sikeresen törölve", "com_ui_agent_duplicate_error": "Hiba történt az ügynök duplikálása során", "com_ui_agent_duplicated": "Ügynök sikeresen duplikálva", "com_ui_agent_editing_allowed": "Más felhasználók már szerkeszthetik ezt az ügynököt", "com_ui_agent_recursion_limit": "<PERSON><PERSON><PERSON>lépés<PERSON>", "com_ui_agent_recursion_limit_info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, hogy az ügynök hány lépést tehet egy futás során, miel<PERSON>tt végső választ adna. Alapértelmezett érték 25 lépés. Egy lépés lehet egy AI API kérés vagy egy eszközhasználati kör. Például egy alapvető eszközinterakció 3 lépést igényel: kezdeti kéré<PERSON>, eszközhasználat és követő kérés.", "com_ui_agent_shared_to_all": "valaminek itt kell lennie. üres volt", "com_ui_agent_var": "{{0}} ügynök", "com_ui_agents": "Ügynökök", "com_ui_agents_allow_create": "Ügynökök létrehozásának engedélyezése", "com_ui_agents_allow_share_global": "Ügynökök megosztásának engedélyezése minden felhasználóval", "com_ui_agents_allow_use": "Ügynökök használatának engedélyezése", "com_ui_all": "mind", "com_ui_all_proper": "Mind", "com_ui_analyzing": "Elemzés", "com_ui_analyzing_finished": "Elemzés befejezve", "com_ui_api_key": "API kulcs", "com_ui_archive": "Archiválás", "com_ui_archive_error": "<PERSON><PERSON> archiválni a beszélgetést", "com_ui_artifact_click": "Kattintson a megnyitáshoz", "com_ui_artifacts": "Műtermékek", "com_ui_artifacts_toggle": "Műtermék kezelőfelület váltása", "com_ui_artifacts_toggle_agent": "Műtermékek engedélyezése", "com_ui_ascending": "Növekvő", "com_ui_assistant": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_assistant_delete_error": "Hiba történt az asszisztens törlése során", "com_ui_assistant_deleted": "Assz<PERSON>z<PERSON> sikeresen törölve", "com_ui_assistants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_assistants_output": "Asszisztensek kimenete", "com_ui_attach_error": "<PERSON>em le<PERSON>t fájlt csatolni. Hozzon létre vagy v<PERSON>zon egy be<PERSON>, vagy pr<PERSON>bá<PERSON>ja meg frissíteni az oldalt.", "com_ui_attach_error_openai": "<PERSON><PERSON> le<PERSON>t asszisztens fájlokat csatolni más végpontokhoz", "com_ui_attach_error_size": "Fájlméret korlát túllépve a végponthoz:", "com_ui_attach_error_type": "<PERSON>em t<PERSON> fájltípus a végponthoz:", "com_ui_attach_remove": "Fájl eltávolítása", "com_ui_attach_warn_endpoint": "A nem asszisztens fájlokat figyelmen kívül hagyhatják kompatibilis eszköz nélkül", "com_ui_attachment": "Csatolmány", "com_ui_auth_type": "Hitelesítési típus", "com_ui_auth_url": "Engedélyezési URL", "com_ui_authentication": "Hitelesítés", "com_ui_authentication_type": "Hitelesítési típus", "com_ui_avatar": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_azure": "Azure", "com_ui_back_to_chat": "Vissza a csevegéshez", "com_ui_back_to_prompts": "Vissza a promptokhoz", "com_ui_backup_codes": "Biztonsági menté<PERSON> kódok", "com_ui_backup_codes_regenerate_error": "Hiba történt a biztonsági mentési kódok újragenerálása során", "com_ui_backup_codes_regenerated": "A biztonsági mentési kódok sikeresen újragenerálva", "com_ui_basic": "Alap", "com_ui_basic_auth_header": "Alapvető engedélyezési fejléc", "com_ui_bearer": "Bearer", "com_ui_bookmark_delete_confirm": "Biztosan törölni szeretné ezt a könyvjelzőt?", "com_ui_bookmarks": "Könyvjelzők", "com_ui_bookmarks_add": "Könyvjelzők hozzáadása", "com_ui_bookmarks_add_to_conversation": "Hozzáadás az aktuális <PERSON>", "com_ui_bookmarks_count": "Szám", "com_ui_bookmarks_create_error": "Hiba történt a könyvjelző létrehozása során", "com_ui_bookmarks_create_exists": "Ez a könyvjelző már létezik", "com_ui_bookmarks_create_success": "Könyvjelző sikeresen létrehozva", "com_ui_bookmarks_delete": "Könyvjelző törlése", "com_ui_bookmarks_delete_error": "Hiba történt a könyvjelző törlése során", "com_ui_bookmarks_delete_success": "Könyvjelző sikeresen törölve", "com_ui_bookmarks_description": "Le<PERSON><PERSON><PERSON>", "com_ui_bookmarks_edit": "Könyvjelző szerkesztése", "com_ui_bookmarks_filter": "Könyvjelzők szűrése...", "com_ui_bookmarks_new": "Új könyvjelző", "com_ui_bookmarks_title": "Cím", "com_ui_bookmarks_update_error": "Hiba történt a könyvjelző frissítése során", "com_ui_bookmarks_update_success": "Könyvjelző sikeresen frissítve", "com_ui_bulk_delete_error": "<PERSON><PERSON> törölni a megosztott linkeket", "com_ui_callback_url": "Visszahívási URL", "com_ui_cancel": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_chat": "Csevegés", "com_ui_chat_history": "Csevegési előzmények", "com_ui_clear": "Törlés", "com_ui_clear_all": "Mind törlése", "com_ui_client_id": "<PERSON><PERSON><PERSON>", "com_ui_client_secret": "<PERSON><PERSON><PERSON> t<PERSON>", "com_ui_close": "Bezárás", "com_ui_close_menu": "<PERSON><PERSON>", "com_ui_code": "<PERSON><PERSON><PERSON>", "com_ui_collapse_chat": "Csevegés összecsukása", "com_ui_command_placeholder": "Opcionális: <PERSON><PERSON> meg egy parancsot a prompt <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, vagy a név kerül <PERSON>", "com_ui_command_usage_placeholder": "Válasszon promptot parancs vagy név szerint", "com_ui_complete_setup": "Beállí<PERSON><PERSON> be<PERSON>", "com_ui_confirm_action": "Művelet megerősítése", "com_ui_confirm_admin_use_change": "Ennek a beállításnak a megváltoztatása blokkolja az adminisztrátorok, köztük az Ön hozzáférését is. Biztosan folytatni szeretné?", "com_ui_confirm_change": "Változtatás megerősítése", "com_ui_context": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_continue": "Folytatás", "com_ui_controls": "Vezérlők", "com_ui_copied": "Másolva!", "com_ui_copied_to_clipboard": "Vágólapra másolva", "com_ui_copy_code": "<PERSON><PERSON><PERSON>", "com_ui_copy_link": "<PERSON>", "com_ui_copy_to_clipboard": "Másolás a vágólapra", "com_ui_create": "Létrehozás", "com_ui_create_link": "<PERSON>", "com_ui_create_prompt": "Prompt létrehozása", "com_ui_currently_production": "<PERSON><PERSON><PERSON>", "com_ui_custom": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_custom_header_name": "<PERSON><PERSON><PERSON><PERSON> f<PERSON> neve", "com_ui_custom_prompt_mode": "<PERSON><PERSON><PERSON><PERSON> prompt mód", "com_ui_dashboard": "Irányítópult", "com_ui_date": "<PERSON><PERSON><PERSON>", "com_ui_date_april": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_august": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_december": "December", "com_ui_date_february": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_january": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_july": "<PERSON><PERSON><PERSON>", "com_ui_date_june": "<PERSON><PERSON><PERSON>", "com_ui_date_march": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_may": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_november": "November", "com_ui_date_october": "Október", "com_ui_date_previous_30_days": "Előző 30 nap", "com_ui_date_previous_7_days": "Előző 7 nap", "com_ui_date_september": "Szeptember", "com_ui_date_today": "Ma", "com_ui_date_yesterday": "Tegnap", "com_ui_decline": "<PERSON><PERSON> el", "com_ui_default_post_request": "Alap<PERSON><PERSON><PERSON><PERSON>ett (POST kérés)", "com_ui_delete": "Törlés", "com_ui_delete_action": "Művelet törlése", "com_ui_delete_action_confirm": "Biztosan törölni szeretné ezt a műveletet?", "com_ui_delete_agent_confirm": "Biztosan törölni szeretné ezt az ügynököt?", "com_ui_delete_assistant_confirm": "Biztosan törölni szeretné ezt az asszisztenst? Ez nem von<PERSON>ó v<PERSON>za.", "com_ui_delete_confirm": "<PERSON><PERSON> törli", "com_ui_delete_confirm_prompt_version_var": "Ez törli a kiválasztott verziót a „{{0}}” számára. Ha nem léteznek más verziók, a prompt törl<PERSON><PERSON> kerül.", "com_ui_delete_conversation": "Csevegés törlése?", "com_ui_delete_prompt": "Prompt törlése?", "com_ui_delete_shared_link": "Megosztott link törlése?", "com_ui_delete_tool": "Eszköz törlése", "com_ui_delete_tool_confirm": "Biztosan törölni szeretné ezt az eszközt?", "com_ui_descending": "Csökkenő", "com_ui_description": "Le<PERSON><PERSON><PERSON>", "com_ui_description_placeholder": "Opcionális: <PERSON><PERSON> meg egy le<PERSON> a prompt megjelení<PERSON>s<PERSON>", "com_ui_disabling": "Letiltás...", "com_ui_download": "Letöltés", "com_ui_download_artifact": "Műtermék letöltése", "com_ui_download_backup": "Biztonsági mentési kódok letöltése", "com_ui_download_backup_tooltip": "<PERSON><PERSON><PERSON><PERSON>, tölts<PERSON> le a biztonsági mentési kódjait. Szüksége lesz rájuk, ha <PERSON>zíti hitelesítő eszközét", "com_ui_download_error": "Hiba a fájl letöltése során. <PERSON><PERSON><PERSON>, hogy a fájl törölve lett.", "com_ui_drag_drop": "valaminek itt kell lennie. üres volt", "com_ui_dropdown_variables": "Legördülő változók:", "com_ui_dropdown_variables_info": "Hozzon létre egyedi legördülő menüket a promptokhoz: `{{változó_név:opció1|opció2|opció3}}`", "com_ui_duplicate": "Duplikálás", "com_ui_duplication_error": "Hiba történt a beszélgetés duplikálása során", "com_ui_duplication_processing": "Beszélgetés duplikálása...", "com_ui_duplication_success": "Besz<PERSON>lge<PERSON>s si<PERSON>", "com_ui_edit": "Szerkesztés", "com_ui_empty_category": "-", "com_ui_endpoint": "<PERSON><PERSON>g<PERSON>", "com_ui_endpoint_menu": "LLM végpont menü", "com_ui_enter": "Enter", "com_ui_enter_api_key": "API kulcs megadása", "com_ui_enter_openapi_schema": "Adja meg itt az OpenAPI sémáját", "com_ui_error": "Hiba", "com_ui_error_connection": "Hiba a szerverhez való csatlak<PERSON>kor, próbálja meg frissíteni az oldalt.", "com_ui_error_save_admin_settings": "Hiba történt az adminisztrátori beállítások mentése során.", "com_ui_examples": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_expand_chat": "Csevegés k<PERSON>", "com_ui_export_convo_modal": "Beszélgetés exportálási ablak", "com_ui_field_required": "Ez a mező kötelező", "com_ui_filter_prompts": "Promptok szűrése", "com_ui_filter_prompts_name": "Promptok szűrése név szerint", "com_ui_finance": "Pénzügy", "com_ui_fork": "Elágazás", "com_ui_fork_all_target": "Mindent innen/ide belefoglalása", "com_ui_fork_branches": "Kapcsolódó ágak belefoglalása", "com_ui_fork_change_default": "Alapértelmezett elágazási opció", "com_ui_fork_default": "Alapértelmezett elágazási opció használata", "com_ui_fork_error": "Hiba történt a beszélgetés elágazása során", "com_ui_fork_from_message": "Válasszon elágazási opciót", "com_ui_fork_info_1": "Ezzel a beállítással a kívánt viselkedés szerint ágazhatja el az üzeneteket.", "com_ui_fork_info_2": "Az „elágazás” egy új beszélgetés létrehozását jele<PERSON>, amely az aktuális beszélgetés specifikus üzeneteitől indul vagy végződik, a kiválasztott opciók szerint másolatot készítve.", "com_ui_fork_info_3": "A „célüzenet” arra az üzenetre utal, amelyből ez az ablak megnyílt, vagy ha bejelöli a „{{0}}” opciót, a beszélgetés legutóbbi üzenetére.", "com_ui_fork_info_branches": "Ez az opció elágazza a látható üzeneteket, valamint a kapcsolódó ágakat; másképpen fogalmazva, a célüzenethez vezető közvetlen utat, beleértve az útvonal menti ágakat.", "com_ui_fork_info_remember": "<PERSON><PERSON><PERSON><PERSON><PERSON> be <PERSON>t, hogy megjegyezze a kiválasztott opciókat a jövőbeli hasz<PERSON>z, <PERSON><PERSON> gyorsabban elágaztathatja a beszélgetéseket a kívánt módon.", "com_ui_fork_info_start": "Ha bejelöli, az elágazás ebből az üzenetből a beszélgetés legutóbbi üzenetéig kezdődik, a fent kiválasztott viselkedés szerint.", "com_ui_fork_info_target": "Ez az opció elágazza az összes üzenetet a célüzenetig, beleértve annak <PERSON>édait; máské<PERSON>n <PERSON>almazva, minden üzenetágat, függ<PERSON><PERSON><PERSON><PERSON> attól, hogy látható-e vagy ugyanazon az útvonalon van-e, belefoglal.", "com_ui_fork_info_visible": "Ez az opció csak a látható üzeneteket ágazza el; másképpen fogalmazva, a célüzenethez vezető közvetlen utat, ágak nélkül.", "com_ui_fork_processing": "Beszélgetés elágazása...", "com_ui_fork_remember": "Megjegyzés", "com_ui_fork_remember_checked": "A választása a használat után megjegyzésre kerül. Ezt bármikor megváltoztathatja a beállításokban.", "com_ui_fork_split_target": "Elágazás innen kezdése", "com_ui_fork_split_target_setting": "Elágazás alapértelmezés szerint a célüzenettől kezdése", "com_ui_fork_success": "Besz<PERSON><PERSON><PERSON>s si<PERSON>", "com_ui_fork_visible": "Csak látható üzenetek", "com_ui_generate_backup": "Biztonsági mentési kódok generálása", "com_ui_generate_qrcode": "QR-k<PERSON>d <PERSON>", "com_ui_generating": "Generálás...", "com_ui_global_group": "valaminek itt kell lennie. üres volt", "com_ui_go_back": "<PERSON><PERSON><PERSON>", "com_ui_go_to_conversation": "Ugrás a beszélgetéshez", "com_ui_good_afternoon": "<PERSON><PERSON>", "com_ui_good_evening": "<PERSON><PERSON>", "com_ui_good_morning": "<PERSON><PERSON>", "com_ui_happy_birthday": "Ez az 1. születésnapom!", "com_ui_hide_qr": "QR-<PERSON><PERSON><PERSON>", "com_ui_host": "Hoszt", "com_ui_idea": "Ötletek", "com_ui_image_gen": "Képgenerá<PERSON>ás", "com_ui_import": "Importálás", "com_ui_import_conversation_error": "Hiba történt a beszélgetések importálása során", "com_ui_import_conversation_file_type_error": "Nem tá<PERSON>ott import típus", "com_ui_import_conversation_info": "Beszélgetések importálása JSON fájlból", "com_ui_import_conversation_success": "Beszélgetések sikeresen importálva", "com_ui_include_shadcnui": "shadcn/ui komponensek utasításainak belefoglalása", "com_ui_input": "Bevitel", "com_ui_instructions": "Utasítások", "com_ui_late_night": "<PERSON><PERSON><PERSON>", "com_ui_latest_footer": "Minden AI mindenkinek.", "com_ui_latest_production_version": "Legfrissebb <PERSON> verz<PERSON>", "com_ui_latest_version": "<PERSON>g<PERSON><PERSON><PERSON><PERSON> verzi<PERSON>", "com_ui_librechat_code_api_key": "Szerezze meg a LibreChat Kódértelmező API kulcsát", "com_ui_librechat_code_api_subtitle": "Biztonságos. Többnyelvű. Be-/Kimeneti fájlok.", "com_ui_librechat_code_api_title": "AI kód futtatása", "com_ui_loading": "Betöltés...", "com_ui_locked": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_logo": "{{0}} Logó", "com_ui_manage": "Kezelés", "com_ui_max_tags": "A megengedett maximális szám {{0}}, a legutóbbi értékek használata.", "com_ui_mention": "<PERSON><PERSON><PERSON><PERSON>en meg egy v<PERSON>, ass<PERSON><PERSON><PERSON>t vagy el<PERSON>re be<PERSON>llítottat a gyors váltáshoz", "com_ui_min_tags": "<PERSON>em t<PERSON>lí<PERSON>ó el több é<PERSON>k, minimum {{0}} szükséges.", "com_ui_misc": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_model": "<PERSON><PERSON>", "com_ui_model_parameters": "<PERSON>lpar<PERSON><PERSON><PERSON><PERSON>", "com_ui_more_info": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_my_prompts": "<PERSON><PERSON><PERSON><PERSON> prompt<PERSON><PERSON>", "com_ui_name": "Név", "com_ui_new": "<PERSON><PERSON>", "com_ui_new_chat": "<PERSON><PERSON>", "com_ui_next": "Következő", "com_ui_no": "Nem", "com_ui_no_backup_codes": "Nincsenek elérhető biztonsági mentési kódok. Kérjük, generáljon újakat", "com_ui_no_bookmarks": "<PERSON><PERSON>, még nincsenek könyvjelzői. Kattintson egy csevegésre, és adjon hozzá egy újat", "com_ui_no_category": "<PERSON><PERSON><PERSON>", "com_ui_no_changes": "Nincsenek frissítendő változtatások", "com_ui_no_data": "valaminek itt kell lennie. üres volt", "com_ui_no_terms_content": "<PERSON>ncs megjeleníthető használati feltételek tartalom", "com_ui_no_valid_items": "valaminek itt kell lennie. üres volt", "com_ui_none": "<PERSON><PERSON><PERSON>", "com_ui_not_used": "<PERSON><PERSON>", "com_ui_nothing_found": "<PERSON><PERSON>i sem tal<PERSON>", "com_ui_oauth": "OAuth", "com_ui_of": "ból/ből", "com_ui_off": "<PERSON>", "com_ui_on": "Be", "com_ui_openai": "OpenAI", "com_ui_page": "<PERSON><PERSON>", "com_ui_prev": "Előző", "com_ui_preview": "Előnézet", "com_ui_privacy_policy": "Adatvédelmi <PERSON>", "com_ui_privacy_policy_url": "Adatvédelmi irányelvek URL", "com_ui_prompt": "Prompt", "com_ui_prompt_already_shared_to_all": "Ez a prompt már meg van osztva minden fel<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_prompt_name": "Prompt neve", "com_ui_prompt_name_required": "A prompt neve k<PERSON>", "com_ui_prompt_preview_not_shared": "A szerző nem engedélyezte az együttműködést ehhez a prompthoz.", "com_ui_prompt_text": "Szöveg", "com_ui_prompt_text_required": "Szöveg megadása kötelező", "com_ui_prompt_update_error": "<PERSON><PERSON> tört<PERSON>t a prompt friss<PERSON><PERSON><PERSON> során", "com_ui_prompts": "Promptok", "com_ui_prompts_allow_create": "Promptok létrehozásának engedélyezése", "com_ui_prompts_allow_share_global": "Promptok megosztásának engedélyezése minden felhasználóval", "com_ui_prompts_allow_use": "Promptok használatának engedélyezése", "com_ui_provider": "S<PERSON>lgáltató", "com_ui_read_aloud": "Fe<PERSON>lvas<PERSON>", "com_ui_redirecting_to_provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{0}}-ra, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, várjon...", "com_ui_refresh_link": "<PERSON>", "com_ui_regenerate": "Újragenerálás", "com_ui_regenerate_backup": "Biztonsági mentési kódok újragenerálása", "com_ui_regenerating": "Újragenerálás...", "com_ui_region": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_rename": "Átnevezés", "com_ui_rename_prompt": "Prompt átnevezése", "com_ui_requires_auth": "Hitelesítést igényel", "com_ui_reset_var": "{{0}} visszaállítása", "com_ui_result": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_revoke": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_revoke_info": "Minden felhasz<PERSON><PERSON><PERSON> megadott hitelesítő adat v<PERSON>zavonása", "com_ui_revoke_key_confirm": "Biztosan vissza szeretné vonni ezt a kulcsot?", "com_ui_revoke_key_endpoint": "Kulcs visszavonása {{0}}-hoz", "com_ui_revoke_keys": "Kulcsok visszavonása", "com_ui_revoke_keys_confirm": "Biztosan vissza szeretné vonni az összes kulcsot?", "com_ui_role_select": "Szerep", "com_ui_roleplay": "Szerepjáték", "com_ui_run_code": "<PERSON><PERSON><PERSON>", "com_ui_run_code_error": "Hiba történt a kód futtatása során", "com_ui_save": "Men<PERSON>s", "com_ui_save_badge_changes": "Jelvényváltoztatások mentése?", "com_ui_save_submit": "Mentés és beküldés", "com_ui_saved": "Mentve!", "com_ui_schema": "<PERSON><PERSON><PERSON>", "com_ui_scope": "Hatókör", "com_ui_search": "Keresés", "com_ui_secret_key": "Titkos k<PERSON>", "com_ui_select": "Kiválasztás", "com_ui_select_file": "Fájl kiválasztása", "com_ui_select_model": "<PERSON><PERSON>lasztás<PERSON>", "com_ui_select_provider": "Szolgáltató kiválasztása", "com_ui_select_provider_first": "Először válasszon szolgáltatót", "com_ui_select_region": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_select_search_model": "Modell keresése név szerint", "com_ui_select_search_plugin": "Bővítmény keresése név szerint", "com_ui_select_search_provider": "Szolgáltató keresése név szerint", "com_ui_select_search_region": "Régió kere<PERSON>ése név szerint", "com_ui_share": "Megosztás", "com_ui_share_create_message": "Neve és a megosztás után hozzáadott üzenetei privátak maradnak.", "com_ui_share_delete_error": "Hiba történt a megosztott link törlése során", "com_ui_share_error": "Hiba történt a csevegőlink megosztása során", "com_ui_share_form_description": "valaminek itt kell lennie. üres volt", "com_ui_share_link_to_chat": "Link megosztása a csevegéshez", "com_ui_share_to_all_users": "Megosztás minden felhasz<PERSON>óval", "com_ui_share_update_message": "<PERSON><PERSON>, egyedi utasításai és a megosztás után hozzáadott üzenetei privátak maradnak.", "com_ui_share_var": "{{0}} megosztása", "com_ui_shared_link_bulk_delete_success": "Megosztott linkek sikeresen törölve", "com_ui_shared_link_delete_success": "Megosztott link sikeresen törölve", "com_ui_shared_link_not_found": "Megosztott link nem található", "com_ui_shared_prompts": "<PERSON><PERSON><PERSON><PERSON><PERSON> promptok", "com_ui_shop": "Vásárlás", "com_ui_show": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_show_all": "Összes megjelenítése", "com_ui_show_qr": "QR-k<PERSON>d <PERSON>", "com_ui_sign_in_to_domain": "Bejelentkezés {{0}}-ra", "com_ui_simple": "Egyszerű", "com_ui_size": "<PERSON><PERSON><PERSON>", "com_ui_special_variables": "Speciális változók:", "com_ui_speech_while_submitting": "<PERSON><PERSON> le<PERSON>t be<PERSON>, amíg válasz generálódik", "com_ui_stop": "Le<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_storage": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_submit": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_teach_or_explain": "<PERSON><PERSON><PERSON>", "com_ui_temporary": "Ideiglenes cseve<PERSON>s", "com_ui_terms_and_conditions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_terms_of_service": "Szolgáltatási feltételek", "com_ui_thinking": "Gondolkodás...", "com_ui_thoughts": "Gondolatok", "com_ui_token_exchange_method": "Token csere mó<PERSON>", "com_ui_token_url": "Token URL", "com_ui_tools": "Eszközök", "com_ui_travel": "Utazás", "com_ui_unarchive": "Archiválás v<PERSON>", "com_ui_unarchive_error": "<PERSON><PERSON> v<PERSON>vonni a beszélgetés archiválását", "com_ui_unknown": "Ismeretlen", "com_ui_update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_upload": "Feltöltés", "com_ui_upload_code_files": "Feltöltés a kódértelmezőhöz", "com_ui_upload_delay": "A „{{0}}” feltöltése több időt vesz igénybe, mint vártuk. Kérjük, várjon, amíg a fájl indexelése befejeződik a lekérdezéshez.", "com_ui_upload_error": "Hiba történt a fájl feltöltése során", "com_ui_upload_file_context": "Fájlk<PERSON><PERSON><PERSON>", "com_ui_upload_file_search": "Feltöltés fájlkereséshez", "com_ui_upload_files": "Fájlok feltöltése", "com_ui_upload_image": "<PERSON><PERSON><PERSON>", "com_ui_upload_image_input": "<PERSON><PERSON><PERSON>", "com_ui_upload_invalid": "Érvénytelen fájl feltöltéshez. Képnek kell lennie, amely nem haladja meg a korlátot", "com_ui_upload_invalid_var": "Érvénytelen fájl feltöltéshez. Képnek kell lennie, amely nem haladja meg a {{0}} MB-ot", "com_ui_upload_ocr_text": "Feltöltés szövegként", "com_ui_upload_success": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_upload_type": "Feltöltési típus kiválasztása", "com_ui_use_2fa_code": "2FA kód használata helyette", "com_ui_use_backup_code": "Biztonsági men<PERSON> kód has<PERSON> he<PERSON>", "com_ui_use_micrphone": "Mik<PERSON><PERSON><PERSON>", "com_ui_used": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_variables": "Változók", "com_ui_variables_info": "Használjon dupla kapcsos zárójeleket a szövegben változók létrehozásához, pl. `{{példa változ<PERSON>}}`, amelyeket k<PERSON> a prompt hasz<PERSON>latakor kitölthet.", "com_ui_verify": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_version_var": "{{0}} verz<PERSON><PERSON>", "com_ui_versions": "Verziók", "com_ui_view_source": "<PERSON><PERSON><PERSON> csevegés megtekintése", "com_ui_weekend_morning": "<PERSON><PERSON><PERSON> h<PERSON>tvé<PERSON>", "com_ui_write": "<PERSON><PERSON><PERSON>", "com_ui_yes": "Igen", "com_ui_zoom": "Zoom", "com_user_message": "<PERSON><PERSON>"}