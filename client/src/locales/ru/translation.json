{"com_a11y_ai_composing": "ИИ продолжает составлять ответ", "com_a11y_end": "ИИ закончил свой ответ", "com_a11y_start": "ИИ начал отвечать", "com_agents_allow_editing": "Разрешить другим пользователям редактировать вашего агента", "com_agents_by_librechat": "от LibreChat", "com_agents_code_interpreter": "При включении позволяет агенту использовать API интерпретатора кода LibreChat для безопасного выполнения сгенерированного кода, включая обработку файлов. Требуется действующий ключ API.", "com_agents_code_interpreter_title": "API Интерпретатора кода", "com_agents_create_error": "Произошла ошибка при создании вашего агента", "com_agents_description_placeholder": "Необязательно: описание вашего агента", "com_agents_enable_file_search": "Включить поиск файлов", "com_agents_file_context": "Контекст файла (OCR)", "com_agents_file_context_disabled": "Агент должен быть создан перед загрузкой файлов для контекста файла", "com_agents_file_context_info": "Файлы, загруженные как «Контекст», обрабатываются с использованием OCR для извлечения текста, который затем добавляется в инструкции агента. Идеально подходит для документов, изображений с текстом или PDF-файлов, где требуется полный текстовый контент.", "com_agents_file_search_disabled": "Для загрузки файлов в Поиск необходимо сначала создать агента", "com_agents_file_search_info": "При включении агент получит доступ к точным названиям файлов, перечисленным ниже, что позволит ему извлекать из них релевантный контекст.", "com_agents_instructions_placeholder": "Системные инструкции, используемые агентом", "com_agents_missing_provider_model": "Выберите провайдера и модель перед созданием агента", "com_agents_name_placeholder": "Необязательно: имя агента", "com_agents_no_access": "У вас нет прав для редактирования этого агента", "com_agents_not_available": "Агент недоступен", "com_agents_search_name": "Поиск агентов по имени", "com_agents_update_error": "Произошла ошибка при обновлении вашего агента.", "com_assistants_action_attempt": "Ассистент хочет поговорить с {{0}}", "com_assistants_actions": "Действия", "com_assistants_actions_disabled": "Вам нужно сохранить ассистента, прежде чем добавлять Actions.", "com_assistants_actions_info": "Позвольте вашему ассистенту получать информацию или выполнять действия через API", "com_assistants_add_actions": "Добавить действия", "com_assistants_add_tools": "Добавить инструменты", "com_assistants_allow_sites_you_trust": "Разрешайте только сайты, которым доверяете.", "com_assistants_append_date": "Добавить текущую дату и время", "com_assistants_append_date_tooltip": "Когда включено, текущая дата и время клиента будут добавлены к инструкциям системы Ассистента.", "com_assistants_attempt_info": "Ассистент хочет отправить следующее:", "com_assistants_available_actions": "Доступные действия", "com_assistants_capabilities": "Возможности", "com_assistants_code_interpreter": "Интерпретатор кода", "com_assistants_code_interpreter_files": "Следующие файлы доступны только для Интерпретатора кода:", "com_assistants_code_interpreter_info": "Интерпретатор кода позволяет ассистенту создавать и выполнять код. Этот инструмент может обрабатывать файлы с различными данными и форматами, а также создавать файлы, например графики.", "com_assistants_completed_action": "Об<PERSON>а<PERSON><PERSON>я с {{0}}", "com_assistants_completed_function": "Выполнено: {{0}}", "com_assistants_conversation_starters": "Примеры запросов", "com_assistants_conversation_starters_placeholder": "Введите начальную фразу для разговора", "com_assistants_create_error": "Произошла ошибка при сохранении вашего ассистента.", "com_assistants_create_success": "Успешно сохранено", "com_assistants_delete_actions_error": "Произошла ошибка при удалении действия.", "com_assistants_delete_actions_success": "Действие успешно удалено из ассистента", "com_assistants_description_placeholder": "Необязательно: описание вашего ассистента", "com_assistants_domain_info": "Ассистент отправил эту информацию {{0}}", "com_assistants_file_search": "Поиск файлов", "com_assistants_file_search_info": "Прикрепление векторных хранилищ для Поиска по файлам пока не поддерживается. Вы можете прикрепить их из Песочницы провайдера или прикрепить файлы к сообщениям для поиска по файлам в отдельных диалогах.", "com_assistants_function_use": "Ассистент использовал {{0}}", "com_assistants_image_vision": "Ана<PERSON>из изображений", "com_assistants_instructions_placeholder": "Системные инструкции, которые использует ассистент", "com_assistants_knowledge": "База знаний", "com_assistants_knowledge_disabled": "Ассистент должен быть сохранён, и Интерпретатор кода (Code Interpreter) или Поиск (Retrieval) должны быть включены и сохранены перед загрузкой файлов к Базе Знаний.", "com_assistants_knowledge_info": "Если вы загрузите файлы в раздел Знания, чаты с вашим ассистентом могут включать содержимое файлов.", "com_assistants_max_starters_reached": "Достигнуто максимальное количество начальных подсказок", "com_assistants_name_placeholder": "Необязательно: имя ассистента", "com_assistants_non_retrieval_model": "Поиск по файлам недоступен для этой модели. Пожалуйста, выберите другую модель.", "com_assistants_retrieval": "Поиск (Retrieval)", "com_assistants_running_action": "Выполняется действие", "com_assistants_search_name": "Поиск ассистентов по имени", "com_assistants_update_actions_error": "Произошла ошибка при создании или обновлении действия.", "com_assistants_update_actions_success": "Действие успешно создано или обновлено", "com_assistants_update_error": "Произошла ошибка при обновлении вашего ассистента.", "com_assistants_update_success": "Успешно обновлено", "com_auth_already_have_account": "Уже зарегистрированы?", "com_auth_apple_login": "Зарегистрироваться с помощью Apple", "com_auth_back_to_login": "Вернуться к авторизации", "com_auth_click": "Нажмите", "com_auth_click_here": "Нажмите здесь", "com_auth_continue": "Продолжить", "com_auth_create_account": "Создать аккаунт", "com_auth_discord_login": "Войти с помощью Discord", "com_auth_email": "Email", "com_auth_email_address": "Адрес электронной почты", "com_auth_email_max_length": "Email не может быть длиннее 120 символов", "com_auth_email_min_length": "Email должен содержать не менее 6 символов", "com_auth_email_pattern": "Вы должны ввести действительный адрес электронной почты", "com_auth_email_required": "Email обязателен", "com_auth_email_resend_link": "Отправить письмо повторно", "com_auth_email_resent_failed": "Не удалось повторно отправить письмо для подтверждения", "com_auth_email_resent_success": "Письмо с подтверждением успешно отправлено повторно", "com_auth_email_verification_failed": "Не удалось выполнить проверку электронной почты", "com_auth_email_verification_failed_token_missing": "Ошибка верификации: отсутствует токен подтверждения", "com_auth_email_verification_in_progress": "Подождите, идет проверка вашего email", "com_auth_email_verification_invalid": "Неверная верификация электронной почты", "com_auth_email_verification_redirecting": "Перенаправление через {{0}} сек...", "com_auth_email_verification_resend_prompt": "Не получили письмо?", "com_auth_email_verification_success": "Адрес электронной почты успешно подтвержден", "com_auth_email_verifying_ellipsis": "Подтверждение...", "com_auth_error_create": "Возникла ошибка при попытке зарегистрировать ваш аккаунт. Пожалуйста, попробуйте еще раз.", "com_auth_error_invalid_reset_token": "Этот токен сброса пароля больше не действителен.", "com_auth_error_login": "Не удалось войти с предоставленной информацией. Пожалуйста, проверьте ваши учетные данные и попробуйте снова.", "com_auth_error_login_ban": "Ваша учетная запись была временно заблокирована в связи с нарушениями нашего сервиса.", "com_auth_error_login_rl": "Слишком много попыток входа в систему за короткий промежуток времени. Пожалуйста, повторите попытку позже.", "com_auth_error_login_server": "Произошла внутренняя ошибка сервера. Пожалуйста, подождите несколько минут и повторите попытку.", "com_auth_error_login_unverified": "Ваша учетная запись не подтверждена. Пожалуйста, проверьте вашу электронную почту и перейдите по ссылке для подтверждения.", "com_auth_facebook_login": "Войти с помощью Facebook", "com_auth_full_name": "Полное имя", "com_auth_github_login": "Войти с помощью Github", "com_auth_google_login": "Войти с помощью Google", "com_auth_here": "ЗДЕСЬ", "com_auth_login": "Войти", "com_auth_login_with_new_password": "Теперь вы можете войти с новым паролем.", "com_auth_name_max_length": "Имя должно быть короче 80 символов", "com_auth_name_min_length": "Имя должно содержать не менее 3 символов", "com_auth_name_required": "Имя обязательно", "com_auth_no_account": "Еще не зарегистрированы?", "com_auth_password": "Пароль", "com_auth_password_confirm": "Подтвердите пароль", "com_auth_password_forgot": "Забыли пароль?", "com_auth_password_max_length": "Пароль должен быть не более 128 символов", "com_auth_password_min_length": "Пароль должен содержать не менее 8 символов", "com_auth_password_not_match": "Пароли не совпадают", "com_auth_password_required": "Пароль обязателен", "com_auth_registration_success_generic": "Пожалуйста, проверьте вашу почту для подтверждения email-адреса", "com_auth_registration_success_insecure": "Регистрация успешно завершена", "com_auth_reset_password": "Сбросить пароль", "com_auth_reset_password_if_email_exists": "Если аккаунт с указанным адресом существует, мы отправили на него инструкции по сбросу пароля. Пожалуйста, проверьте также папку \"Спам\".", "com_auth_reset_password_link_sent": "Письмо отправлено", "com_auth_reset_password_success": "Сброс пароля успешно выполнен", "com_auth_sign_in": "Войти", "com_auth_sign_up": "Зарегистрироваться", "com_auth_submit_registration": "Отправить регистрацию", "com_auth_to_reset_your_password": "чтобы сбросить ваш пароль.", "com_auth_to_try_again": "чтобы попробовать снова.", "com_auth_two_factor": "Проверьте код в выбранном вами приложении одноразовых паролей", "com_auth_username": "Имя пользователя (необязательно)", "com_auth_username_max_length": "Имя пользователя должно быть не более 20 символов", "com_auth_username_min_length": "Имя пользователя должно содержать не менее 2 символов", "com_auth_verify_your_identity": "Подтвердите ваши идентификационные данные.", "com_auth_welcome_back": "Добро пожаловать", "com_click_to_download": "(нажмите для скачивания)", "com_download_expired": "срок скачивания истек", "com_download_expires": "(нажмите здесь для скачивания - срок действия до {{0}})", "com_endpoint": "Эндпоинт", "com_endpoint_agent": "Агент", "com_endpoint_agent_model": "Модель агента (Рекомендуется: GPT-3.5)", "com_endpoint_agent_placeholder": "Выберите Агента", "com_endpoint_ai": "ИИ", "com_endpoint_anthropic_maxoutputtokens": "Максимальное количество токенов, которые могут быть сгенерированы в ответе. Укажите меньшее значение для более коротких ответов и большее значение для более длинных ответов.", "com_endpoint_anthropic_prompt_cache": "Кэширование промтов позволяет повторно использовать большой контекст или инструкции между API-запросами, снижая затраты и задержки", "com_endpoint_anthropic_temp": "Диапазон значений от 0 до 1. Используйте значение temp ближе к 0 для аналитических / множественного выбора и ближе к 1 для креативных и генеративных задач. Мы рекомендуем изменять это или Top P, но не оба значения одновременно.", "com_endpoint_anthropic_thinking": "Включает режим \"Рассуждение\" для поддерживаемых моделей Claude (3.7 Sonnet).  \nПримечание: требуется установка «Бюджета на рассуждение» ниже «Максимального числа токенов на вывод».", "com_endpoint_anthropic_thinking_budget": "Определяет максимальное количество токенов, которое Claude может использовать для режима \"Рассуждение\". Более высокий бюджет может повысить качество ответов за счёт более глубокого анализа сложных задач, хотя Claude может использовать не весь выделенный бюджет, особенно при значениях выше 32K. Этот параметр должен быть меньше, чем «Максимальное число выводимых токенов».", "com_endpoint_anthropic_topk": "Top K изменяет то, как модель выбирает токены для вывода. Top K равное 1 означает, что выбирается наиболее вероятный токен из всего словаря модели (так называемое жадное декодирование), а Top K равное 3 означает, что следующий токен выбирается из трех наиболее вероятных токенов (с использованием температуры).", "com_endpoint_anthropic_topp": "Top P изменяет то, как модель выбирает токены для вывода. Токены выбираются из наиболее вероятных (см. параметр topK) до наименее вероятных, пока сумма их вероятностей не достигнет значения top-p.", "com_endpoint_assistant": "Ассистент", "com_endpoint_assistant_model": "Модель ассистента", "com_endpoint_assistant_placeholder": "Выберите ассистента в правой боковой панели", "com_endpoint_completion": "Завершение", "com_endpoint_completion_model": "Модель завершения (Рекомендуется: GPT-4)", "com_endpoint_config_click_here": "Нажми Здесь", "com_endpoint_config_google_api_info": "Чтобы получить ключ к API Generative Language (для Gemini),", "com_endpoint_config_google_api_key": "Google API Key", "com_endpoint_config_google_cloud_platform": "(из Google Cloud Platform)", "com_endpoint_config_google_gemini_api": "(Gemini API)", "com_endpoint_config_google_service_key": "Google Service Account Key", "com_endpoint_config_key": "Указать ключ к API", "com_endpoint_config_key_encryption": "<PERSON>а<PERSON> ключ зашифрован и будет удалён", "com_endpoint_config_key_for": "Установить ключ к API для", "com_endpoint_config_key_google_need_to": "Вам нужно", "com_endpoint_config_key_google_service_account": "Создать Service Account", "com_endpoint_config_key_google_vertex_ai": "Активировать Vertex AI", "com_endpoint_config_key_google_vertex_api": "API в Google Cloud, после", "com_endpoint_config_key_google_vertex_api_role": "Убедитесь что нажали на 'Create and Continue' чтобы получить как минимум 'Vertex AI User'. Наконец, создайте JSON-ключ чтобы импортировать его сюда.", "com_endpoint_config_key_import_json_key": "Импортировать Service Account JSON Key.", "com_endpoint_config_key_import_json_key_invalid": "Некорректный Service Account JSON Key, Вы импортировали верный файл?", "com_endpoint_config_key_import_json_key_success": "Успешно Импортирован Service Account JSON Key", "com_endpoint_config_key_name": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_config_key_never_expires": "Ва<PERSON> ключ будет действовать бессрочно", "com_endpoint_config_placeholder": "Укажите ваш ключ к API в меню сверху для начала разговора.", "com_endpoint_config_value": "Введите значение для", "com_endpoint_context": "Контекст", "com_endpoint_context_info": "Максимальное количество токенов, которое может быть использовано для контекста. Используется для контроля количества токенов, отправляемых за один запрос. Если не указано, будут использованы системные значения по умолчанию, основанные на известном размере контекста моделей. Установка более высоких значений может привести к ошибкам и/или более высокой стоимости токенов.", "com_endpoint_context_tokens": "Максимальное количество контекстных токенов", "com_endpoint_custom_name": "Кастомное имя", "com_endpoint_default": "по умолчанию", "com_endpoint_default_blank": "по умолчанию: пусто", "com_endpoint_default_empty": "по умолчанию: пусто", "com_endpoint_default_with_num": "по умолчанию: {{0}}", "com_endpoint_deprecated": "Больше не поддерживается", "com_endpoint_deprecated_info": "Эта конечная точка устарела и может быть удалена в будущих версиях. Пожалуйста, используйте вместо этого конечную точку агента.", "com_endpoint_deprecated_info_a11y": "Конечная точка плагина больше не поддерживается и может быть удалена в будущих версиях. Рекомендуем использовать конечную точку агента вместо этой", "com_endpoint_examples": "Примеры", "com_endpoint_export": "Экспорт", "com_endpoint_export_share": "Экспорт/Поделиться", "com_endpoint_frequency_penalty": "Штраф за частоту", "com_endpoint_func_hover": "Включить использование плагинов как функции OpenAI", "com_endpoint_google_custom_name_placeholder": "Задайте кастомное имя для Google", "com_endpoint_google_maxoutputtokens": "Максимальное количество токенов, которые могут быть сгенерированы в ответе. Укажите меньшее значение для более коротких ответов и большее значение для более длинных ответов.", "com_endpoint_google_temp": "Более высокие значения = более случайные результаты, более низкие значения = более фокусированные и детерминированные результаты. Мы рекомендуем изменять это или Top P, но не оба значения одновременно.", "com_endpoint_google_topk": "Top-k изменяет то, как модель выбирает токены для вывода. Top-k равное 1 означает, что выбирается наиболее вероятный токен из всего словаря модели (так называемое жадное декодирование), а Top-k равное 3 означает, что следующий токен выбирается из трех наиболее вероятных токенов (с использованием температуры).", "com_endpoint_google_topp": "Top-p изменяет то, как модель выбирает токены для вывода. Токены выбираются из наиболее вероятных K (см. параметр topK) до наименее вероятных, пока сумма их вероятностей не достигнет значения top-p.", "com_endpoint_instructions_assistants": "Инструкции для ассистентов", "com_endpoint_instructions_assistants_placeholder": "Переопределяет инструкции для ассистента. Это полезно для изменения поведения для отдельного запуска.", "com_endpoint_max_output_tokens": "Максимальное количество выводимых токенов", "com_endpoint_message": "Сообщение", "com_endpoint_message_new": "Сообщение {{0}}", "com_endpoint_message_not_appendable": "Отредактируйте свое сообщение или перегенерируйте.", "com_endpoint_my_preset": "Мой Пресет", "com_endpoint_no_presets": "Пока нет пресетов, используйте кнопку настроек чтобы создать его", "com_endpoint_open_menu": "Открыть меню", "com_endpoint_openai_custom_name_placeholder": "Задайте кастомное имя для ChatGPT", "com_endpoint_openai_detail": "Разрешение для запросов Vision. \"Низкое\" - дешевле и быстрее, \"Высокое\" - более детализировано и дорогое, а \"Авто\" автоматически выберет один из двух вариантов в зависимости от разрешения изображения.", "com_endpoint_openai_freq": "Число от -2.0 до 2.0. Положительные значения штрафуют новые токены на основе их частоты в тексте до сих пор, уменьшая вероятность модели повторить ту же строку дословно.", "com_endpoint_openai_max": "Максимальное количество генерируемых токенов. Общая длина входных токенов и сгенерированных токенов ограничена длиной контекста модели.", "com_endpoint_openai_max_tokens": "Необязательное поле `max_tokens`, задающее максимальное количество токенов, которое может быть сгенерировано в ответе чата. Общая длина входных токенов и сгенерированных токенов ограничена длиной контекста модели. Вы можете получить ошибку, если это число превысит максимальную длину контекста.", "com_endpoint_openai_pres": "Число от -2.0 до 2.0. Положительные значения штрафуют новые токены на основе того, появляются ли они в тексте до сих пор, увеличивая вероятность модели говорить о новых темах.", "com_endpoint_openai_prompt_prefix_placeholder": "Задайте кастомные промпты для включения в системное сообщение. По умолчанию: нет", "com_endpoint_openai_reasoning_effort": "Только для моделей o1: ограничивает затраты на рассуждение для моделей с поддержкой рассуждения. Снижение усилий на рассуждение может ускорить ответы и уменьшить количество токенов, используемых для размышлений.", "com_endpoint_openai_resend": "Повторно отправить все ранее прикрепленные изображения. Примечание: это может значительно увеличить стоимость токенов, и при большом количестве прикрепленных изображений могут возникнуть ошибки.", "com_endpoint_openai_resend_files": "Повторно отправить все ранее прикрепленные файлы. Примечание: это увеличит расход токенов, и при большом количестве вложений могут возникнуть ошибки.", "com_endpoint_openai_stop": "До 4 последовательностей, после которых API прекратит генерировать дальнейшие токены.", "com_endpoint_openai_temp": "Более высокие значения = более случайные результаты, более низкие значения = более фокусированные и детерминированные результаты. Мы рекомендуем изменять это или Top P, но не оба значения одновременно.", "com_endpoint_openai_topp": "Альтернатива выбору с использованием температуры, называемая выбором по ядру, при которой модель учитывает результаты токенов с наибольшей вероятностью top_p. Таким образом, значение 0,1 означает, что рассматриваются только токены, составляющие верхние 10% вероятностной массы. Мы рекомендуем изменять это или температуру, но не оба значения одновременно.", "com_endpoint_output": "Вывод", "com_endpoint_plug_image_detail": "Детали изображения", "com_endpoint_plug_resend_files": "Повторить отправку файлов", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Задайте кастомные инструкции для включения в системное сообщение. По умолчанию: нет", "com_endpoint_plug_skip_completion": "Пропустить завершение", "com_endpoint_plug_use_functions": "Использовать функции", "com_endpoint_presence_penalty": "Штраф за присутствие", "com_endpoint_preset": "пресет", "com_endpoint_preset_default": "теперь пресет \"По умолчаанию\".", "com_endpoint_preset_default_item": "По умолчанию:", "com_endpoint_preset_default_none": "Активных пресетов по умолчанию нет.", "com_endpoint_preset_default_removed": "больше не пресет по умолчанию.", "com_endpoint_preset_delete_confirm": "Вы уверены, что хотите удалить этот пресет?", "com_endpoint_preset_delete_error": "Произошла ошибка при удалении вашего пресета. Пожалуйста, попробуйте еще раз.", "com_endpoint_preset_import": "Пресет Импортирован!", "com_endpoint_preset_import_error": "Произошла ошибка при импорте вашего пресета. Пожалуйста, попробуйте еще раз.", "com_endpoint_preset_name": "Имя пресета", "com_endpoint_preset_save_error": "Произошла ошибка при сохранении вашего пресета. Пожалуйста, попробуйте еще раз.", "com_endpoint_preset_selected": "Пресет Активирован!", "com_endpoint_preset_selected_title": "Активирован!", "com_endpoint_preset_title": "Пресет", "com_endpoint_presets": "пресеты", "com_endpoint_presets_clear_warning": "Вы уверены, что хотите удалить все пресеты? Это действие необратимо и восстановление невозможно.", "com_endpoint_prompt_cache": "Использовать кэширование промтов", "com_endpoint_prompt_prefix": "Префикс промпта", "com_endpoint_prompt_prefix_assistants": "Дополнительные инструкции", "com_endpoint_prompt_prefix_assistants_placeholder": "Задайте дополнительные инструкции или контекст сверху основных инструкций ассистента. Игнорируется, если пусто.", "com_endpoint_prompt_prefix_placeholder": "Задайте пользовательские инструкции или контекст. Игнорируется, если пусто.", "com_endpoint_reasoning_effort": "Затраты на рассуждение", "com_endpoint_save_as_preset": "Сохранить как Пресет", "com_endpoint_search": "Поиск эндпоинта по имени", "com_endpoint_search_endpoint_models": "Поиск {{0}} моделей...", "com_endpoint_search_models": "Поиск моделей...", "com_endpoint_search_var": "Поиск {{0}}...", "com_endpoint_select_model": "Выбрать модель", "com_endpoint_set_custom_name": "Задайте кастомное имя на случай, если вы сможете найти эту предустановку :)", "com_endpoint_skip_hover": "Пропустить этап завершения, который проверяет окончательный ответ и сгенерированные шаги", "com_endpoint_stop": "Стоп-последовательности", "com_endpoint_stop_placeholder": "Разделяйте значения нажатием `Enter`", "com_endpoint_temperature": "Температура", "com_endpoint_thinking": "Рассуждение", "com_endpoint_thinking_budget": "Бюджет на рассуждения", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "Использовать активного ассистента", "com_error_expired_user_key": "Предоставленный ключ для {{0}} истек {{1}}. Пожалуйста, укажите новый ключ и повторите попытку.", "com_error_files_dupe": "Обнаружен дублирующийся файл", "com_error_files_empty": "Пустые файлы не допускаются", "com_error_files_process": "Произошла ошибка при обработке файла", "com_error_files_unsupported_capability": "Отсутствуют разрешения для работы с данным типом файлов", "com_error_files_upload": "При загрузке файла произошла ошибка", "com_error_files_upload_canceled": "Запрос на загрузку файла был отменен. Примечание: файл все еще может обрабатываться и потребуется удалить его вручную.", "com_error_files_validation": "Произошла ошибка при проверке файла", "com_error_input_length": "Последнее сообщение слишком длинное и превышает допустимый лимит токенов ({{0}}). Пожалуйста, сократите сообщение, измените максимальный размер контекста в параметрах беседы или создайте ответвление беседы для продолжения.", "com_error_invalid_agent_provider": "Провайдер «{{0}}» недоступен для использования с агентами. Перейдите в настройки агента и выберите доступного провайдера.", "com_error_invalid_user_key": "Предоставлен некорректный ключ. Пожалуйста, укажите действительный ключ и повторите попытку.", "com_error_moderation": "К сожалению, отправленный вами контент был помечен нашей системой модерации как не соответствующий правилам сообщества. Мы не можем продолжить обсуждение этой конкретной темы. Если у вас есть другие вопросы или темы, которые вы хотели бы обсудить, пожалуйста, отредактируйте сообщение или начните новый диалог.", "com_error_no_base_url": "Базовый URL не найден. Пожалуйста, укажите его и повторите попытку.", "com_error_no_user_key": "Ключ не найден. Пожалуйста, укажите ключ и повторите попытку.", "com_files_filter": "Фильтр <PERSON>а<PERSON><PERSON>ов", "com_files_no_results": "Нет результатов", "com_files_number_selected": "Выбрано {{0}} из {{1}} файл(а/ов)", "com_generated_files": "Сгенерированные файлы:", "com_hide_examples": "Скрыть примеры", "com_nav_2fa": "Двухфакторная аутентификация (2FA)", "com_nav_account_settings": "Настройки аккаунта", "com_nav_always_make_prod": "Автоматически публиковать новые версии", "com_nav_archive_created_at": "Дата создания", "com_nav_archive_name": "Имя", "com_nav_archived_chats": "Архивированные чаты", "com_nav_at_command": "@-команда", "com_nav_at_command_description": "Переключение команды \"@\" для выбора эндпоинтов, моделей, пресетов и др.", "com_nav_audio_play_error": "Ошибка воспроизведения аудио: {{0}}", "com_nav_audio_process_error": "Ошибка обработки аудио: {{0}}", "com_nav_auto_scroll": "Автоматически проматывать к самым новым сообщениям при открытии", "com_nav_auto_send_prompts": "Автоотправка промптов", "com_nav_auto_send_text": "Автоотправка сообщений", "com_nav_auto_send_text_disabled": "установите -1 для отключения", "com_nav_auto_transcribe_audio": "Автоматическая транскрипция", "com_nav_automatic_playback": "Автовоспроизведение последнего сообщения", "com_nav_balance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_browser": "Браузер", "com_nav_center_chat_input": "Центрировать поле ввода чата на экране приветствия", "com_nav_change_picture": "Изменить изображение", "com_nav_chat_commands": "Команды чата", "com_nav_chat_commands_info": "Эти команды активируются при вводе определенных символов в начале вашего сообщения. Каждая команда запускается своим префиксом. Вы можете отключить их, если часто используете эти символы для начала сообщений.", "com_nav_chat_direction": "Направление чата", "com_nav_clear_all_chats": "Удалить все чаты", "com_nav_clear_cache_confirm_message": "Вы уверены, что хотите очистить кэш?", "com_nav_clear_conversation": "Удалить чаты", "com_nav_clear_conversation_confirm_message": "Вы уверены, что хотите удалить все чаты? Это действие нельзя отменить.", "com_nav_close_sidebar": "Закрыть боковую панель", "com_nav_commands": "Команды", "com_nav_confirm_clear": "Подтвердить удаление", "com_nav_conversation_mode": "Режим диалога", "com_nav_convo_menu_options": "Параметры диалога", "com_nav_db_sensitivity": "Чувствительность в децибелах", "com_nav_delete_account": "Удалить аккаунт", "com_nav_delete_account_button": "Удалить аккаунт навсегда", "com_nav_delete_account_confirm": "Вы уверены, что хотите удалить аккаунт?", "com_nav_delete_account_email_placeholder": "Введите email вашего аккаунта", "com_nav_delete_cache_storage": "Очистить кэш озвучивания", "com_nav_delete_data_info": "Все ваши данные будут удалены", "com_nav_delete_warning": "ВНИМАНИЕ: Ваша учетная запись будет удалена без возможности восстановления.", "com_nav_enable_cache_tts": "Включить кэширование TTS", "com_nav_enable_cloud_browser_voice": "Использовать облачные голоса", "com_nav_enabled": "Включено", "com_nav_engine": "Модель", "com_nav_enter_to_send": "Отправить сообщение нажатием Enter", "com_nav_export": "Экспорт", "com_nav_export_all_message_branches": "Экспортировать все ветки сообщений", "com_nav_export_conversation": "Экспортировать разговор", "com_nav_export_filename": "Имя файла", "com_nav_export_filename_placeholder": "Задайте имя файла", "com_nav_export_include_endpoint_options": "Включить параметры эндпоинта", "com_nav_export_recursive": "Рекурсивно", "com_nav_export_recursive_or_sequential": "Рекурсивно или последовательно?", "com_nav_export_type": "Тип", "com_nav_external": "Внешние", "com_nav_font_size": "Размер шрифта", "com_nav_font_size_base": "Средний", "com_nav_font_size_lg": "Крупный", "com_nav_font_size_sm": "Мелкий", "com_nav_font_size_xl": "Очень большой", "com_nav_font_size_xs": "Очень мелкий", "com_nav_help_faq": "Помощь и Вопросы", "com_nav_hide_panel": "Скрыть правую боковую панель", "com_nav_info_code_artifacts": "Включает отображение экспериментального программного кода рядом с чатом", "com_nav_info_code_artifacts_agent": "Включает использование артефактов кода для этого агента. По умолчанию добавляются дополнительные инструкции, связанные с использованием артефактов, если не включен режим «Пользовательский промт».", "com_nav_info_custom_prompt_mode": "При включении этого режима системный промт по умолчанию для создания артефактов не будет использоваться. Все инструкции для генерации артефактов должны задаваться вручную.", "com_nav_info_enter_to_send": "Если включено, нажатие клавиши Enter отправит ваше сообщение. Если отключено, Enter добавит новую строку, а для отправки сообщения нужно будет нажать CTRL + Enter или ⌘ + Enter.", "com_nav_info_fork_change_default": "«Только видимые сообщения» включает лишь прямой путь к выбранному сообщению. «Включить связанные ветки» добавляет ответвления вдоль этого пути. «Включить все сообщения до/от этой точки» охватывает все связанные сообщения и ветки.", "com_nav_info_fork_split_target_setting": "Если включено, ветвление будет выполняться от целевого сообщения до последнего сообщения в диалоге в соответствии с выбранным поведением.", "com_nav_info_include_shadcnui": "При включении будут добавлены инструкции по использованию компонентов shadcn/ui. shadcn/ui — это набор переиспользуемых компонентов, созданных на основе Radix UI и Tailwind CSS. Примечание: эти инструкции довольно объемные, включайте их только если для вас важно информировать LLM о правильных импортах и компонентах. Подробнее о компонентах можно узнать на сайте: https://ui.shadcn.com/", "com_nav_info_latex_parsing": "При включении этой функции код LaTeX в сообщениях будет отображаться в виде математических формул. Если вам не требуется отображение LaTeX, отключение этой функции может улучшить производительность.", "com_nav_info_save_badges_state": "Если эта опция включена, состояние значков чата будет сохраняться. Это означает, что при создании нового чата значки останутся в том же состоянии, что и в предыдущем чате. Если вы отключите эту опцию, значки будут сбрасываться до состояния по умолчанию каждый раз при создании нового чата.", "com_nav_info_save_draft": "При включении этой функции текст и прикрепленные файлы, введенные в форму чата, будут автоматически сохраняться локально как черновики. Эти черновики останутся доступными даже после перезагрузки страницы или перехода к другому разговору. Черновики хранятся локально на вашем устройстве и удаляются после отправки сообщения.", "com_nav_info_show_thinking": "Если включено, выпадающие блоки рассуждений в чате будут открыты по умолчанию, позволяя видеть ход рассуждений ИИ в реальном времени. Если отключено, блоки рассуждений будут скрыты по умолчанию для более упрощённого интерфейса.", "com_nav_info_user_name_display": "Если включено, над каждым вашим сообщением будет отображаться ваше имя пользователя. Если отключено, над вашими сообщениями будет отображаться только \"Вы\".", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "Автоопределение", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_catalan": "Каталанский", "com_nav_lang_chinese": "中文", "com_nav_lang_czech": "Чешский", "com_nav_lang_danish": "Датский", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_hungarian": "Венгерский", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_persian": "Фарси", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "Локализация", "com_nav_latex_parsing": "Обработка LaTeX в сообщениях (может повлиять на производительность)", "com_nav_log_out": "Выйти", "com_nav_long_audio_warning": "Обработка длинных текстов займет больше времени", "com_nav_maximize_chat_space": "Развернуть чат", "com_nav_modular_chat": "Разрешить менять точки подключения в середине разговора", "com_nav_my_files": "Мои файлы", "com_nav_not_supported": "Не поддерживается", "com_nav_open_sidebar": "Открыть боковую панель", "com_nav_playback_rate": "Скорость воспроизведения", "com_nav_plugin_auth_error": "При попытке аутентификации этого плагина произошла ошибка. Пожалуйста, попробуйте еще раз.", "com_nav_plugin_install": "Установить", "com_nav_plugin_search": "Поиск плагинов", "com_nav_plugin_store": "<PERSON>а<PERSON><PERSON><PERSON><PERSON><PERSON> плаг<PERSON><PERSON>ов", "com_nav_plugin_uninstall": "Удалить", "com_nav_plus_command": "Добавить команду", "com_nav_plus_command_description": "Переключить команду ' + ' для настройки множественных ответов", "com_nav_profile_picture": "Изображение профиля", "com_nav_save_badges_state": "Сохранить значок чата", "com_nav_save_drafts": "Сохранить черновики локально", "com_nav_scroll_button": "Кнопка прокрутки в конец сообщения", "com_nav_search_placeholder": "Поиск сообщений", "com_nav_send_message": "Отправить сообщение", "com_nav_setting_account": "Аккаунт", "com_nav_setting_chat": "Чат", "com_nav_setting_data": "Управление данными", "com_nav_setting_general": "Общие", "com_nav_setting_speech": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_settings": "Настройки", "com_nav_shared_links": "Связываемые ссылки", "com_nav_show_code": "Всегда показывать код при использовании интерпретатора", "com_nav_show_thinking": "Открывать блоки рассуждений по умолчанию", "com_nav_slash_command": "/-Команда", "com_nav_slash_command_description": "Вызов командной строки клавишей '/' для выбора промта с клавиатуры", "com_nav_speech_to_text": "Преобразование речи в текст", "com_nav_stop_generating": "Остановить генерацию", "com_nav_text_to_speech": "Синтез речи", "com_nav_theme": "Тема", "com_nav_theme_dark": "Темная", "com_nav_theme_light": "Светлая", "com_nav_theme_system": "Системная", "com_nav_tool_dialog": "Инструменты ассистента", "com_nav_tool_dialog_agents": "Инструменты агента", "com_nav_tool_dialog_description": "Ассистент должен быть сохранен для применения выбранных инструментов.", "com_nav_tool_remove": "Удалить", "com_nav_tool_search": "Поиск инструментов", "com_nav_user": "ПОЛЬЗОВАТЕЛЬ", "com_nav_user_msg_markdown": "Отображать сообщения пользователя в формате Markdown", "com_nav_user_name_display": "Отображать имя пользователя в сообщениях", "com_nav_voice_select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_show_agent_settings": "Настройки агента", "com_show_completion_settings": "Показать настройки завершения", "com_show_examples": "Показать примеры", "com_sidepanel_agent_builder": "Конструктор агента", "com_sidepanel_assistant_builder": "Конструктор Ассистента", "com_sidepanel_attach_files": "Прикрепить файлы", "com_sidepanel_conversation_tags": "Закладки", "com_sidepanel_hide_panel": "Скрыть панель", "com_sidepanel_manage_files": "Управление файлами", "com_sidepanel_parameters": "Параметры", "com_ui_2fa_account_security": "Двухфакторная аутентификация добавляет дополнительный уровень защиты вашему аккаунту.", "com_ui_2fa_disable": "Отключить 2FA", "com_ui_2fa_disable_error": "Произошла ошибка при отключении двухфакторной аутентификации.", "com_ui_2fa_disabled": "Двухфакторная аутентификация (2FA) отключена", "com_ui_2fa_enable": "Включить 2FA", "com_ui_2fa_enabled": "Двухфакторная аутентификация (2FA) включена", "com_ui_2fa_generate_error": "Произошла ошибка при создании настроек двухфакторной аутентификации", "com_ui_2fa_invalid": "Неверный код двухфакторной аутентификации", "com_ui_2fa_setup": "Настроить 2FA", "com_ui_2fa_verified": "Двухфакторная аутентификация успешно подтверждена", "com_ui_accept": "Принимаю", "com_ui_action_button": "Кнопка действия", "com_ui_add": "Добавить", "com_ui_add_model_preset": "Добавить модель или пресет для дополнительного ответа", "com_ui_add_multi_conversation": "Добавить несколько бесед", "com_ui_adding_details": "Добавление деталей", "com_ui_admin": "Администратор", "com_ui_admin_access_warning": "Отключение административного доступа к этой функции может вызвать непредвиденные проблемы с интерфейсом, требующие обновления страницы. После сохранения изменений вернуть настройку можно будет только через параметр interface в конфигурационном файле librechat.yaml, что повлияет на все роли.", "com_ui_admin_settings": "Настройки администратора", "com_ui_advanced": "Расширенные", "com_ui_advanced_settings": "Дополнительные настройки", "com_ui_agent": "Агент", "com_ui_agent_chain": "Цепочка агентов (\"Mixture-of-Agents\")", "com_ui_agent_chain_info": "Позволяет создавать последовательности агентов, где каждый агент может использовать результаты работы предыдущих агентов в цепочке. Основано на архитектуре «Смешение агентов» (Mixture-of-Agents), в которой агенты используют предыдущие результаты в качестве вспомогательной информации.", "com_ui_agent_chain_max": "Вы достигли максимального количества агентов: {{0}}.", "com_ui_agent_delete_error": "Произошла ошибка при удалении ассистента", "com_ui_agent_deleted": "Ассистент успешно удален", "com_ui_agent_duplicate_error": "Произошла ошибка при дублировании ассистента", "com_ui_agent_duplicated": "Ассистент успешно скопирован", "com_ui_agent_editing_allowed": "Другие пользователи уже могут редактировать этого ассистента", "com_ui_agent_recursion_limit": "Максимальное количество шагов агента", "com_ui_agent_recursion_limit_info": "Ограничивает количество шагов, которые агент может выполнить за один запуск перед выдачей окончательного ответа. Значение по умолчанию — 25 шагов. Шагом считается либо запрос к API ИИ, либо использование инструмента. Например, базовое взаимодействие с инструментом включает 3 шага: исходный запрос, использование инструмента и последующий запрос.", "com_ui_agent_shared_to_all": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_agent_var": "{{0}} агент", "com_ui_agents": "Агенты", "com_ui_agents_allow_create": "Разрешить создание ассистентов", "com_ui_agents_allow_share_global": "Разрешить доступ к Агентам всем пользователям", "com_ui_agents_allow_use": "Разрешить использование ассистентов", "com_ui_all": "все", "com_ui_all_proper": "Все", "com_ui_analyzing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_analyzing_finished": "Ана<PERSON><PERSON><PERSON> завершен", "com_ui_api_key": "ключ API", "com_ui_archive": "Архивировать", "com_ui_archive_delete_error": "Не удалось удалить архивированный чат", "com_ui_archive_error": "Не удалось заархивировать чат", "com_ui_artifact_click": "Нажмите, чтобы открыть", "com_ui_artifacts": "Артефакты", "com_ui_artifacts_toggle": "Показать/скрыть артефакты", "com_ui_artifacts_toggle_agent": "Включить артефакты", "com_ui_ascending": "По возрастанию", "com_ui_assistant": "Помощник", "com_ui_assistant_delete_error": "Произошла ошибка при удалении ассистента", "com_ui_assistant_deleted": "Ассистент успешно удален", "com_ui_assistants": "Ассистенты", "com_ui_assistants_output": "Вывод ассистентов", "com_ui_attach_error": "Невозможно прикрепить файл. Создайте новый или выберите разговор, или попробуйте обновить страницу.", "com_ui_attach_error_openai": "Невозможно прикрепить файлы ассистента к другим режимам", "com_ui_attach_error_size": "Превышен лимит размера файла для этого режима:", "com_ui_attach_error_type": "Неподдерживаемый тип файла для этого режима:", "com_ui_attach_remove": "Удалить файл", "com_ui_attach_warn_endpoint": "Файлы сторонних приложений могут быть проигнорированы без совместимого плагина", "com_ui_attachment": "Вложение", "com_ui_auth_type": "Тип аутентификации", "com_ui_auth_url": "URL авторизации", "com_ui_authentication": "Аутентификация", "com_ui_authentication_type": "Тип аутентификации", "com_ui_avatar": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_azure": "Azure", "com_ui_back_to_chat": "Вернуться к чату", "com_ui_back_to_prompts": "Вернуться к промтам", "com_ui_backup_codes": "Резервные коды", "com_ui_backup_codes_regenerate_error": "Произошла ошибка при повторной генерации резервных кодов", "com_ui_backup_codes_regenerated": "Резервные коды успешно сгенерированы повторно", "com_ui_basic_auth_header": "Заголовок базовой авторизации", "com_ui_bearer": "Токен на предъявителя", "com_ui_bookmark_delete_confirm": "Вы уверены, что хотите удалить эту закладку?", "com_ui_bookmarks": "Закладки", "com_ui_bookmarks_add": "Добавить закладку", "com_ui_bookmarks_add_to_conversation": "Добавить в текущий разговор", "com_ui_bookmarks_count": "Количество", "com_ui_bookmarks_create_error": "Произошла ошибка при создании закладки", "com_ui_bookmarks_create_exists": "Такая закладка уже существует", "com_ui_bookmarks_create_success": "Закладка успешно создана", "com_ui_bookmarks_delete": "Удалить закладку", "com_ui_bookmarks_delete_error": "Произошла ошибка при удалении закладки", "com_ui_bookmarks_delete_success": "Закладка успешно удалена", "com_ui_bookmarks_description": "Описание", "com_ui_bookmarks_edit": "Редактировать закладку", "com_ui_bookmarks_filter": "Поиск в закладках...", "com_ui_bookmarks_new": "Новая закладка", "com_ui_bookmarks_title": "Заголовок", "com_ui_bookmarks_update_error": "Произошла ошибка при обновлении закладки", "com_ui_bookmarks_update_success": "Закладка успешно обновлена", "com_ui_bulk_delete_error": "Не удалось удалить общие ссылки", "com_ui_callback_url": "URL обратного вызова", "com_ui_cancel": "Отмена", "com_ui_category": "Категория", "com_ui_chat": "Чат", "com_ui_chat_history": "История чатов", "com_ui_clear": "Удалить", "com_ui_clear_all": "Очистить всё", "com_ui_client_id": "ID клиента", "com_ui_client_secret": "Секрет клиента", "com_ui_close": "Закрыть", "com_ui_close_menu": "Закрыть меню", "com_ui_code": "<PERSON>од", "com_ui_collapse_chat": "Свернуть чат", "com_ui_command_placeholder": "Необязательно: введите команду для промта или будет использовано название", "com_ui_command_usage_placeholder": "Выберите промпт по команде или названию", "com_ui_complete_setup": "Завершить настройку", "com_ui_confirm_action": "Подтвердить действие", "com_ui_confirm_admin_use_change": "Изменение этого параметра заблокирует доступ для администраторов, включая вас. Вы уверены, что хотите продолжить?", "com_ui_confirm_change": "Подтвердить изменения", "com_ui_context": "Контекст", "com_ui_continue": "Продолжить", "com_ui_controls": "Управление", "com_ui_convo_delete_error": "Не удалось удалить чат", "com_ui_copied": "Скопировано", "com_ui_copied_to_clipboard": "Скопировано в буфер обмена", "com_ui_copy_code": "Копировать код", "com_ui_copy_link": "Скопировать ссылку", "com_ui_copy_to_clipboard": "Копировать в буфер обмена", "com_ui_create": "Создать", "com_ui_create_link": "Создать ссылку", "com_ui_create_prompt": "Создать промт", "com_ui_creating_image": "Создается изображение. Подождите немного.", "com_ui_currently_production": "В настоящее время в продакшене", "com_ui_custom": "Настраиваемый", "com_ui_custom_header_name": "Настраиваемое имя заголовка", "com_ui_custom_prompt_mode": "Режим пользовательского промта", "com_ui_dashboard": "Главная панель", "com_ui_date": "Дата", "com_ui_date_april": "Апрель", "com_ui_date_august": "Август", "com_ui_date_december": "Декабрь", "com_ui_date_february": "Февраль", "com_ui_date_january": "Январь", "com_ui_date_july": "Июль", "com_ui_date_june": "Июнь", "com_ui_date_march": "Ма<PERSON><PERSON>", "com_ui_date_may": "<PERSON><PERSON><PERSON>", "com_ui_date_november": "Ноябрь", "com_ui_date_october": "Октябрь", "com_ui_date_previous_30_days": "За последние 30 дней", "com_ui_date_previous_7_days": "Предыдущие 7 дней", "com_ui_date_september": "Сентябрь", "com_ui_date_today": "Сегодня", "com_ui_date_yesterday": "Вчера", "com_ui_decline": "Не принимаю", "com_ui_default_post_request": "По умолчанию (POST-запрос)", "com_ui_delete": "Удалить", "com_ui_delete_action": "Удалить действие", "com_ui_delete_action_confirm": "Вы действительно хотите удалить это действие?", "com_ui_delete_agent_confirm": "Вы действительно хотите удалить этого агента?", "com_ui_delete_assistant_confirm": "Вы действительно хотите удалить этого ассистента? Это действие необратимо.", "com_ui_delete_confirm": "Будет удален следующий чат: ", "com_ui_delete_confirm_prompt_version_var": "Это действие удалит выбранную версию для '{{0}}'. Если других версий не существует, промт будет полностью удален.", "com_ui_delete_conversation": "Удалить чат?", "com_ui_delete_prompt": "Удалить промт?", "com_ui_delete_shared_link": "Удалить общую ссылку?", "com_ui_delete_tool": "Удалить инструмент", "com_ui_delete_tool_confirm": "Вы действительно хотите удалить этот инструмент?", "com_ui_descending": "По убыванию", "com_ui_description": "Описание", "com_ui_description_placeholder": "Дополнительно: введите описание для промта", "com_ui_disabling": "Отключение...", "com_ui_download": "Скачать", "com_ui_download_artifact": "Скачать артифакт", "com_ui_download_backup": "Скачать резервные коды", "com_ui_download_backup_tooltip": "Прежде чем продолжить, скачайте ваши резервные коды. Они понадобятся вам для восстановления доступа в случае утери устройства аутентификации", "com_ui_download_error": "Ошибка загрузки файла. Возможно, файл был удален.", "com_ui_dropdown_variables": "Выпадающие переменные:", "com_ui_dropdown_variables_info": "Создавайте пользовательские выпадающие списки для ваших промптов: `{{название_переменной:вариант1|вариант2|вариант3}}`", "com_ui_duplicate": "Дублировать", "com_ui_duplication_error": "Не удалось создать копию разговора", "com_ui_duplication_processing": "Создание копии беседы...", "com_ui_duplication_success": "Разговор успешно скопирован", "com_ui_edit": "Редактировать", "com_ui_endpoint": "Эндпоинт", "com_ui_endpoint_menu": "Меню настроек LLM", "com_ui_enter": "Ввести", "com_ui_enter_api_key": "Введите API-ключ", "com_ui_enter_openapi_schema": "Введите вашу OpenAPI схему", "com_ui_error": "Ошибка", "com_ui_error_connection": "Ошибка подключения к серверу. Попробуйте обновить страницу.", "com_ui_error_save_admin_settings": "Произошла ошибка при сохранении настроек администратора", "com_ui_examples": "Примеры", "com_ui_expand_chat": "Развернуть чат", "com_ui_export_convo_modal": "Экспорт беседы", "com_ui_field_required": "Это поле обязательно для заполнения", "com_ui_files": "Файлы", "com_ui_filter_prompts": "Фильтр промтов", "com_ui_filter_prompts_name": "Фильтровать промты по названию", "com_ui_final_touch": "Финальные штрихи", "com_ui_finance": "Финан<PERSON>ы", "com_ui_fork": "Разделить", "com_ui_fork_all_target": "Включить все сюда", "com_ui_fork_branches": "Включить связанные ветки", "com_ui_fork_change_default": "Изменить вариант ветвления по умолчанию", "com_ui_fork_default": "Использовать вариант по умолчанию", "com_ui_fork_error": "Произошла ошибка при создании ответвления разговора", "com_ui_fork_from_message": "Выберите вариант ответвления", "com_ui_fork_info_1": "Используйте эту настройку для разделения сообщений с нужным поведением.", "com_ui_fork_info_2": "\"Форкинг\" означает создание новой ветви разговора, которая начинается или заканчивается на определенных сообщениях текущего разговора, создавая копию в соответствии с выбранными параметрами.", "com_ui_fork_info_3": "\"Целевое сообщение\" относится либо к сообщению, из которого было открыто это всплывающее окно, либо, если вы отметите \"{{0}}\", к последнему сообщению в диалоге.", "com_ui_fork_info_branches": "Эта опция создает ветвление видимых сообщений вместе со связанными ветвями; другими словами, прямой путь к целевому сообщению, включая ветви на этом пути.", "com_ui_fork_info_button_label": "Просмотреть информацию о разветвлении диалогов", "com_ui_fork_info_remember": "Отметьте это, чтобы запомнить выбранные вами параметры для будущего использования, что позволит быстрее создавать ответвления бесед по вашим предпочтениям.", "com_ui_fork_info_start": "Если отмечено, ветвление начнется с этого сообщения до последнего сообщения в разговоре в соответствии с выбранным выше поведением.", "com_ui_fork_info_target": "Эта опция создает ветвление всех сообщений, ведущих к целевому сообщению, включая соседние. Другими словами, включаются все ветви сообщений, независимо от того, видны они или находятся по одному пути.", "com_ui_fork_info_visible": "Эта опция создает ветвь только для видимых сообщений, то есть прямой путь к целевому сообщению без боковых ветвей.", "com_ui_fork_more_details_about": "Просмотреть дополнительную информацию и сведения о варианте разветвления «{{0}}»", "com_ui_fork_more_info_options": "Просмотреть подробное описание всех вариантов разветвления и их поведения", "com_ui_fork_processing": "Разветвление беседы...", "com_ui_fork_remember": "Запомнить", "com_ui_fork_remember_checked": "Ваш выбор будет сохранен после использования. Вы можете изменить его в любое время в настройках.", "com_ui_fork_split_target": "Начать ветвление здесь", "com_ui_fork_split_target_setting": "По умолчанию создавать ветку от целевого сообщения", "com_ui_fork_success": "Разветвление беседы успешно выполнено", "com_ui_fork_visible": "Только видимые сообщения", "com_ui_generate_backup": "Создать резервные коды", "com_ui_generate_qrcode": "Сгенерировать QR-код", "com_ui_generating": "Генерация...", "com_ui_getting_started": "Начало работы", "com_ui_go_back": "Назад", "com_ui_go_to_conversation": "Перейти к беседе", "com_ui_good_afternoon": "Добрый день", "com_ui_good_evening": "Добрый вечер", "com_ui_good_morning": "Доброе утро", "com_ui_happy_birthday": "Это мой первый день рождения!", "com_ui_hide_qr": "Скрыть QR код", "com_ui_host": "Хо<PERSON>т", "com_ui_idea": "Идеи", "com_ui_image_created": "Изображение создано", "com_ui_image_edited": "Изображение обновлено", "com_ui_image_gen": "Генератор изображений", "com_ui_import": "Импорт", "com_ui_import_conversation_error": "При импорте бесед произошла ошибка", "com_ui_import_conversation_file_type_error": "Неподдерживаемый тип импорта", "com_ui_import_conversation_info": "Импортировать беседы из файла JSON", "com_ui_import_conversation_success": "Беседы успешно импортированы", "com_ui_include_shadcnui": "Включить компоненты shadcn/ui", "com_ui_input": "Ввод", "com_ui_instructions": "Инструкции", "com_ui_late_night": "Доброй ночи", "com_ui_latest_footer": "Искусственный интеллект для каждого", "com_ui_latest_production_version": "Последняя рабочая версия", "com_ui_latest_version": "Последняя версия", "com_ui_librechat_code_api_key": "Получить ключ API интерпретатора кода LibreChat", "com_ui_librechat_code_api_subtitle": "Безопасно. Многоязычно. Работа с файлами.", "com_ui_librechat_code_api_title": "Запустить AI-код", "com_ui_loading": "Загрузка...", "com_ui_locked": "Заблокировано", "com_ui_logo": "Логотип {{0}}", "com_ui_manage": "Управление", "com_ui_max_tags": "Максимально допустимое количество - {{0}}, используются последние значения.", "com_ui_mcp_servers": "MCP серверы", "com_ui_mention": "Упомянуть конечную точку, помощника или предустановку для быстрого переключения", "com_ui_min_tags": "Нельзя удалить больше значений, требуется минимум {{0}}.", "com_ui_misc": "Разное", "com_ui_model": "Модель", "com_ui_model_parameters": "Параметры модели", "com_ui_more_info": "Подробнее", "com_ui_my_prompts": "Мои промты", "com_ui_name": "Имя", "com_ui_new": "Новый", "com_ui_new_chat": "Создать чат", "com_ui_new_conversation_title": "Название нового чата", "com_ui_next": "Следующий", "com_ui_no": "Нет", "com_ui_no_backup_codes": "Резервные коды отсутствуют. Сгенерируйте новые", "com_ui_no_bookmarks": "Похоже, у вас пока нет закладок. Выберите чат и добавьте новую закладку", "com_ui_no_category": "Без категории", "com_ui_no_changes": "Нет изменений для обновления", "com_ui_no_terms_content": "Нет содержания условий использования для отображения", "com_ui_none": "Пусто", "com_ui_not_used": "Не используется", "com_ui_nothing_found": "Ничего не найдено", "com_ui_oauth": "OAuth", "com_ui_of": "из", "com_ui_off": "Выкл.", "com_ui_on": "<PERSON><PERSON><PERSON>.", "com_ui_openai": "OpenAI", "com_ui_page": "Страница", "com_ui_prev": "Предыдущий", "com_ui_preview": "Предпросмотр", "com_ui_privacy_policy": "Политика конфиденциальности", "com_ui_privacy_policy_url": "Ссылка на политику конфиденциальности", "com_ui_prompt": "Промт", "com_ui_prompt_already_shared_to_all": "Этот промт уже доступен всем пользователям", "com_ui_prompt_name": "Название промта", "com_ui_prompt_name_required": "Необходимо указать название промта", "com_ui_prompt_preview_not_shared": "Автор не разрешил совместную работу с этим промтом", "com_ui_prompt_text": "Текст", "com_ui_prompt_text_required": "Введите текст", "com_ui_prompt_update_error": "Произошла ошибка при обновлении промта", "com_ui_prompts": "Промты", "com_ui_prompts_allow_create": "Разрешить создание промтов", "com_ui_prompts_allow_share_global": "Разрешить доступ к промптам всем пользователям", "com_ui_prompts_allow_use": "Разрешить использование промтов", "com_ui_provider": "Провайдер", "com_ui_read_aloud": "Прочитать вслух", "com_ui_redirecting_to_provider": "Перенаправление на {{0}}, пожалуйста, подождите...", "com_ui_refresh_link": "Обновить ссылку", "com_ui_regenerate": "Повторная генерация", "com_ui_regenerate_backup": "Сгенерировать резервные коды заново", "com_ui_regenerating": "Повторная генерация...", "com_ui_region": "Регион", "com_ui_rename": "Переименовать", "com_ui_rename_conversation": "Переименовать чат", "com_ui_rename_failed": "Не удалось переименовать чат", "com_ui_rename_prompt": "Переименовать промт", "com_ui_requires_auth": "Требуется аутентификация", "com_ui_reset_var": "Сбросить {{0}}", "com_ui_result": "Результат", "com_ui_revoke": "Отозвать", "com_ui_revoke_info": "Отозвать все предоставленные учетные данные", "com_ui_revoke_key_confirm": "Вы действительно хотите отозвать этот ключ?", "com_ui_revoke_key_endpoint": "Отозвать ключ для {{0}}", "com_ui_revoke_keys": "Отозвать ключи", "com_ui_revoke_keys_confirm": "Вы действительно хотите отозвать все ключи?", "com_ui_role_select": "Роль", "com_ui_roleplay": "Ролевой режим", "com_ui_run_code": "Выполнить код", "com_ui_run_code_error": "Произошла ошибка при выполнении кода", "com_ui_save": "Сохранить", "com_ui_save_badge_changes": "Сохранить изменения значков?", "com_ui_save_submit": "Сохранить и отправить", "com_ui_saved": "Сохранено!", "com_ui_schema": "Схема", "com_ui_search": "Поиск", "com_ui_secret_key": "Секретный ключ", "com_ui_select": "Выбрать", "com_ui_select_file": "Выберите файл", "com_ui_select_model": "Выберите модель", "com_ui_select_provider": "Выберите провайдера", "com_ui_select_provider_first": "Сначала выберите провайдера", "com_ui_select_region": "Выберите регион", "com_ui_select_search_model": "Поиск модели по названию", "com_ui_select_search_plugin": "Поиск плагина по названию", "com_ui_select_search_provider": "Поиск провайдера по названию", "com_ui_select_search_region": "Поиск региона по названию", "com_ui_share": "Поделиться", "com_ui_share_create_message": "Ваше имя и любые сообщения, которые вы добавите после обмена, останутся конфиденциальными.", "com_ui_share_delete_error": "Произошла ошибка при удалении общей ссылки.", "com_ui_share_error": "Произошла ошибка при попытке поделиться ссылкой на чат", "com_ui_share_link_to_chat": "Поделиться ссылкой в чате", "com_ui_share_to_all_users": "Поделиться со всеми пользователями", "com_ui_share_update_message": "Ваше имя, пользовательские инструкции и любые сообщения, которые вы добавите после обмена, останутся конфиденциальными.", "com_ui_share_var": "Поделиться {{0}}", "com_ui_shared_link_bulk_delete_success": "Общие ссылки успешно удалены", "com_ui_shared_link_delete_success": "Общая ссылка успешно удалена", "com_ui_shared_link_not_found": "Общая ссылка не найдена", "com_ui_shared_prompts": "Общие промты", "com_ui_shop": "Покупки", "com_ui_show": "Показать", "com_ui_show_all": "Показать все", "com_ui_show_qr": "Показать QR код", "com_ui_sign_in_to_domain": "Вход в {{0}}", "com_ui_simple": "Простой", "com_ui_size": "Размер", "com_ui_special_var_current_date": "Текущая дата", "com_ui_special_var_current_datetime": "Текущая дата и время", "com_ui_special_var_current_user": "Текущий пользователь", "com_ui_special_var_iso_datetime": "Дата и время в формате UTC ISO", "com_ui_special_variables": "Специальные переменные:", "com_ui_special_variables_more_info": "Вы можете выбрать специальные переменные из выпадающего списка: `{{current_date}}` (сегодняшняя дата и день недели), `{{current_datetime}}` (местные дата и время), `{{utc_iso_datetime}}` (дата и время в формате UTC ISO), `{{current_user}}` (имя вашего аккаунта).", "com_ui_speech_while_submitting": "Невозможно отправить голосовой ввод во время генерации ответа", "com_ui_sr_actions_menu": "Открыть меню действий для \"{{0}}\"", "com_ui_stop": "Остановить генерацию", "com_ui_storage": "Храни<PERSON><PERSON><PERSON>е", "com_ui_submit": "Отправить", "com_ui_teach_or_explain": "Обучение", "com_ui_temporary": "Временный чат", "com_ui_terms_and_conditions": "Условия использования", "com_ui_terms_of_service": "Условия использования", "com_ui_thinking": "Рассуждаю...", "com_ui_thoughts": "Мысли", "com_ui_token_exchange_method": "Метод обмена токена", "com_ui_token_url": "URL токена", "com_ui_tools": "Инструменты", "com_ui_travel": "Путешествия", "com_ui_unarchive": "разархивировать", "com_ui_unarchive_error": "Не удалось восстановить чат из архива", "com_ui_unknown": "Неизвестно", "com_ui_untitled": "Без названия", "com_ui_update": "Обновить", "com_ui_upload": "Загрузить", "com_ui_upload_code_files": "Загрузить для Интерпретатора кода", "com_ui_upload_delay": "Загрузка \"{{0}}\" занимает больше времени, чем ожидалось. Пожалуйста, подождите, пока файл полностью проиндексируется для доступа.", "com_ui_upload_error": "Произошла ошибка при загрузке вашего файла", "com_ui_upload_file_context": "Загрузить файл контекста", "com_ui_upload_file_search": "Загрузить для поиска по файлам", "com_ui_upload_files": "Загрузить файлы", "com_ui_upload_image": "Загрузить изображение", "com_ui_upload_image_input": "Загрузить изображение", "com_ui_upload_invalid": "Недопустимый файл для загрузки. Загружаемое изображение не должно превышать установленный размер", "com_ui_upload_invalid_var": "Недопустимый файл. Загружаемое изображение не должно превышать {{0}} МБ", "com_ui_upload_ocr_text": "Загрузить как текст", "com_ui_upload_success": "Файл успешно загружен", "com_ui_upload_type": "Выберите тип загрузки", "com_ui_use_2fa_code": "Использовать код 2FA вместо этого", "com_ui_use_backup_code": "Использовать резервный код вместо этого", "com_ui_use_micrphone": "Использовать микрофон", "com_ui_used": "Использован", "com_ui_variables": "Переменные", "com_ui_variables_info": "Используйте двойные фигурные скобки в тексте для создания переменных, например `{{пример переменной}}`, чтобы заполнить их позже при использовании промта.", "com_ui_verify": "Проверить", "com_ui_version_var": "Версия {{0}}", "com_ui_versions": "Версии", "com_ui_view_source": "Просмотреть исходный чат", "com_ui_weekend_morning": "Хороших выходных", "com_ui_write": "Письмо", "com_ui_x_selected": "{{0}} выбрано", "com_ui_yes": "Да", "com_ui_zoom": "Масш<PERSON><PERSON><PERSON>", "com_user_message": "Вы"}