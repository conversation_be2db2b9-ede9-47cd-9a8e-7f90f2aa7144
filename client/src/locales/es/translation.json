{"chat_direction_left_to_right": "algo debería ir aquí pero está vacío", "chat_direction_right_to_left": "algo debería ir aquí pero está vacío", "com_a11y_ai_composing": "La IA está componiendo la respuesta", "com_a11y_end": "La IA ha finalizado su respuesta", "com_a11y_start": "La IA ha comenzado su respuesta", "com_agents_allow_editing": "Permitir que otros usuarios editen su agente", "com_agents_by_librechat": "por LibreChat", "com_agents_code_interpreter": "Cuando está habilitado, permite que su agente utilice la API del Intérprete de Código de LibreChat para ejecutar código generado de manera segura, incluyendo el procesamiento de archivos. Requiere una clave de API válida.", "com_agents_code_interpreter_title": "API del Intérprete de Código", "com_agents_create_error": "Hubo un error al crear su agente.", "com_agents_description_placeholder": "Opcional: Describa su Agente aquí", "com_agents_enable_file_search": "Habilitar búsqueda de archivos", "com_agents_file_context": "Archivo de contexto (OCR)", "com_agents_file_context_disabled": "Es necesario crear el Agente antes de subir archivos.", "com_agents_file_search_disabled": "Es necesario crear el Agente antes de subir archivos para la Búsqueda de Archivos.", "com_agents_file_search_info": "<PERSON>uando está habilitado, se informará al agente sobre los nombres exactos de los archivos listados a continuación, permitiéndole recuperar el contexto relevante de estos archivos.", "com_agents_instructions_placeholder": "Las instrucciones del sistema que utiliza el agente", "com_agents_mcp_description_placeholder": "Explica que hace en pocas palabras", "com_agents_mcp_icon_size": "Tamaño minimo 128 x128 px", "com_agents_mcp_trust_subtext": "LibreChat no verifica los conectores personalizados", "com_agents_missing_provider_model": "Por favor, seleccione un proveedor y un modelo antes de crear un agente.", "com_agents_name_placeholder": "Opcional: El nombre del agente", "com_agents_no_access": "No tiene acceso para editar este agente", "com_agents_not_available": "Agente no disponible", "com_agents_search_name": "Buscar agentes por nombre", "com_agents_update_error": "Hubo un error al actualizar su agente.", "com_assistants_action_attempt": "El asistente quiere hablar con {{0}}", "com_assistants_actions": "Acciones", "com_assistants_actions_disabled": "Necesita crear un asistente antes de añadir acciones.", "com_assistants_actions_info": "Permita que su Asistente recupere información o realice acciones a través de API's", "com_assistants_add_actions": "<PERSON><PERSON><PERSON>", "com_assistants_add_tools": "<PERSON><PERSON><PERSON>", "com_assistants_allow_sites_you_trust": "Solo permite sitios en los que confíes.", "com_assistants_append_date": "<PERSON><PERSON><PERSON> y Hora Actual", "com_assistants_append_date_tooltip": "<PERSON>uando está habilitado, la fecha y hora actual del cliente se adjuntarán a las instrucciones del sistema del asistente.", "com_assistants_attempt_info": "El asistente quiere enviar lo siguiente:", "com_assistants_available_actions": "Acciones Disponibles", "com_assistants_capabilities": "Capacidades", "com_assistants_code_interpreter": "Intérprete de Código", "com_assistants_code_interpreter_files": "Los siguientes archivos solo están disponibles para el Intérprete de Código:", "com_assistants_code_interpreter_info": "El Intérprete de Código permite al asistente escribir y ejecutar código. Esta herramienta puede procesar archivos con diversos formatos y datos, y generar archivos como gráficos.", "com_assistants_completed_action": "Hablé con {{0}}", "com_assistants_completed_function": "Eje<PERSON>é {{0}}", "com_assistants_conversation_starters": "Iniciadores de conversación", "com_assistants_conversation_starters_placeholder": "Ingrese un iniciador de conversación", "com_assistants_create_error": "Hubo un error al crear su asistente.", "com_assistants_create_success": "Creado con éxito", "com_assistants_delete_actions_error": "Hubo un error al eliminar la acción.", "com_assistants_delete_actions_success": "Acción eliminada del Asistente con éxito", "com_assistants_description_placeholder": "Opcional: Describa su Asistente aquí", "com_assistants_domain_info": "El Asistente envió esta información a {{0}}", "com_assistants_file_search": "Búsqueda de Archivos", "com_assistants_file_search_info": "Adjuntar almacenes vectoriales para la Búsqueda de Archivos aún no está soportado. Puede adjuntarlos desde el Área de Pruebas del Proveedor o adjuntar archivos a los mensajes para la búsqueda de archivos en una conversación específica.", "com_assistants_function_use": "El Asistente usó {{0}}", "com_assistants_image_vision": "Visión de Imagen", "com_assistants_instructions_placeholder": "Las instrucciones del sistema que utiliza el asistente", "com_assistants_knowledge": "Conocimiento", "com_assistants_knowledge_disabled": "El Asistente debe ser creado, y el Intérprete de Código o la Recuperación deben estar habilitados y guardados antes de subir archivos como Conocimiento.", "com_assistants_knowledge_info": "Si sube archivos en Conocimiento, las conversaciones con su Asistente pueden incluir el contenido de los archivos.", "com_assistants_max_starters_reached": "Se alcanzó el número máximo de iniciadores de conversación", "com_assistants_name_placeholder": "Opcional: El nombre del asistente", "com_assistants_non_retrieval_model": "La búsqueda de archivos no está habilitada en este modelo. Por favor, seleccione otro modelo.", "com_assistants_retrieval": "Recuperación", "com_assistants_running_action": "Ejecutando acción", "com_assistants_search_name": "Buscar asistentes por nombre", "com_assistants_update_actions_error": "Hubo un error al crear o actualizar la acción.", "com_assistants_update_actions_success": "Acción creada o actualizada con éxito", "com_assistants_update_error": "Hubo un error al actualizar su asistente.", "com_assistants_update_success": "Actualizado con éxito", "com_auth_already_have_account": "¿Ya tiene una cuenta?", "com_auth_apple_login": "Inicia con <PERSON>", "com_auth_back_to_login": "Volver al inicio de sesión", "com_auth_click": "Haga clic", "com_auth_click_here": "Haz clic aquí", "com_auth_continue": "<PERSON><PERSON><PERSON><PERSON>", "com_auth_create_account": "<PERSON><PERSON>r su cuenta", "com_auth_discord_login": "Continuar con <PERSON>rd", "com_auth_email": "Correo electrónico", "com_auth_email_address": "Dirección de correo electrónico", "com_auth_email_max_length": "El correo electrónico no debe tener más de 120 caracteres", "com_auth_email_min_length": "El correo electrónico debe tener al menos 6 caracteres", "com_auth_email_pattern": "Debe ingresar una dirección de correo electrónico válida", "com_auth_email_required": "Se requiere correo electrónico", "com_auth_email_resend_link": "Reenviar correo electrónico", "com_auth_email_resent_failed": "No se pudo reenviar el correo electrónico de verificación", "com_auth_email_resent_success": "El correo electrónico de verificación ha sido reenviado exitosamente", "com_auth_email_verification_failed": "La verificación del correo electrónico ha fallado", "com_auth_email_verification_failed_token_missing": "La verificación falló, falta el token de seguridad", "com_auth_email_verification_in_progress": "Verificando su correo electrónico, por favor espere", "com_auth_email_verification_invalid": "Verificación de correo electrónico no válida", "com_auth_email_verification_redirecting": "Redirigiendo en {{0}} segundos...", "com_auth_email_verification_resend_prompt": "¿No recibió el correo electrónico?", "com_auth_email_verification_success": "Correo electrónico verificado exitosamente", "com_auth_email_verifying_ellipsis": "Verificando...", "com_auth_error_create": "Hubo un error al intentar registrar su cuenta. Inténtelo de nuevo.", "com_auth_error_invalid_reset_token": "Este token de restablecimiento de contraseña ya no es válido.", "com_auth_error_login": "No se puede iniciar sesión con la información proporcionada. Verifique sus credenciales y vuelva a intentarlo.", "com_auth_error_login_ban": "Su cuenta ha sido bloqueada temporalmente debido a violaciones de nuestro servicio.", "com_auth_error_login_rl": "Demasiados intentos de inicio de sesión en un corto período de tiempo. Inténtelo de nuevo más tarde.", "com_auth_error_login_server": "Hubo un error interno del servidor. Espere unos momentos y vuelva a intentarlo.", "com_auth_error_login_unverified": "Su cuenta no ha sido verificada. Por favor, revise su correo electrónico para encontrar el enlace de verificación.", "com_auth_facebook_login": "Continuar con Facebook", "com_auth_full_name": "Nombre completo", "com_auth_github_login": "<PERSON><PERSON><PERSON><PERSON> con <PERSON>", "com_auth_google_login": "Continuar con <PERSON>", "com_auth_here": "AQUÍ", "com_auth_login": "In<PERSON><PERSON>", "com_auth_login_with_new_password": "Ahora puede iniciar sesión con su nueva contraseña.", "com_auth_name_max_length": "El nombre debe tener menos de 80 caracteres", "com_auth_name_min_length": "El nombre debe tener al menos 3 caracteres", "com_auth_name_required": "Se requiere nombre", "com_auth_no_account": "¿No tiene una cuenta?", "com_auth_password": "Contraseña", "com_auth_password_confirm": "Confirmar con<PERSON>", "com_auth_password_forgot": "¿Olvidó su contraseña?", "com_auth_password_max_length": "La contraseña debe tener menos de 128 caracteres", "com_auth_password_min_length": "La contraseña debe tener al menos 8 caracteres", "com_auth_password_not_match": "Las contraseñas no coinciden", "com_auth_password_required": "Se requiere contraseña", "com_auth_registration_success_generic": "Por favor, revise su correo electrónico para verificar su dirección.", "com_auth_registration_success_insecure": "Registro completado exitosamente.", "com_auth_reset_password": "Restablecer su contraseña", "com_auth_reset_password_if_email_exists": "Si existe una cuenta con ese correo electrónico, se le ha enviado un mensaje con instrucciones para restablecer su contraseña. Por favor, asegúrese de revisar su carpeta de correo no deseado.", "com_auth_reset_password_link_sent": "Correo electrónico enviado", "com_auth_reset_password_success": "Éxito al restablecer la contraseña", "com_auth_sign_in": "In<PERSON><PERSON>", "com_auth_sign_up": "Regístrese", "com_auth_submit_registration": "Enviar registro", "com_auth_to_reset_your_password": "para restablecer su contraseña.", "com_auth_to_try_again": "para intentar de nuevo.", "com_auth_two_factor": "Revisa tu aplicación preferida de OTP para obtener el código", "com_auth_username": "Nombre de usuario (opcional)", "com_auth_username_max_length": "El nombre de usuario debe tener menos de 20 caracteres", "com_auth_username_min_length": "El nombre de usuario debe tener al menos 2 caracteres", "com_auth_verify_your_identity": "Verifica Tu Identidad", "com_auth_welcome_back": "Bienvenido de nuevo", "com_click_to_download": "(haga clic aquí para descargar)", "com_download_expired": "Descarga expirada", "com_download_expires": "(haga clic aquí para descargar - expira el {{0}})", "com_endpoint": "Endpoint", "com_endpoint_agent": "<PERSON><PERSON>", "com_endpoint_agent_model": "<PERSON><PERSON> de agente (Recomendado: GPT-3.5)", "com_endpoint_agent_placeholder": "Por favor seleccione un Agente", "com_endpoint_ai": "IA", "com_endpoint_anthropic_maxoutputtokens": "Número máximo de tokens que se pueden generar en la respuesta. Especifique un valor más bajo para respuestas más cortas y un valor más alto para respuestas más largas.", "com_endpoint_anthropic_prompt_cache": "El almacenamiento en caché de instrucciones permite reutilizar contextos o instrucciones extensas entre llamadas a la API, reduciendo costos y tiempo de respuesta", "com_endpoint_anthropic_temp": "Rango de 0 a 1. Utilice una temperatura más cercana a 0 para tareas analíticas/de opción múltiple y más cercana a 1 para tareas creativas y generativas. Recomendamos alterar esto o Top P, pero no ambos.", "com_endpoint_anthropic_topk": "Top-k cambia la forma en que el modelo selecciona tokens para la salida. Un top-k de 1 significa que el token seleccionado es el más probable entre todos los tokens en el vocabulario del modelo (también llamado decodificación codiciosa), mientras que un top-k de 3 significa que el siguiente token se selecciona entre los 3 tokens más probables (usando temperatura).", "com_endpoint_anthropic_topp": "Top-p cambia la forma en que el modelo selecciona tokens para la salida. Los tokens se seleccionan desde los más K (ver parámetro topK) probables hasta los menos probables hasta que la suma de sus probabilidades sea igual al valor top-p.", "com_endpoint_assistant": "<PERSON><PERSON><PERSON>", "com_endpoint_assistant_model": "<PERSON>o de asistente", "com_endpoint_assistant_placeholder": "Por favor, seleccione un Asistente desde el panel lateral derecho", "com_endpoint_completion": "Finalización", "com_endpoint_completion_model": "Modelo de finalización (Recomendado: GPT-4)", "com_endpoint_config_click_here": "Haz clic aquí", "com_endpoint_config_google_api_info": "Para obtener tu clave de la API de Lenguaje Generativo (para Gemini),", "com_endpoint_config_google_api_key": "Clave API de Google", "com_endpoint_config_google_cloud_platform": "(de Google Cloud Platform)", "com_endpoint_config_google_gemini_api": "(API Gemini)", "com_endpoint_config_google_service_key": "Clave de cuenta de servicio de Google", "com_endpoint_config_key": "Establecer clave API", "com_endpoint_config_key_encryption": "Tu clave será encriptada y eliminada en", "com_endpoint_config_key_for": "Establecer clave API para", "com_endpoint_config_key_google_need_to": "Necesitas", "com_endpoint_config_key_google_service_account": "C<PERSON>r una Cuenta de Servicio", "com_endpoint_config_key_google_vertex_ai": "Habilitar el Vertex AI", "com_endpoint_config_key_google_vertex_api": "API en Google Cloud, luego", "com_endpoint_config_key_google_vertex_api_role": "Asegúrate de hacer clic en 'Crear y continuar' para otorgar al menos el rol de 'Usuario de Vertex AI'. <PERSON><PERSON>ltimo, crea una clave JSON para importar aquí.", "com_endpoint_config_key_import_json_key": "Importar clave JSON de cuenta de servicio.", "com_endpoint_config_key_import_json_key_invalid": "Clave JSON de cuenta de servicio no válida, ¿importaste el archivo correcto?", "com_endpoint_config_key_import_json_key_success": "Clave JSON de cuenta de servicio importada correctamente", "com_endpoint_config_key_name": "Clave", "com_endpoint_config_key_never_expires": "Su clave nunca expirará", "com_endpoint_config_placeholder": "Establezca su clave en el menú del encabezado para chatear.", "com_endpoint_config_value": "Ingresar valor para", "com_endpoint_context": "Contexto", "com_endpoint_context_info": "La cantidad máxima de tokens que se pueden utilizar para el contexto. Utilice esto para controlar cuántos tokens se envían por solicitud. Si no se especifica, se utilizarán los valores predeterminados del sistema basados en el tamaño de contexto conocido de los modelos. Establecer valores más altos puede resultar en errores y/o un mayor costo de tokens.", "com_endpoint_context_tokens": "Máximo de tokens de contexto", "com_endpoint_custom_name": "Nombre personalizado", "com_endpoint_default": "predeterminado", "com_endpoint_default_blank": "predeterminado: en blanco", "com_endpoint_default_empty": "predeterminado: vacío", "com_endpoint_default_with_num": "predeterminado: {{0}}", "com_endpoint_deprecated": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_examples": " Configuraciones preestablecidas", "com_endpoint_export": "Exportar", "com_endpoint_export_share": "Exportar/Compartir", "com_endpoint_frequency_penalty": "Penalización de frecuencia", "com_endpoint_func_hover": "Habilitar el uso de Plugins como funciones de OpenAI", "com_endpoint_google_custom_name_placeholder": "Establecer un nombre personalizado para Google", "com_endpoint_google_maxoutputtokens": "Número máximo de tokens que se pueden generar en la respuesta. Especifique un valor más bajo para respuestas más cortas y un valor más alto para respuestas más largas.", "com_endpoint_google_temp": "Los valores más altos = más aleatorios, mientras que los valores más bajos = más enfocados y deterministas. Recomendamos alterar esto o Top P, pero no ambos.", "com_endpoint_google_topk": "Top-k cambia la forma en que el modelo selecciona tokens para la salida. Un top-k de 1 significa que el token seleccionado es el más probable entre todos los tokens en el vocabulario del modelo (también llamado decodificación codiciosa), mientras que un top-k de 3 significa que el siguiente token se selecciona entre los 3 tokens más probables (usando temperatura).", "com_endpoint_google_topp": "Top-p cambia la forma en que el modelo selecciona tokens para la salida. Los tokens se seleccionan desde los más K (ver parámetro topK) probables hasta los menos probables hasta que la suma de sus probabilidades sea igual al valor top-p.", "com_endpoint_instructions_assistants": "Anular instrucciones", "com_endpoint_instructions_assistants_placeholder": "Anula las instrucciones del asistente. Esto es útil para modificar el comportamiento por ejecución.", "com_endpoint_max_output_tokens": "Tokens de Salida Máximos", "com_endpoint_message": "Men<PERSON><PERSON>", "com_endpoint_message_new": "Men<PERSON>je {{0}}", "com_endpoint_message_not_appendable": "Edita tu mensaje o regénera.", "com_endpoint_my_preset": "Mi configuración preestablecida", "com_endpoint_no_presets": "Aún no hay configuraciones preestablecidas, utiliza el botón de configuración para crear una", "com_endpoint_open_menu": "<PERSON><PERSON><PERSON>", "com_endpoint_openai_custom_name_placeholder": "Establecer un nombre personalizado para ChatGPT", "com_endpoint_openai_detail": "La resolución para las solicitudes de Vision. \"Baja\" es más económica y rápida, \"Alta\" es más detallada y costosa, y \"Automática\" elegirá automáticamente entre las dos en función de la resolución de la imagen.", "com_endpoint_openai_freq": "Número entre -2.0 y 2.0. Los valores positivos penalizan los nuevos tokens basados en su frecuencia existente en el texto hasta el momento, disminuyendo la probabilidad del modelo de repetir la misma línea textualmente.", "com_endpoint_openai_max": "Los tokens máximos a generar. La longitud total de los tokens de entrada y los tokens generados está limitada por la longitud del contexto del modelo.", "com_endpoint_openai_max_tokens": "Campo opcional `max_tokens`, que representa el número máximo de tokens que se pueden generar en la finalización del chat.\n\nLa longitud total de los tokens de entrada y los tokens generados está limitada por la longitud del contexto del modelo. Puede experimentar errores si este número excede los tokens máximos de contexto.", "com_endpoint_openai_pres": "Número entre -2.0 y 2.0. Los valores positivos penalizan los nuevos tokens basados en si aparecen o no en el texto hasta el momento, aumentando la probabilidad del modelo de hablar sobre nuevos temas.", "com_endpoint_openai_prompt_prefix_placeholder": "Establecer instrucciones personalizadas para incluir en el Mensaje del sistema. Predeterminado: ninguno", "com_endpoint_openai_resend": "Reenviar todas las imágenes adjuntas previamente. Nota: esto puede aumentar significativamente el costo de tokens y puede experimentar errores con muchos archivos adjuntos de imágenes.", "com_endpoint_openai_resend_files": "Reenviar todos los archivos adjuntos anteriormente. Nota: esto aumentará el costo de tokens y puede experimentar errores con muchos archivos adjuntos.", "com_endpoint_openai_stop": "Hasta 4 secuencias donde la API dejará de generar más tokens.", "com_endpoint_openai_temp": "Los valores más altos = más aleatorios, mientras que los valores más bajos = más enfocados y deterministas. Recomendamos alterar esto o Top P, pero no ambos.", "com_endpoint_openai_topp": "Una alternativa al muestreo con temperatura, llamada muestreo de núcleo, donde el modelo considera los resultados de los tokens con la masa de probabilidad superior al top_p. Entonces, 0.1 significa que solo se consideran los tokens que comprenden la masa de probabilidad superior al 10%. Recomendamos alterar esto o la temperatura, pero no ambos.", "com_endpoint_output": "Salida", "com_endpoint_plug_image_detail": "Detalle de imagen", "com_endpoint_plug_resend_files": "Reenviar archivos", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Establecer instrucciones personalizadas para incluir en el Mensaje del sistema. Predeterminado: ninguno", "com_endpoint_plug_skip_completion": "Omitir finaliza<PERSON>", "com_endpoint_plug_use_functions": "Utilizar funciones", "com_endpoint_presence_penalty": "Penalización de presencia", "com_endpoint_preset": "configuración preestablecida", "com_endpoint_preset_default": "es ahora la configuración preestablecida predeterminada.", "com_endpoint_preset_default_item": "Predeterminado:", "com_endpoint_preset_default_none": "No hay configuración preestablecida predeterminada activa.", "com_endpoint_preset_default_removed": "ya no es la configuración preestablecida predeterminada.", "com_endpoint_preset_delete_confirm": "¿Estás seguro de que quieres eliminar esta configuración preestablecida?", "com_endpoint_preset_delete_error": "Hubo un error al eliminar tu configuración preestablecida. Por favor, inténtalo de nuevo.", "com_endpoint_preset_import": "¡Configuración preestablecida importada!", "com_endpoint_preset_import_error": "Hubo un error al importar tu configuración preestablecida. Por favor, inténtalo de nuevo.", "com_endpoint_preset_name": "Nombre de la configuración preestablecida", "com_endpoint_preset_save_error": "Hubo un error al guardar tu configuración preestablecida. Por favor, inténtalo de nuevo.", "com_endpoint_preset_selected": "¡Configuración preestablecida activa!", "com_endpoint_preset_selected_title": "¡Activo!", "com_endpoint_preset_title": "Configuración preestablecida", "com_endpoint_presets": "configuraciones preestablecidas", "com_endpoint_presets_clear_warning": "¿Estás seguro de que quieres borrar todas las configuraciones preestablecidas? Esto es irreversible.", "com_endpoint_prompt_cache": "Utilizar caché de instrucciones", "com_endpoint_prompt_prefix": "Instrucciones personalizadas", "com_endpoint_prompt_prefix_assistants": "Instrucciones adicionales", "com_endpoint_prompt_prefix_assistants_placeholder": "Establecer instrucciones o contexto adicionales además de las instrucciones principales del Asistente. Se ignora si está vacío.", "com_endpoint_prompt_prefix_placeholder": "Configurar instrucciones personalizadas o contexto. Se ignora si está vacío.", "com_endpoint_reasoning_effort": "Esfu<PERSON>zo de Razonamiento", "com_endpoint_save_as_preset": "Guardar como configuración preestablecida", "com_endpoint_search": "Buscar punto de conexión por nombre", "com_endpoint_search_models": "Buscar modelos...", "com_endpoint_search_var": "Buscar {{0}}...", "com_endpoint_set_custom_name": "Establece un nombre personalizado, en caso de que puedas encontrar esta configuración preestablecida", "com_endpoint_skip_hover": "Habilitar omitir el paso de finalización, que revisa la respuesta final y los pasos generados", "com_endpoint_stop": "Secuencias de detención", "com_endpoint_stop_placeholder": "Separe los valores presionando `Intro`", "com_endpoint_temperature": "Temperatura", "com_endpoint_thinking": "Pensando", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "Utilizar asistente activo", "com_error_expired_user_key": "La clave proporcionada para {{0}} expiró en {{1}}. Por favor, proporcione una clave nueva e inténtelo de nuevo.", "com_error_files_dupe": "Se detectó un archivo duplicado", "com_error_files_empty": "No se permiten archivos vacíos.", "com_error_files_process": "Se produjo un error al procesar el archivo.", "com_error_files_unsupported_capability": "No hay capacidades habilitadas que admitan este tipo de archivo.", "com_error_files_upload": "Se produjo un error durante la subida del archivo", "com_error_files_upload_canceled": "La solicitud de carga del archivo fue cancelada. Nota: es posible que la carga del archivo aún esté en proceso y necesite ser eliminada manualmente.", "com_error_files_validation": "Se produjo un error durante la validación del archivo.", "com_error_input_length": "El conteo de tokens del último mensaje es demasiado largo y excede el límite permitido ({{0}}). Por favor, acorte su mensaje, ajuste el tamaño máximo del contexto desde los parámetros de conversación, o bifurque la conversación para continuar.", "com_error_invalid_agent_provider": "El proveedor \"{{0}}\" no está disponible para el uso con Agentes. Por favor, ve a las configuraciones de tu agente y selecciona un proveedor que se encuentre disponible.", "com_error_invalid_user_key": "Clave proporcionada no válida. Por favor proporcione una clave válida e inténtelo de nuevo.", "com_error_moderation": "Parece que el contenido enviado ha sido marcado por nuestro sistema de moderación por no estar alineado con nuestras pautas comunitarias. No podemos proceder con este tema específico. Si tiene alguna otra pregunta o tema que le gustaría explorar, por favor edite su mensaje o cree una nueva conversación.", "com_error_no_base_url": "No se encontró URL base. Por favor proporcione una y vuelva a intentarlo.", "com_error_no_user_key": "No se encontró ninguna clave. Por favor, proporcione una clave e inténtelo de nuevo.", "com_files_filter": "Filtrar archivos...", "com_files_no_results": "Sin resultados.", "com_files_number_selected": "{{0}} de {{1}} archivo(s) seleccionado(s)", "com_generated_files": "Archivos generados:", "com_hide_examples": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_2fa": "Autenticación en dos pasos", "com_nav_account_settings": "Configuración de la cuenta", "com_nav_always_make_prod": "Convertir siempre las nuevas versiones en producción", "com_nav_archive_created_at": "CreadoEn", "com_nav_archive_name": "Nombre", "com_nav_archived_chats": "Conversaciones archivadas", "com_nav_at_command": "Comando @", "com_nav_at_command_description": "Alternar comando \"@\" para cambiar entre puntos de conexión, modelos, ajustes predefinidos, etc.", "com_nav_audio_play_error": "Error al reproducir el audio: {{0}}", "com_nav_audio_process_error": "Error al procesar el audio: {{0}}", "com_nav_auto_scroll": "Desplazamiento automático al más reciente al abrir", "com_nav_auto_send_prompts": "Envío automático de mensajes", "com_nav_auto_send_text": "Envío automático de texto", "com_nav_auto_send_text_disabled": "Establecer -1 para deshabilitar", "com_nav_auto_transcribe_audio": "Transcribir audio automáticamente", "com_nav_automatic_playback": "Reproducción automática del último mensaje", "com_nav_balance": "Balance", "com_nav_browser": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_change_picture": "Cambiar imagen", "com_nav_chat_commands": "Comandos de <PERSON>", "com_nav_chat_commands_info": "Estos comandos se activan al escribir caracteres específicos al inicio de su mensaje. Cada comando se activa mediante su prefijo designado. Puede desactivarlos si utiliza frecuentemente estos caracteres para comenzar mensajes.", "com_nav_chat_direction": "Dirección del chat", "com_nav_clear_all_chats": "<PERSON><PERSON><PERSON> todos los chats", "com_nav_clear_cache_confirm_message": "¿Está seguro de que desea borrar el caché?", "com_nav_clear_conversation": "Borrar conversaciones", "com_nav_clear_conversation_confirm_message": "¿Estás seguro de que quieres borrar todas las conversaciones? Esta acción es irreversible.", "com_nav_close_sidebar": "Ce<PERSON>r barra lateral", "com_nav_commands": "<PERSON><PERSON><PERSON>", "com_nav_confirm_clear": "Confirma<PERSON> b<PERSON>", "com_nav_conversation_mode": "Modo de conversación", "com_nav_convo_menu_options": "Opciones del menú de conversación", "com_nav_db_sensitivity": "Sensibilidad en decibelios", "com_nav_delete_account": "Eliminar cuenta", "com_nav_delete_account_button": "Eliminar mi cuenta permanentemente", "com_nav_delete_account_confirm": "Eliminar cuenta - ¿E<PERSON><PERSON> seguro?", "com_nav_delete_account_email_placeholder": "Por favor ingrese el correo electrónico de su cuenta", "com_nav_delete_cache_storage": "Eliminar almacenamiento caché de TTS", "com_nav_delete_data_info": "Se eliminarán todos sus datos", "com_nav_delete_warning": "ADVERTENCIA: Esta acción eliminará su cuenta de forma permanente.", "com_nav_enable_cache_tts": "Habilitar caché de texto a voz", "com_nav_enable_cloud_browser_voice": "Usar voces basadas en la nube", "com_nav_enabled": "Habilitado", "com_nav_engine": "Motor", "com_nav_enter_to_send": "Enviar mensaje con la tecla Enter", "com_nav_export": "Exportar", "com_nav_export_all_message_branches": "Exportar todas las ramas de mensajes", "com_nav_export_conversation": "Exportar conversación", "com_nav_export_filename": "Nombre de archivo", "com_nav_export_filename_placeholder": "Establecer el nombre de archivo", "com_nav_export_include_endpoint_options": "Incluir opciones de punto final", "com_nav_export_recursive": "Recursivo", "com_nav_export_recursive_or_sequential": "¿Recursivo o secuencial?", "com_nav_export_type": "Tipo", "com_nav_external": "Externo", "com_nav_font_size": "Tamaño de fuente", "com_nav_font_size_base": "Mediano", "com_nav_font_size_lg": "Grande", "com_nav_font_size_sm": "Pequeño", "com_nav_font_size_xl": "Extra grande", "com_nav_font_size_xs": "Extra pequeño", "com_nav_help_faq": "Ayuda y preguntas frecuentes", "com_nav_hide_panel": "Ocultar el panel lateral derecho", "com_nav_info_code_artifacts": "Permite mostrar artefactos de código experimentales junto al chat", "com_nav_info_code_artifacts_agent": "Habilita el uso de artefactos de código para este agente. De forma predeterminada, instrucciones adicionales específicas para el uso de artefactos son añadidas, a menos que \"Modo personalizado de Prompt\" esté habilitado.", "com_nav_info_custom_prompt_mode": "Cuando está habilitado, no se incluirá el mensaje del sistema predeterminado para artefactos. En este modo, todas las instrucciones para generar artefactos deberán proporcionarse manualmente.", "com_nav_info_enter_to_send": "Cuando está habilitado, al presionar `ENTER` se enviará su mensaje. Cuando está deshabilitado, al presionar Enter se agregará una nueva línea, y necesitará presionar `CTRL + ENTER` / `⌘ + ENTER` para enviar su mensaje.", "com_nav_info_fork_change_default": "\"Mostrar únicamente mensajes visibles\" incluye solo la ruta directa hacia el mensaje seleccionado. \"Incluir ramas relacionadas\" añade las ramificaciones a lo largo de la ruta. \"Incluir todo desde/hacia aquí\" incluye todos los mensajes y ramificaciones conectados.", "com_nav_info_fork_split_target_setting": "Cuando está habilitado, la bifurcación comenzará desde el mensaje objetivo hasta el último mensaje de la conversación, según el comportamiento seleccionado.", "com_nav_info_include_shadcnui": "Cuando está habilitado, se incluirán instrucciones para el uso de los componentes de shadcn/ui. shadcn/ui es una colección de componentes reutilizables construidos utilizando Radix UI y Tailwind CSS. Nota: estas son instrucciones extensas, debe habilitarlas solo si es importante para usted informar al LLM sobre las importaciones y componentes correctos. Para más información sobre estos componentes, visite: https://ui.shadcn.com/", "com_nav_info_latex_parsing": "Cuando está habilitado, el código LaTeX en los mensajes se mostrará como ecuaciones matemáticas. Si usted no necesita el renderizado de LaTeX, puede deshabilitarlo para mejorar el rendimiento.", "com_nav_info_save_badges_state": "Al habilitar esta opción, se guardará el estado de las insignias del chat. Esto significa que si crea un nuevo chat, las insignias permanecerán en el mismo estado que el chat anterior. Si desactiva esta opción, las insignias se restablecerán a su estado predeterminado cada vez que cree un nuevo chat.", "com_nav_info_save_draft": "Cuando está habilitado, el texto y los archivos adjuntos que ingrese en el formulario de chat se guardarán automáticamente como borradores en su dispositivo local. Estos borradores estarán disponibles incluso si recarga la página o cambia a una conversación diferente. Los borradores se almacenan localmente en su dispositivo y se eliminan una vez que el mensaje es enviado.", "com_nav_info_user_name_display": "<PERSON>uando está habilitado, se mostrará su nombre de usuario sobre cada mensaje que envíe. Cuando está deshabilitado, solo ver<PERSON> \"Usted\" sobre sus mensajes.", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "Detección automática", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_chinese": "中文", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "Idioma", "com_nav_latex_parsing": "<PERSON><PERSON><PERSON> en los mensajes (puede afectar el rendimiento)", "com_nav_log_out": "<PERSON><PERSON><PERSON>", "com_nav_long_audio_warning": "Los textos más extensos tomarán más tiempo en procesarse.", "com_nav_maximize_chat_space": "Maximizar espacio del chat", "com_nav_modular_chat": "Habilitar el cambio de puntos finales en medio de una conversación", "com_nav_my_files": "Mis archivos", "com_nav_not_supported": "No soportado", "com_nav_open_sidebar": "Abrir barra lateral", "com_nav_playback_rate": "Velocidad de reproducción de audio", "com_nav_plugin_auth_error": "Hubo un error al intentar autenticar este plugin. Por favor, inténtalo de nuevo.", "com_nav_plugin_install": "Instalar", "com_nav_plugin_search": "Buscar plugins", "com_nav_plugin_store": "Tienda de plugins", "com_nav_plugin_uninstall": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_plus_command": "+ Comando", "com_nav_plus_command_description": "Alternar comando ' + ' para agregar una configuración de respuestas múltiples", "com_nav_profile_picture": "Imagen de perfil", "com_nav_save_drafts": "Guardar borradores localmente", "com_nav_search_placeholder": "Buscar mensajes", "com_nav_send_message": "<PERSON><PERSON><PERSON> men<PERSON>", "com_nav_setting_account": "C<PERSON><PERSON>", "com_nav_setting_chat": "Configuración del chat", "com_nav_setting_data": "Controles de datos", "com_nav_setting_general": "General", "com_nav_setting_speech": "Voz y habla", "com_nav_settings": "Configuración", "com_nav_shared_links": "Links Compartidos", "com_nav_show_code": "Mostrar siempre el código cuando se use el intérprete de código", "com_nav_slash_command": "Comando /", "com_nav_slash_command_description": "Alternar comando '/' para seleccionar un mensaje predefinido mediante el teclado", "com_nav_speech_to_text": "Voz a texto", "com_nav_stop_generating": "Detener generación", "com_nav_text_to_speech": "Texto a voz", "com_nav_theme": "<PERSON><PERSON>", "com_nav_theme_dark": "Oscuro", "com_nav_theme_light": "<PERSON><PERSON><PERSON>", "com_nav_theme_system": "Sistema", "com_nav_tool_dialog": "Herramientas del asistente", "com_nav_tool_dialog_agents": "Herramientas del agente", "com_nav_tool_dialog_description": "El asistente debe guardarse para que las selecciones de herramientas persistan.", "com_nav_tool_remove": "Eliminar", "com_nav_tool_search": "Buscar herramientas", "com_nav_user": "USUARIO", "com_nav_user_msg_markdown": "Mostrar mensajes de usuario en formato markdown", "com_nav_user_name_display": "Mostrar nombre de usuario en los mensajes", "com_nav_voice_select": "Voz", "com_show_agent_settings": "Mostrar configuración del agente", "com_show_completion_settings": "Mostrar configuración de completado", "com_show_examples": "<PERSON><PERSON> e<PERSON>", "com_sidepanel_agent_builder": "<PERSON><PERSON><PERSON><PERSON>es", "com_sidepanel_assistant_builder": "<PERSON><PERSON><PERSON>", "com_sidepanel_attach_files": "Adjuntar Archivos", "com_sidepanel_conversation_tags": "Marcadores", "com_sidepanel_hide_panel": "Ocultar Panel", "com_sidepanel_manage_files": "Administrar Archivos", "com_sidepanel_parameters": "Parámetros", "com_ui_2fa_disable": "Deshabilitar 2FA", "com_ui_2fa_disable_error": "Hubo en error deshabilitando la autenticación en dos pasos", "com_ui_2fa_enable": "Activa 2FA", "com_ui_2fa_enabled": "2FA ha sido activada", "com_ui_accept": "Acepto", "com_ui_add": "Agregar", "com_ui_add_model_preset": "Agregar un modelo o configuración preestablecida para una respuesta adicional", "com_ui_add_multi_conversation": "Agregar múltiples conversaciones", "com_ui_admin": "Administrador", "com_ui_admin_access_warning": "Deshabilitar el acceso de Administrador a esta función puede causar problemas inesperados en la interfaz que requieran actualizar la página. Si se guarda este cambio, la única forma de revertirlo es mediante la configuración de interfaz en el archivo librechat.yaml, lo cual afectará a todos los roles.", "com_ui_admin_settings": "Configuración de Administrador", "com_ui_advanced": "<PERSON><PERSON><PERSON>", "com_ui_advanced_settings": "Configuración avanzada", "com_ui_agent": "<PERSON><PERSON>", "com_ui_agent_delete_error": "Se produjo un error al eliminar el agente", "com_ui_agent_deleted": "Asistente eliminado exitosamente", "com_ui_agent_duplicate_error": "Se produjo un error al duplicar el asistente", "com_ui_agent_duplicated": "Agente duplicado exitosamente", "com_ui_agent_editing_allowed": "Otros usuarios ya pueden editar este agente", "com_ui_agent_var": "{{0}} agente", "com_ui_agents": "<PERSON><PERSON>", "com_ui_agents_allow_create": "Permitir la creación de Agentes", "com_ui_agents_allow_share_global": "Permitir compartir Agentes con todos los usuarios", "com_ui_agents_allow_use": "Permitir el uso de Agentes", "com_ui_all": "todas", "com_ui_all_proper": "Todos", "com_ui_analyzing": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_analyzing_finished": "Acabando el análisis", "com_ui_archive": "Archivar", "com_ui_archive_error": "Error al archivar la conversación", "com_ui_artifact_click": "Haga clic para abrir", "com_ui_artifacts": "Artefactos", "com_ui_artifacts_toggle": "Alternar Interfaz de Artefactos", "com_ui_ascending": "Asc", "com_ui_assistant": "<PERSON><PERSON><PERSON>", "com_ui_assistant_delete_error": "Hubo un error al eliminar el asistente", "com_ui_assistant_deleted": "Asistente eliminado con éxito", "com_ui_assistants": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_assistants_output": "Salida de Asistentes", "com_ui_attach_error": "No se puede adjuntar el archivo. Cree o seleccione una conversación, o intente actualizar la página.", "com_ui_attach_error_openai": "No se pueden adjuntar archivos del Asistente a otros puntos de conexión", "com_ui_attach_error_size": "Se excedió el límite de tamaño de archivo para el endpoint:", "com_ui_attach_error_type": "Tipo de archivo no admitido para el endpoint:", "com_ui_attach_remove": "Eliminar archivo", "com_ui_attach_warn_endpoint": "Es posible que los archivos no compatibles con la herramienta sean ignorados", "com_ui_attachment": "Adjunto", "com_ui_authentication": "Autenticación", "com_ui_avatar": "Avatar", "com_ui_back_to_chat": "Volver al Chat", "com_ui_back_to_prompts": "Volver a Prompts", "com_ui_bookmark_delete_confirm": "¿Está seguro de que desea eliminar este marcador?", "com_ui_bookmarks": "Marcadores", "com_ui_bookmarks_add": "Agregar <PERSON>", "com_ui_bookmarks_add_to_conversation": "Agregar a la conversación actual", "com_ui_bookmarks_count": "Conteo", "com_ui_bookmarks_create_error": "Hubo un error al crear el marcador", "com_ui_bookmarks_create_exists": "Este marcador ya existe", "com_ui_bookmarks_create_success": "Marcador creado con éxito", "com_ui_bookmarks_delete": "Eliminar Marc<PERSON>", "com_ui_bookmarks_delete_error": "Hubo un error al eliminar el marcador", "com_ui_bookmarks_delete_success": "Marcador eliminado con éxito", "com_ui_bookmarks_description": "Descripción", "com_ui_bookmarks_edit": "<PERSON><PERSON>", "com_ui_bookmarks_filter": "Filtrar marcadores...", "com_ui_bookmarks_new": "Nuevo marcador", "com_ui_bookmarks_title": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmarks_update_error": "Hubo un error al actualizar el marcador", "com_ui_bookmarks_update_success": "Marcador actualizado con éxito", "com_ui_cancel": "<PERSON><PERSON><PERSON>", "com_ui_chat": "Cha<PERSON>", "com_ui_chat_history": "<PERSON><PERSON> <PERSON>", "com_ui_clear": "Limpiar", "com_ui_clear_all": "<PERSON><PERSON><PERSON> todo", "com_ui_close": "<PERSON><PERSON><PERSON>", "com_ui_close_menu": "<PERSON><PERSON><PERSON>", "com_ui_code": "Código", "com_ui_collapse_chat": "<PERSON><PERSON><PERSON>", "com_ui_command_placeholder": "Opcional: Ingrese un comando para el prompt o se utilizará el nombre", "com_ui_command_usage_placeholder": "Seleccione un Prompt por comando o nombre", "com_ui_confirm_action": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_confirm_change": "Confirmar cambio", "com_ui_context": "Contexto", "com_ui_continue": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_controls": "Controles", "com_ui_copied": "¡Copiado!", "com_ui_copied_to_clipboard": "Copiado al portapapeles", "com_ui_copy_code": "<PERSON><PERSON>r c<PERSON>", "com_ui_copy_link": "<PERSON><PERSON><PERSON> enlace", "com_ui_copy_to_clipboard": "Copiar al portapapeles", "com_ui_create": "<PERSON><PERSON><PERSON>", "com_ui_create_link": "<PERSON><PERSON><PERSON> enlace", "com_ui_create_prompt": "<PERSON><PERSON><PERSON> Prompt", "com_ui_custom_prompt_mode": "Modo de Prompt Personalizado", "com_ui_dashboard": "Panel de control", "com_ui_date": "<PERSON><PERSON>", "com_ui_date_april": "Abril", "com_ui_date_august": "Agosto", "com_ui_date_december": "Diciembre", "com_ui_date_february": "<PERSON><PERSON><PERSON>", "com_ui_date_january": "<PERSON><PERSON>", "com_ui_date_july": "<PERSON>", "com_ui_date_june": "<PERSON><PERSON>", "com_ui_date_march": "<PERSON><PERSON>", "com_ui_date_may": "Mayo", "com_ui_date_november": "Noviembre", "com_ui_date_october": "Octubre", "com_ui_date_previous_30_days": "Últimos 30 días", "com_ui_date_previous_7_days": "Últimos 7 días", "com_ui_date_september": "Septiembre", "com_ui_date_today": "Hoy", "com_ui_date_yesterday": "Ayer", "com_ui_decline": "No acepto", "com_ui_delete": "Eliminar", "com_ui_delete_action": "Eliminar Acción", "com_ui_delete_action_confirm": "¿Está seguro de que desea eliminar esta acción?", "com_ui_delete_agent_confirm": "¿Está seguro de que desea eliminar este agente?", "com_ui_delete_assistant_confirm": "¿Está seguro de que desea eliminar este Asistente? Esta acción no se puede deshacer.", "com_ui_delete_confirm": "Esto eliminará", "com_ui_delete_confirm_prompt_version_var": "Esto eliminará la versión seleccionada para \"{{0}}\". Si no existen otras versiones, el prompt será eliminado.", "com_ui_delete_conversation": "¿Eliminar Chat?", "com_ui_delete_prompt": "¿Eliminar Prompt?", "com_ui_delete_shared_link": "¿Eliminar enlace compartido?", "com_ui_delete_tool": "Elimina<PERSON>", "com_ui_delete_tool_confirm": "¿Está seguro de que desea eliminar esta herramienta?", "com_ui_descending": "Desc", "com_ui_description": "Descripción", "com_ui_description_placeholder": "Opcional: Ingrese una descripción para mostrar en el prompt", "com_ui_download": "<PERSON><PERSON><PERSON>", "com_ui_download_error": "Hubo un error al descargar el archivo. Es posible que el archivo haya sido eliminado.", "com_ui_dropdown_variables": "Variables desplegables:", "com_ui_dropdown_variables_info": "Cree menús desplegables personalizados para sus prompts: `{{nombre_variable:opción1|opción2|opción3}}`", "com_ui_duplicate": "Duplicar", "com_ui_duplication_error": "Hubo un error al duplicar la conversación", "com_ui_duplication_processing": "Duplicando conversación...", "com_ui_duplication_success": "Conversación duplicada exitosamente", "com_ui_edit": "<PERSON><PERSON>", "com_ui_endpoint": "Punto de conexión", "com_ui_endpoint_menu": "Menú de Punto de Conexión LLM", "com_ui_enter": "Intro", "com_ui_enter_api_key": "Ingrese la clave API", "com_ui_enter_openapi_schema": "Ingrese su esquema OpenAPI aquí", "com_ui_error": "Error", "com_ui_error_connection": "Error al conectarse al servidor. Intente actualizar la página.", "com_ui_error_save_admin_settings": "Se produjo un error al guardar su configuración de administrador.", "com_ui_examples": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_export_convo_modal": "Exportar Conversación", "com_ui_field_required": "Este campo es obligatorio", "com_ui_filter_prompts": "Filtrar Prompts", "com_ui_filter_prompts_name": "Filtrar prompts por nombre", "com_ui_finance": "Finanzas", "com_ui_fork": "Bifurcar", "com_ui_fork_all_target": "Incluir todo desde/hacia aquí", "com_ui_fork_branches": "Incluir ramas relacionadas", "com_ui_fork_change_default": "Opción de bifurcación predeterminada", "com_ui_fork_default": "Utilizar opción de bifurcación predeterminada", "com_ui_fork_error": "Hubo un error al bifurcar la conversación", "com_ui_fork_from_message": "Seleccione una opción de bifurcación", "com_ui_fork_info_1": "com_ui_fork_info_1: 'Utilice esta configuración para bifurcar los mensajes con el comportamiento deseado.'", "com_ui_fork_info_2": "\"Bifurcar\" se refiere a crear una nueva conversación que comienza/termina desde mensajes específicos en la conversación actual, creando una copia según las opciones seleccionadas.", "com_ui_fork_info_3": "El término \"mensaje objetivo\" se refiere ya sea al mensaje desde el cual se abrió este popup, o, si marca \"{{0}}\", al último mensaje en la conversación.", "com_ui_fork_info_branches": "Esta opción bifurca los mensajes visibles, junto con las ramas relacionadas; en otras palabras, la ruta directa hacia el mensaje objetivo, incluyendo las ramas a lo largo de esa ruta.", "com_ui_fork_info_remember": "Marque esta opción para recordar las preferencias que seleccione para su uso futuro, lo que agilizará la bifurcación de conversaciones según sus preferencias.", "com_ui_fork_info_start": "Si se marca, la bifurcación comenzará desde este mensaje hasta el último mensaje de la conversación, según el comportamiento seleccionado anteriormente.", "com_ui_fork_info_target": "Esta opción bifurca todos los mensajes que conducen al mensaje objetivo, incluyendo sus vecinos; en otras palabras, se incluyen todas las ramas de mensajes, ya sean visibles o no, o estén en el mismo camino.", "com_ui_fork_info_visible": "Esta opción bifurca únicamente los mensajes visibles; es decir, la ruta directa hacia el mensaje objetivo, sin ninguna ramificación.", "com_ui_fork_processing": "Bifurcando conversación...", "com_ui_fork_remember": "Recuerde", "com_ui_fork_remember_checked": "Su selección se recordará después de utilizarla. Puede cambiar esta opción en cualquier momento en la configuración.", "com_ui_fork_split_target": "Iniciar bifurcación aquí", "com_ui_fork_split_target_setting": "Iniciar bifurcación desde el mensaje objetivo de forma predeterminada", "com_ui_fork_success": "Se ha bifurcado la conversación con éxito", "com_ui_fork_visible": "Mostrar únicamente mensajes visibles", "com_ui_generating": "Generando...", "com_ui_go_back": "Volver", "com_ui_go_to_conversation": "Ir a la conversación", "com_ui_good_afternoon": "Buenas tardes", "com_ui_good_evening": "Buenas noches", "com_ui_good_morning": "Buenos días", "com_ui_happy_birthday": "¡Es mi primer cumpleaños!", "com_ui_host": "Host", "com_ui_idea": "Ideas", "com_ui_image_gen": "<PERSON>", "com_ui_import_conversation_error": "Hubo un error al importar tus chats", "com_ui_import_conversation_file_type_error": "com_ui_import_conversation_file_type_error: Tipo de archivo no compatible para importar", "com_ui_import_conversation_info": "Importar chats de un archivo JSON", "com_ui_import_conversation_success": "Chats importados exitosamente", "com_ui_include_shadcnui": "Incluir instrucciones de componentes shadcn/ui", "com_ui_input": "Entrada", "com_ui_instructions": "Instrucciones", "com_ui_latest_footer": "IA para todos.", "com_ui_latest_version": "Última versión", "com_ui_librechat_code_api_key": "Obtenga su clave API del Intérprete de Código de LibreChat", "com_ui_librechat_code_api_subtitle": "Seguro. Multilenguaje. Archivos de entrada/salida.", "com_ui_librechat_code_api_title": "Ejecutar Código IA", "com_ui_loading": "Cargando...", "com_ui_locked": "Bloqueado", "com_ui_logo": "Logotip<PERSON> de {{0}}", "com_ui_manage": "Administrar", "com_ui_max_tags": "El número máximo permitido es {{0}}, utilizando los valores más recientes.", "com_ui_mention": "Menciona un punto de conexión, asistente o preconfiguración para cambiar rápidamente a él.", "com_ui_min_tags": "No se pueden eliminar más valores, se requiere un mínimo de {{0}}.", "com_ui_model": "<PERSON><PERSON>", "com_ui_model_parameters": "Parámetros del Modelo", "com_ui_more_info": "Más información", "com_ui_my_prompts": "Mis Prompts", "com_ui_name": "Nombre", "com_ui_new": "Nuevo", "com_ui_new_chat": "Nuevo Chat", "com_ui_next": "Sig", "com_ui_no": "No", "com_ui_no_bookmarks": "Parece que aún no tiene marcadores. Haga clic en un chat y agregue uno nuevo", "com_ui_no_category": "Sin categoría", "com_ui_no_changes": "No hay cambios para actualizar", "com_ui_no_terms_content": "No hay contenido de términos y condiciones para mostrar", "com_ui_none": "<PERSON><PERSON><PERSON>", "com_ui_nothing_found": "No se encontró nada", "com_ui_of": "de", "com_ui_off": "Desactivado", "com_ui_on": "Encendido", "com_ui_page": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_prev": "Ant", "com_ui_preview": "Previsualizar", "com_ui_privacy_policy": "Política de privacidad", "com_ui_privacy_policy_url": "URL de la Política de Privacidad", "com_ui_prompt": "Prompt", "com_ui_prompt_already_shared_to_all": "Este prompt ya está compartido con todos los usuarios", "com_ui_prompt_name": "Nombre del Prompt", "com_ui_prompt_name_required": "El nombre del prompt es obligatorio", "com_ui_prompt_preview_not_shared": "El autor no ha permitido la colaboración para este prompt", "com_ui_prompt_text": "Texto", "com_ui_prompt_text_required": "El texto es obligatorio", "com_ui_prompt_update_error": "Hubo un error al actualizar el prompt", "com_ui_prompts": "Indicaciones", "com_ui_prompts_allow_create": "<PERSON><PERSON><PERSON> crea<PERSON> Prompts", "com_ui_prompts_allow_share_global": "Permitir compartir plantillas con todos los usuarios", "com_ui_prompts_allow_use": "<PERSON><PERSON><PERSON> uso de Prompts", "com_ui_provider": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_read_aloud": "Leer en voz alta", "com_ui_regenerate": "<PERSON><PERSON><PERSON>", "com_ui_regenerating": "Regenerando...", "com_ui_region": "Región", "com_ui_rename": "Renombrar", "com_ui_rename_conversation": "Renombrar conversación", "com_ui_rename_prompt": "Renombrar prompt", "com_ui_reset_var": "Restablecer {{0}}", "com_ui_result": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_revoke": "Revocar", "com_ui_revoke_info": "Revocar todas las credenciales proporcionadas por el usuario", "com_ui_revoke_key_confirm": "¿Está seguro de que desea revocar esta clave?", "com_ui_revoke_key_endpoint": "Revocar clave para {{0}}", "com_ui_revoke_keys": "<PERSON><PERSON><PERSON>", "com_ui_revoke_keys_confirm": "¿Está seguro de que desea revocar todas las claves?", "com_ui_role_select": "Rol", "com_ui_run_code": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "com_ui_run_code_error": "Se produjo un error al ejecutar el código", "com_ui_save": "Guardar", "com_ui_save_submit": "Guardar y Enviar", "com_ui_saved": "¡Guardado!", "com_ui_schema": "Esquema", "com_ui_search": "Buscar", "com_ui_select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_select_file": "Seleccionar un archivo", "com_ui_select_model": "Seleccionar un modelo", "com_ui_select_provider": "<PERSON><PERSON><PERSON><PERSON><PERSON> un proveedor", "com_ui_select_provider_first": "Seleccione un proveedor primero", "com_ui_select_region": "Seleccionar una región", "com_ui_select_search_model": "Buscar modelo por nombre", "com_ui_select_search_plugin": "Buscar plugin por nombre", "com_ui_select_search_provider": "Buscar proveedor por nombre", "com_ui_select_search_region": "Buscar región por nombre", "com_ui_share": "Compartir", "com_ui_share_create_message": "Tu nombre y cualquier mensaje que agregues después de compartir se mantendrán privados.", "com_ui_share_delete_error": "Hubo un error al eliminar el enlace compartido.", "com_ui_share_error": "Hubo un error al compartir el enlace del chat", "com_ui_share_link_to_chat": "Compartir enlace en el chat", "com_ui_share_to_all_users": "Compartir con todos los usuarios", "com_ui_share_update_message": "Tu nombre, instrucciones personalizadas y cualquier mensaje que agregues después de compartir se mantendrán privados.", "com_ui_share_var": "Compartir {{0}}", "com_ui_shared_link_not_found": "Enlace compartido no encontrado", "com_ui_shared_prompts": "Prompts Compartidos", "com_ui_shop": "Compras", "com_ui_show": "Mostrar", "com_ui_show_all": "<PERSON><PERSON>", "com_ui_simple": "Simple", "com_ui_size": "<PERSON><PERSON><PERSON>", "com_ui_special_variables": "Variables especiales:", "com_ui_speech_while_submitting": "No se puede enviar un mensaje de voz mientras se está generando una respuesta", "com_ui_stop": "Detener", "com_ui_storage": "Almacenamiento", "com_ui_submit": "Enviar", "com_ui_teach_or_explain": "Aprendizaje", "com_ui_terms_and_conditions": "Términos y Condiciones", "com_ui_terms_of_service": "Términos de servicio", "com_ui_thinking": "Pensando...", "com_ui_tools": "Herramientas", "com_ui_travel": "<PERSON><PERSON>", "com_ui_unarchive": "Desarchivar", "com_ui_unarchive_error": "Error al desarchivar la conversación", "com_ui_unknown": "Desconocido", "com_ui_update": "Actualizar", "com_ui_upload": "Subir", "com_ui_upload_code_files": "Subir archivo para el Intérprete de Código", "com_ui_upload_delay": "La carga de \"{{0}}\" está tomando más tiempo del esperado. Espere mientras el archivo termina de indexarse para su recuperación.", "com_ui_upload_error": "Hubo un error al subir su archivo", "com_ui_upload_file_search": "Subir para búsqueda de archivos", "com_ui_upload_files": "Subir archivos", "com_ui_upload_image": "Subir una imagen", "com_ui_upload_image_input": "Subir imagen", "com_ui_upload_invalid": "Archivo no válido para subir. Debe ser una imagen que no exceda el límite", "com_ui_upload_invalid_var": "Archivo inválido para subir. Debe ser una imagen que no exceda los {{0}} MB", "com_ui_upload_success": "Archivo subido con éxito", "com_ui_upload_type": "Seleccionar tipo de carga", "com_ui_use_micrphone": "Usar micrófono", "com_ui_variables": "Variables", "com_ui_variables_info": "Utilice llaves dobles en su texto para crear variables, por ejemplo `{{variable de ejemplo}}`, para completarlas posteriormente al usar el prompt.", "com_ui_verify": "Verificar", "com_ui_version_var": "Versión {{0}}", "com_ui_versions": "Versiones", "com_ui_weekend_morning": "Feliz fin de semana", "com_ui_write": "Escribiendo", "com_ui_x_selected": "{{0}} se<PERSON><PERSON><PERSON>do", "com_ui_yes": "Sí", "com_ui_zoom": "Zoom", "com_user_message": "Usted"}