{"com_a11y_end": "Teko<PERSON><PERSON> vastaus on valmis.", "com_a11y_start": "<PERSON><PERSON><PERSON><PERSON> on aloittanut vastaamisen.", "com_agents_allow_editing": "<PERSON> muiden käyttäjien muokata agenttiasi", "com_agents_create_error": "Agentin luonnissa tapah<PERSON>i virhe.", "com_agents_description_placeholder": "Valinnainen: <PERSON><PERSON><PERSON><PERSON> tähän agentin kuvaus", "com_agents_enable_file_search": "Käytä <PERSON>", "com_agents_file_context": "<PERSON><PERSON><PERSON><PERSON>nteks<PERSON> (OCR)", "com_agents_file_context_disabled": "Agentti t<PERSON>ytyy luoda ennen tiedostojen lataamista Tiedostokontekstiin", "com_agents_file_context_info": "\"Kontekstiksi\" ladatuista tiedostoista luetaan sisältö tekstintunnist<PERSON> (OCR) avulla agentin ohjeisiin lisättäväksi. Soveltuu erityisesti asiakirjojen, tekstiä sisältävien kuvien tai PDF-tiedostojen k<PERSON><PERSON>, kun haluat hyödyntää tiedoston koko tekstisisällön.", "com_agents_file_search_disabled": "Agentti t<PERSON>y luoda ennen tiedostojen lataamista Tiedostohakuun", "com_agents_file_search_info": "Asetuksen ollessa päällä agentti saa tiedoksi alla luetellut tiedostonimet, jolloin se voi hakea vastausten pohjaksi asiayhteyksiä tiedostojen sisällöistä.", "com_agents_instructions_placeholder": "Agentin k<PERSON>yttämät järjestelm<PERSON><PERSON><PERSON>t", "com_agents_mcp_description_placeholder": "<PERSON> muutaman sanan kuvaus sen toim<PERSON><PERSON>a", "com_agents_mcp_icon_size": "Minimikoko 128 x 128 px", "com_agents_mcp_info": "Lisää MCP-palvelimia agentillesi, jotta se voi ottaa yhteyttä ulkoisiin palveluihin ja suorittaa tehtäviä niissä.", "com_agents_mcp_name_placeholder": "Mukautettu työkalu", "com_agents_mcp_trust_subtext": "LibreChat ei varmenna mukautettuja liittymiä.", "com_agents_mcps_disabled": "Agentti on luotava ennen MCP:n lisäämistä.", "com_agents_missing_provider_model": "Valitse palvelun tuottaja ja malli ennen agentin luomista.", "com_agents_name_placeholder": "Valinnainen: <PERSON><PERSON>", "com_agents_no_access": "<PERSON>ulla ei ole lupaa muokata tätä agenttia.", "com_agents_no_agent_id_error": "Agentin tunnistetta ei löytynyt. Varmista ensin, ett<PERSON> agentti on luotu.", "com_agents_not_available": "Agentti ei ole saatavilla.", "com_agents_search_info": "Asetuksen ollessa päällä agentti voi tehdä verkkohakuja ajantasaisen tiedon hakemista varten. Vaatii voimassaolevan API-avaimen.", "com_agents_search_name": "Etsi agentteja ni<PERSON> per<PERSON>", "com_agents_update_error": "Agentin päivittämisessä tapahtui virhe.", "com_assistants_action_attempt": "Assistent<PERSON> haluaa keskustella {{0}}:n kanssa", "com_assistants_actions": "<PERSON><PERSON><PERSON><PERSON>", "com_assistants_actions_disabled": "Avustaja täytyy luoda ennen toimintojen lisäämistä", "com_assistants_actions_info": "<PERSON><PERSON> tai Toimintojen suorittaminen API-kutsujen kautta", "com_assistants_add_actions": "Lisää <PERSON>", "com_assistants_add_tools": "Lisää Työkaluja", "com_assistants_allow_sites_you_trust": "<PERSON><PERSON> vain sellaiset sivustot, joi<PERSON> luotat,", "com_assistants_append_date": "Lisää nykyinen päivämäärä ja aika", "com_assistants_append_date_tooltip": "<PERSON><PERSON> k<PERSON>, nykyinen asiakkaan päivämäärä ja aika lisätään avustajan järjestelmäohjeisiin.", "com_assistants_attempt_info": "Assistentti haluaa lähettää seuraavan:", "com_assistants_available_actions": "Käytettävissä olevat Toiminnot", "com_assistants_capabilities": "Kyvykkyydet", "com_assistants_code_interpreter": "Kooditulk<PERSON>", "com_assistants_code_interpreter_files": "<PERSON><PERSON><PERSON><PERSON> tied<PERSON>ot ovat vain Kooditulkin käytettävissä:", "com_assistants_code_interpreter_info": "Kooditulkki mahdollistaa assistentille koodin kirjoittamisen ja ajamisen. Tämä työkalu voi käsitellä tiedostoja, jois<PERSON> on eri tyyppisiä tietoja ja muotoiluja, ja luoda tiedostoja kuten kaavioita.", "com_assistants_completed_action": "Puhuttiin {{0}}:lle", "com_assistants_completed_function": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{0}}", "com_assistants_conversation_starters": "<PERSON><PERSON><PERSON><PERSON><PERSON> al<PERSON>", "com_assistants_conversation_starters_placeholder": "Lisää keskustelun aloitus", "com_assistants_create_error": "<PERSON><PERSON><PERSON><PERSON> luonnissa tapahtui virhe.", "com_assistants_create_success": "<PERSON><PERSON><PERSON>", "com_assistants_delete_actions_error": "<PERSON><PERSON><PERSON><PERSON> poistamisessa tapahtui virhe.", "com_assistants_delete_actions_success": "<PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON> onnistuneesti", "com_assistants_description_placeholder": "Valinnainen: <PERSON><PERSON><PERSON>", "com_assistants_domain_info": "Avustaja lähetti tiedon tänne: {{0}}", "com_assistants_file_search": "Tiedostohak<PERSON>", "com_assistants_file_search_info": "Vektoritietokannan liittämistä tiedostohakuun ei vielä tueta. Voit liittää ne rajapinnan palveluntarjoajan käyttöliittymän kautta, tai liittää tiedostoja viesteihin keskusteluketjupohjaisesti.", "com_assistants_function_use": "Avustaja käytti: {{0}}", "com_assistants_image_vision": "Kuvanäkö", "com_assistants_instructions_placeholder": "Avustajan k<PERSON>ämät järjestelmäohjeet", "com_assistants_knowledge": "<PERSON><PERSON><PERSON>", "com_assistants_knowledge_disabled": "Avustaja täytyy ensin luoda, ja <PERSON>od<PERSON> tai <PERSON> täytyy olla päällä ja asetukset tallenne<PERSON>, ennen kuin tiedostoja voidaan ladata Tietoihin.", "com_assistants_knowledge_info": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> kans<PERSON> k<PERSON> keskusteluihin voi tulla niiden sisältöä.", "com_assistants_max_starters_reached": "Maksimimäärä keskustelun aloituksia lisätty", "com_assistants_name_placeholder": "Valinnainen: <PERSON><PERSON><PERSON><PERSON>", "com_assistants_non_retrieval_model": "Tiedostohaku ei ole käytössä tässä mallissa. Valitse toinen malli.", "com_assistants_retrieval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_assistants_running_action": "<PERSON><PERSON><PERSON><PERSON><PERSON> toim<PERSON>", "com_assistants_running_var": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{0}}", "com_assistants_search_name": "<PERSON><PERSON>a nimen per<PERSON>", "com_assistants_update_actions_error": "<PERSON><PERSON>innon luomisessa tai päivittämisessä tapahtui virhe.", "com_assistants_update_actions_success": "<PERSON><PERSON><PERSON>o luotiiin tai päivitettiin onnistuneesti", "com_assistants_update_error": "Avustajan päivittämisessä tapahtui virhe.", "com_assistants_update_success": "<PERSON><PERSON><PERSON><PERSON>", "com_auth_already_have_account": "K<PERSON>yttäjätilisi on jo luotu?", "com_auth_apple_login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_back_to_login": "<PERSON><PERSON><PERSON>", "com_auth_click": "Napauta", "com_auth_click_here": "Napauta tästä", "com_auth_continue": "Jatka", "com_auth_create_account": "<PERSON><PERSON> tili", "com_auth_discord_login": "Jatka Discordilla", "com_auth_email": "Sähköposti", "com_auth_email_address": "Sähköpostiosoite Email address", "com_auth_email_max_length": "Sähköpostiosoitteen ei pitäisi olla 120 merkkiä pidempi", "com_auth_email_min_length": "Sähköpostiosoitteen on oltava vähintään 6 merkkiä pitkä", "com_auth_email_pattern": "Sähköpostiosoite on syötettävä oikeassa muodossa", "com_auth_email_required": "Sähköposti on pakollinen", "com_auth_email_resend_link": "Lähetä sähköposti uudestaan", "com_auth_email_resent_failed": "Varmennussähköpostin uudelleenlähetys epäonnistui", "com_auth_email_resent_success": "Varmennussähköpostin uudelleenlähetys onnistui", "com_auth_email_verification_failed": "Sähköpostin varmentaminen epäonnistui", "com_auth_email_verification_failed_token_missing": "Varmennus epäonnistui tunnisteen puutt<PERSON> vuoksi", "com_auth_email_verification_in_progress": "Varmennetaan sähköpostia. Ole hyvä ja odota.", "com_auth_email_verification_invalid": "Sähköpostin varmentaminen ei voimassa", "com_auth_email_verification_redirecting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{0}} sekunnissa...", "com_auth_email_verification_resend_prompt": "Sähköposti ei saapunut perille?", "com_auth_email_verification_success": "Sähköposti varmennettu", "com_auth_email_verifying_ellipsis": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "com_auth_error_create": "<PERSON>ilin rekisteröinnissä tapahtui virhe. Yritä u<PERSON>.", "com_auth_error_invalid_reset_token": "Tämä salasanan uusimistunniste ei ole enää voimassa.", "com_auth_error_login": "Kirjautuminen annetuilla tiedoilla ei onnistunut. Tarkista kirjautumist<PERSON>, ja y<PERSON><PERSON>.", "com_auth_error_login_ban": "<PERSON><PERSON><PERSON> on väliaikaisesti suljettu palvelun sääntöjen rikkomisesta.", "com_auth_error_login_rl": "<PERSON><PERSON> monta kirjautumisyritystä lyhyen ajan sisällä. Yrit<PERSON>.", "com_auth_error_login_server": "Tapaht<PERSON> sisäinen palvelinvirhe. <PERSON><PERSON><PERSON>, ja yrit<PERSON>.", "com_auth_error_login_unverified": "Tiliäsi ei ole vahvistettu. Vahvistuslinkin pitäisi löytyä sähköposteistasi.", "com_auth_facebook_login": "Jatka Facebookilla", "com_auth_full_name": "<PERSON><PERSON> nimi", "com_auth_github_login": "<PERSON>at<PERSON>", "com_auth_google_login": "<PERSON><PERSON><PERSON>", "com_auth_here": "TÄTÄ", "com_auth_login": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_login_with_new_password": "Voit nyt kirjautua uudella <PERSON>.", "com_auth_name_max_length": "<PERSON><PERSON> voi olla enintään 80 merkkiä pitkä", "com_auth_name_min_length": "Nimessä on oltava vähintään 3 merkkiä", "com_auth_name_required": "<PERSON><PERSON> on pak<PERSON><PERSON>", "com_auth_no_account": "Ei tunnusta?", "com_auth_password": "<PERSON><PERSON><PERSON>", "com_auth_password_confirm": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>", "com_auth_password_forgot": "<PERSON><PERSON><PERSON> un<PERSON>?", "com_auth_password_max_length": "Salasana voi olla enintään 128 merkkiä", "com_auth_password_min_length": "Salasanan on oltava vähintään 8 merkkiä pitkä", "com_auth_password_not_match": "Salasanat eivät täsmää", "com_auth_password_required": "Salasana on pakollinen", "com_auth_registration_success_generic": "Tarkista sähköpostisi sähköpostiosoitteen vahvistamiseksi.", "com_auth_registration_success_insecure": "Rekisteröityminen onnistui.", "com_auth_reset_password": "<PERSON><PERSON> uusi salasana", "com_auth_reset_password_if_email_exists": "<PERSON><PERSON> k<PERSON><PERSON><PERSON> sähköpostiosoitteelle löytyy käyttäjätili, siihen lähetetään sähköposti joka sisältää ohjeet salasanan uusimiseen. Tarkistathan myös roskapostikansion.", "com_auth_reset_password_link_sent": "Sähköposti lähetetty", "com_auth_reset_password_success": "<PERSON><PERSON><PERSON> as<PERSON><PERSON><PERSON> on<PERSON>", "com_auth_saml_login": "Jatka SAML-kirjautumisella", "com_auth_sign_in": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_sign_up": "Rekisteröidy", "com_auth_submit_registration": "Lähetä rekisteröityminen", "com_auth_to_reset_your_password": "as<PERSON><PERSON><PERSON> uuden salasanan.", "com_auth_to_try_again": "k<PERSON><PERSON><PERSON><PERSON>.", "com_auth_username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (valinnainen)", "com_auth_username_max_length": "Käyttäjänimi voi olla enintään 20 merkkiä pitkä", "com_auth_username_min_length": "Käyttäjänimessä on oltava vähintään 2 merkkiä", "com_auth_verify_your_identity": "Varmista identiteettisi", "com_auth_welcome_back": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_citation_more_details": "Lisätietoa: {{label}}", "com_citation_source": "Lä<PERSON><PERSON>", "com_click_to_download": "(lataa napauttamalla tästä)", "com_download_expired": "(lataus on vanhentunut)", "com_download_expires": "(lataa napauttamalla tätä - van<PERSON> {{0}})", "com_endpoint": "Päätepiste", "com_endpoint_agent": "<PERSON><PERSON>", "com_endpoint_agent_model": "<PERSON><PERSON><PERSON><PERSON> (Suositus: GPT-3.5)", "com_endpoint_agent_placeholder": "Valitse agentti", "com_endpoint_ai": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_anthropic_maxoutputtokens": "Vastauksen maksimi-tokenmäärä. valitse pienempi arvo, jos haluat lyhyempiä vastauksia, ja korkeampi arvo, jos haluat pidempiä vastauksia.", "com_endpoint_anthropic_prompt_cache": "Tallentamalla syötteet välimuistiin suurta määrää kontekstia tai ohjeita voidaan käyttää useiden API-kutsujen, mikä vähentää kuluja ja nopeuttaa käsittelyä.", "com_endpoint_anthropic_temp": "Vaihteluväli on 0 - 1. Käytä lähempänä nollaa olevaa lämpötilaa analyyttisiin tai monivalintatehtäviin, ja lähempänä yhtä luoviin ja generatiivisiin tehtäviin. <PERSON><PERSON><PERSON>lemme, että muokkaat tätä tai Top P:tä, mutta ei molempia.", "com_endpoint_anthropic_topk": "Top-k vaiku<PERSON><PERSON> siihen, miten malli valitsee to<PERSON><PERSON><PERSON> tulokseen. Jos Top-k on 1, vali<PERSON><PERSON> se token, joka on kaikkien todennäköisen mallin sanastossa (tun<PERSON><PERSON> myös nimell<PERSON> ah<PERSON> dekooda<PERSON>), kun taas top-k 3 tarkoittaisi, että seuraavat token valitaan 3 todennäköisimmän tokenin joukosta, lämpötilaa hyödyntäen.", "com_endpoint_anthropic_topp": "Top-P vaikuttaa siihen kuinka malli valitsee tokeneita tulokseen. Tokenit valitaan top-k:sta (ks. Top-k -parametri) todennäköisimmistä vähiten toden<PERSON>äköseen, kunnes niiden todennäköisyyksien summa ylittää Top-P -arvon.", "com_endpoint_assistant": "Avustaja", "com_endpoint_assistant_model": "<PERSON><PERSON><PERSON><PERSON> malli", "com_endpoint_assistant_placeholder": "Valitse Avustaja oike<PERSON>puoleises<PERSON> sivupal<PERSON>ta", "com_endpoint_completion": "Vastaus", "com_endpoint_completion_model": "<PERSON><PERSON><PERSON><PERSON><PERSON> (Suositus: GPT-4)", "com_endpoint_config_click_here": "Napauta tästä", "com_endpoint_config_google_api_info": "Saadaksesi Generative Language API -avaimesi (Gemini:a varten),", "com_endpoint_config_google_api_key": "Google API Key", "com_endpoint_config_google_cloud_platform": "(Google Cloud Platform:ista)", "com_endpoint_config_google_gemini_api": "(Gemini API)", "com_endpoint_config_google_service_key": "Google Service Account Key", "com_endpoint_config_key": "Aseta API-avain", "com_endpoint_config_key_encryption": "<PERSON><PERSON><PERSON> sa<PERSON>aan ja poiste<PERSON>an: ", "com_endpoint_config_key_for": "Aseta API-avain:", "com_endpoint_config_key_google_need_to": "<PERSON><PERSON> t<PERSON>", "com_endpoint_config_key_google_service_account": "<PERSON><PERSON> (Service Account)", "com_endpoint_config_key_google_vertex_ai": "sallia Vertex AI", "com_endpoint_config_key_google_vertex_api": "API Google Cloud:issa, sitten", "com_endpoint_config_key_google_vertex_api_role": "<PERSON><PERSON> nap<PERSON> '<PERSON><PERSON> and Continue' jotta saat ainakin 'Vertex AI User' -roolin. Lopuksi luo JSON-avain tänne tuotavaksi.", "com_endpoint_config_key_import_json_key": "<PERSON><PERSON> palvel<PERSON><PERSON>itteen JSON-avain.", "com_endpoint_config_key_import_json_key_invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> palveluosoitteen JSON-avain. <PERSON><PERSON><PERSON> tied<PERSON>on?", "com_endpoint_config_key_import_json_key_success": "Palveluosoitteetn JSON-avain tuotu onnistuneesti", "com_endpoint_config_key_name": "Avain", "com_endpoint_config_key_never_expires": "Avaimesi ei koskaan van<PERSON>e", "com_endpoint_config_placeholder": "Keskustellaksesi aseta avaimesi Ylätunnistevalikossa.", "com_endpoint_config_value": "Aseta arvo:", "com_endpoint_context": "Konteks<PERSON>", "com_endpoint_context_info": "Kontekstia varten käytettävien tokeneiden maksimimäärä. Käytä tätä pyyntökohtaisten token-määrien hallinnointiin. Jos tätä ei määritetä, käytössä ovat järjestelmän oletusarvot perustuen tiedossa olevien mallien konteksti-ikkunoiden kokoon. Korkeamman arvon asettaminen voi aiheuttaa virheitä tai korkeamman token-hinnan.", "com_endpoint_context_tokens": "Konteksti-<PERSON>ien maksimimäärä", "com_endpoint_custom_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "com_endpoint_default": "o<PERSON>us", "com_endpoint_default_blank": "oletus: tyhjä", "com_endpoint_default_empty": "oletus: tyhjä", "com_endpoint_default_with_num": "oletus: {{0}}", "com_endpoint_examples": " <PERSON><PERSON>setukset", "com_endpoint_export": "Vie", "com_endpoint_export_share": "Vie/Jaa", "com_endpoint_frequency_penalty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_func_hover": "<PERSON><PERSON> lisäosien käyttö OpenAI-toimintoina", "com_endpoint_google_custom_name_placeholder": "<PERSON><PERSON>lle mukautettu nimi", "com_endpoint_google_maxoutputtokens": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, joita gene<PERSON> tulo<PERSON>. Valitse pienempi arvo saadaksesi lyhyempiä vastauksia, ja suurempi arvo pitkiä vastauksia varten.", "com_endpoint_google_temp": "Korkeampi arvo = satunnaisempi; matalampi arvo = keskittyneempi ja deterministisempi. <PERSON><PERSON><PERSON>lemme, että muokkaat tätä tai Top P:tä, mutta ei molempia.", "com_endpoint_google_topk": "Top-k vaiku<PERSON><PERSON> siihen, miten malli valitsee to<PERSON><PERSON><PERSON> tulokseen. Jos Top-k on 1, vali<PERSON><PERSON> se token, joka on kaikkien todennäköisen mallin sanastossa (tun<PERSON><PERSON> myös nimell<PERSON> ah<PERSON> dekooda<PERSON>), kun taas top-k 3 tarkoittaisi, että seuraavat token valitaan 3 todennäköisimmän tokenin joukosta, lämpötilaa hyödyntäen.", "com_endpoint_google_topp": "Top-P vaikuttaa siihen kuinka malli valitsee tokeneita tulokseen. Tokenit valitaan top-k:sta (ks. Top-k -parametri) todennäköisimmistä vähiten toden<PERSON>äköseen, kunnes niiden todennäköisyyksien summa ylittää Top-P -arvon.", "com_endpoint_instructions_assistants": "<PERSON><PERSON><PERSON>", "com_endpoint_instructions_assistants_placeholder": "Yliajaa A<PERSON> oh<PERSON>. Tätä voi hyödyntää käytöksen muuttamiseen keskustelukohtaisesti.", "com_endpoint_max_output_tokens": "<PERSON><PERSON>-<PERSON><PERSON><PERSON> maksimimäärä", "com_endpoint_message": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_message_not_appendable": "Muokkaa viestiäsi tai <PERSON>.", "com_endpoint_my_preset": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_no_presets": "Ei vielä esiasetuksia. Käytä Asetukset-painiketta luodaksesi esiasetuksen.", "com_endpoint_open_menu": "<PERSON><PERSON> vali<PERSON>", "com_endpoint_openai_custom_name_placeholder": "<PERSON> muka<PERSON>ttu nimi", "com_endpoint_openai_detail": "Kuvatarkkuus Vision-pyynnöille. \"Matala\" on halvempi ja nopeamp<PERSON>, \"Kork<PERSON>\" on yksityiskohtaisempi ja kalli<PERSON>, ja \"Auto\" valitsee näiden välillä automaattisesti kuvan koon perust<PERSON>.", "com_endpoint_openai_freq": "Lukuarvo väliltä -2.0 - 2.0. Positiiviset arvot rankaisevat uusia tokeneita perustuen niiden esiintymistiheyteen siihen mennessä luodussa tekstissä, mikä vähentää todennäköisyyttä, että malli toistaa saman rivin täsmälleen samanlaisena.", "com_endpoint_openai_max": "Luotavien tokeneiden maksimimäärä. <PERSON><PERSON> konte<PERSON>-ikkuna raj<PERSON>a syötteiden ja vastausten kokonaispituutta.", "com_endpoint_openai_max_tokens": "Valinn<PERSON>n 'max_tokens' -k<PERSON><PERSON><PERSON>, joka kuvaa keskustelun vastauksessa generoitujen tokeneiden maksimimäärää. Syötteen ja vastauksen kokonaispituutta rajoittaa mallin konteksti-ikkuna. Konteksti -ikkunan koon ylittämisestä voi seurata virheitä.", "com_endpoint_openai_pres": "Lukuarvo väliltä -2.0 - 2.0. Positiiviset arvot rankaisevat uusia tokeneita perustuen niiden esiintymiseen siihen mennessä luodussa tekstissä, ja lisäävät todennäköisyyttä että malli aloittaa uuden aiheen.", "com_endpoint_openai_prompt_prefix_placeholder": "Aseta mukautetut oh<PERSON>t J<PERSON>r<PERSON>stelmäohjeisiin si<PERSON>ällytettäväksi. Oletus: tyhjä", "com_endpoint_openai_resend": "Lähetä uudestaan kaikki aiemmin liitetyt kuvat. Huom: tämä voi lisätä token-kustann<PERSON><PERSON>, ja useiden kuvien käsittelystä kerralla voi seurata virheitä.", "com_endpoint_openai_resend_files": "Lähetä uudestaan kaikki aiemmin liitetyt tiedostot. Huom: tämä lisää token-kustann<PERSON><PERSON>, ja useiden tiedostojen käsittelystä kerralla voi seurata virheitä.", "com_endpoint_openai_stop": "Enintään 4 sekvenssiä, joiden kohdalla API lopettaa tokenien luomisen.", "com_endpoint_openai_temp": "Korkeampi arvo = satunnaisempi; matalampi arvo = keskittyneempi ja deterministisempi. <PERSON><PERSON><PERSON>lemme, että muokkaat tätä tai Top P:tä, mutta ei molempia.", "com_endpoint_openai_topp": "Vaihtoehto lämpötil<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, valitsee tokeneita Top P -todennäköisyysmassasta. Esimerkiksi arvo 0.1 tarkoittaa että vain top 10% tokeneista todennäköisyysmassassa huomioidaan. <PERSON><PERSON><PERSON><PERSON><PERSON>, että muokkaat tätä tai lämpötilaa, mutta ei molempia.", "com_endpoint_output": "<PERSON><PERSON>", "com_endpoint_plug_image_detail": "<PERSON><PERSON>", "com_endpoint_plug_resend_files": "Lähetä tiedostot uudestaan", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Aseta mukautetut oh<PERSON>t J<PERSON>rjestelmäohjeisiin liitettäviksi. Oletus: tyhjä", "com_endpoint_plug_skip_completion": "<PERSON><PERSON>", "com_endpoint_plug_use_functions": "Käytä <PERSON>", "com_endpoint_presence_penalty": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_preset": "es<PERSON><PERSON>", "com_endpoint_preset_default": "on nyt oletus-esiasetus.", "com_endpoint_preset_default_item": "<PERSON><PERSON>:", "com_endpoint_preset_default_none": "Oletus-esiasetusta ei ole k<PERSON>ä", "com_endpoint_preset_default_removed": " ei ole enää oletus-esiasetus.", "com_endpoint_preset_delete_confirm": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa nämä esiasetukset?", "com_endpoint_preset_delete_error": "<PERSON><PERSON><PERSON><PERSON><PERSON> poistossa tapahtui virhe. Yrit<PERSON>.", "com_endpoint_preset_import": "<PERSON><PERSON><PERSON><PERSON> tuotu!", "com_endpoint_preset_import_error": "<PERSON><PERSON><PERSON><PERSON>sen tuonnissa tapahtui virhe. <PERSON><PERSON><PERSON>.", "com_endpoint_preset_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> nimi", "com_endpoint_preset_save_error": "<PERSON><PERSON><PERSON><PERSON><PERSON> tallennuksessa tap<PERSON>i virhe. <PERSON><PERSON><PERSON>.", "com_endpoint_preset_selected": "Esiasetus k<PERSON>ytössä!", "com_endpoint_preset_selected_title": "Käytössä!", "com_endpoint_preset_title": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_presets": "esiasetukset", "com_endpoint_presets_clear_warning": "Haluat<PERSON> varmasti tyhjentää kaikki esiasetukset? Tätä toimintoa ei voi perua.", "com_endpoint_prompt_prefix": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_prompt_prefix_assistants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_prompt_prefix_assistants_placeholder": "<PERSON> lisäohjeita tai kontekstia Avustajan pääohjeiden lisäksi. Set additional instructions or context on top of the Assistant's main instructions. <PERSON><PERSON>etä<PERSON><PERSON> huo<PERSON>, jos tyhj<PERSON>.", "com_endpoint_prompt_prefix_placeholder": "Aseta mukautetut ohjeet tai konteksti. Jätetään huo<PERSON>, jos tyhj<PERSON>.", "com_endpoint_save_as_preset": "<PERSON><PERSON><PERSON>", "com_endpoint_set_custom_name": "<PERSON><PERSON> muka<PERSON>u nimi, jotta esiasetus olisi help<PERSON>", "com_endpoint_skip_hover": "<PERSON><PERSON><PERSON><PERSON><PERSON> vast<PERSON> ohit<PERSON>, j<PERSON> ma<PERSON><PERSON><PERSON><PERSON><PERSON> lo<PERSON> vastauksen ja generoitujen askeleiden tarkastelun", "com_endpoint_stop": "Pysäytyssekvenssit", "com_endpoint_stop_placeholder": "<PERSON>rota arvot toisistaan rivinvaihdoilla", "com_endpoint_temperature": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_top_k": "Top k", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "Käytä aktiivista Avustajaa", "com_error_expired_user_key": "{{0}} varten annettu avain <PERSON> {{1}}. Syötä uusi avain ja yritä uudes<PERSON>.", "com_error_invalid_user_key": "Avain ei kelpaa. Lisää toimiva avain ja yritä uudes<PERSON>.", "com_error_moderation": "Näyttää siltä, että moderointijärjestelmämme merkitsi lähetetyn sisällön yhteisön sääntöjen vastaisiksi. Emme voi jatkaa tämän aiheen käsittelyä. <PERSON><PERSON> on muita kysymyksiä tai aiheita joita haluaisit käsitellä, ole hyvä ja muokkaa viestiäsi, tai aloita uusi keskustelu.", "com_error_no_base_url": "Base URL puuttuu. Syötä URL ja yritä uudestaan.", "com_error_no_user_key": "Avainta ei löytynyt. Lisää avain ja yritä u<PERSON>.", "com_files_filter": "<PERSON><PERSON><PERSON> tied<PERSON>...", "com_files_no_results": "<PERSON>i tulo<PERSON>.", "com_files_number_selected": "{{0}}/{{1}} <PERSON><PERSON><PERSON> valittu", "com_generated_files": "<PERSON><PERSON><PERSON>:", "com_hide_examples": "Piilota esimerkit", "com_info_heic_converting": "Muunnetaan kuvaa HEIC:istä JPEG:iksi...", "com_nav_2fa": "Kaksivaiheinen tunnistautuminen (2FA)", "com_nav_account_settings": "<PERSON><PERSON><PERSON>", "com_nav_always_make_prod": "Tee aina uudet versiot tuotantoon", "com_nav_archive_created_at": "Arkistointipäivä", "com_nav_archive_name": "<PERSON><PERSON>", "com_nav_archived_chats": "Arkistoidut keskustelut", "com_nav_at_command": "@-komento", "com_nav_audio_play_error": "<PERSON><PERSON><PERSON> to<PERSON>: {{0}}", "com_nav_audio_process_error": "<PERSON><PERSON><PERSON> ää<PERSON> käsitelles<PERSON>ä: {{0}}", "com_nav_auto_scroll": "Vieritä automaattisesti viimeisimpään viestiin keskustelua avatessa", "com_nav_auto_send_prompts": "Lähetä syötteet automaattisesti", "com_nav_auto_send_text": "Lähetä teksti automaattisesti (3 sekunnin kuluttua)", "com_nav_auto_transcribe_audio": "Automaattinen äänen litterointi", "com_nav_automatic_playback": "Toista viimeisin viesti automaattisesti", "com_nav_balance": "<PERSON><PERSON>", "com_nav_browser": "<PERSON><PERSON>", "com_nav_change_picture": "<PERSON><PERSON><PERSON><PERSON> kuva", "com_nav_clear_all_chats": "Tyhjennä kaikki keskustelut", "com_nav_clear_conversation": "Tyhjennä keskustelut", "com_nav_clear_conversation_confirm_message": "<PERSON><PERSON><PERSON> varma että haluat tyhjentää kaikki keskustelut? Tätä toimintoa ei voi peruuttaa.", "com_nav_close_sidebar": "<PERSON><PERSON>", "com_nav_confirm_clear": "Vahvista tyhjennys", "com_nav_conversation_mode": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_db_sensitivity": "Desibeliherkkyys", "com_nav_delete_account": "Poista käyttäjätili", "com_nav_delete_account_button": "Poista käyttäjätilini pysyvästi", "com_nav_delete_account_confirm": "Poista k<PERSON>yttäjätili - o<PERSON><PERSON> varma?", "com_nav_delete_account_email_placeholder": "Syötä käyttäjätilisi sähköpostiosoite", "com_nav_delete_cache_storage": "Tyhjennä TTS (tekstistä ääneksi) -välimuistivarasto", "com_nav_delete_data_info": "<PERSON><PERSON><PERSON> p<PERSON>.", "com_nav_delete_warning": "VAROITUS: T<PERSON>mä poistaa käyttäjätilisi pysyvästi.", "com_nav_enable_cache_tts": "TTS (tekstistä ääneksi) -välimuisti käyttöön", "com_nav_enabled": "Päällä", "com_nav_engine": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_enter_to_send": "Lähetä viestit Enter-painikkeella", "com_nav_export": "Vie", "com_nav_export_all_message_branches": "<PERSON>ie kaikki sivupolut", "com_nav_export_conversation": "Vie keskustelu", "com_nav_export_filename": "Tiedoston nimi", "com_nav_export_filename_placeholder": "<PERSON><PERSON> tied<PERSON>on nimi", "com_nav_export_include_endpoint_options": "Sisällytä päätepistevaihtoehdot", "com_nav_export_recursive": "Re<PERSON><PERSON>iivisesti", "com_nav_export_recursive_or_sequential": "Re<PERSON>rsiivisesti vai sarjassa?", "com_nav_export_type": "Tyyppi", "com_nav_external": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_font_size": "Viestien kirjainkoko", "com_nav_font_size_base": "Keskikoko", "com_nav_font_size_lg": "<PERSON><PERSON>", "com_nav_font_size_sm": "<PERSON><PERSON>", "com_nav_font_size_xl": "Eks<PERSON>uuri", "com_nav_font_size_xs": "Ekstrapieni", "com_nav_help_faq": "Ohjeet & FAQ", "com_nav_hide_panel": "<PERSON><PERSON><PERSON><PERSON> si<PERSON>", "com_nav_info_enter_to_send": "<PERSON><PERSON> on pä<PERSON><PERSON><PERSON>, Enter-näppäimen painaminen lähettää viestin. <PERSON><PERSON> asetus on pois päältä, Enter lisää riv<PERSON>don, ja viestin lähettämiseksi on painettava CTRL + ENTER.", "com_nav_info_fork_change_default": "'Vain näkyvät viestit' sisältää vain suoran polun valittuun viestiin. 'Sisällytä sivupolut' lisää polun varrella olevat sivupolut. 'Lisää kaikki tänne/täältä' sisällyttää kaikki kytköksissä olevat viestit ja sivupolut.", "com_nav_info_fork_split_target_setting": "<PERSON><PERSON> on p<PERSON><PERSON><PERSON><PERSON>, haara syntyy kohdeviestistä keskustelun viimeiseen viestiin valitun haarautumistavan mukaisesti.", "com_nav_info_latex_parsing": "Kun tämä on pä<PERSON><PERSON><PERSON>, viesteissä oleva LaTeX-koodi näytetään yhtälöinä. Tämän asetuksen jättäminen pois päältä saattaa parantaa suorituskykyä, jos et tarvitse LaTeX-tulkkia.", "com_nav_info_save_draft": "<PERSON><PERSON> on p<PERSON><PERSON><PERSON><PERSON>, teksti ja liitteet jotka syötät keskusteluun tallennetaan automaattisesti paikallisina luonnoksina. Nämä luonnokset ovat käytettävissä, vaikka välillä lataisit sivun uudestaan tai vaihtaisit toiseen keskusteluun. Luonnokset on tallettettu paikallisesti omalle laitteellesi, ja ne poiste<PERSON>, kun viesti on lähetetty.", "com_nav_info_user_name_display": "Jo<PERSON> tä<PERSON> on pä<PERSON><PERSON><PERSON>, lähettäjän käyttäjänimi näytetään jokaisen lähettämäsi viestin päällä. Jos tämä ei ole käytössä, viestien päällä näytetään vain \"Sinä\".", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "Tunnista automaattisesti", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_chinese": "中文", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "<PERSON><PERSON>", "com_nav_latex_parsing": "Tulkitse LaTeX:ia viesteissä (saattaa vaikuttaa suoritustehoon)", "com_nav_log_out": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_long_audio_warning": "Pidemmän tekstin käsittely kestää kauemmin.", "com_nav_modular_chat": "<PERSON>li päätepisteen vaihto kesken keskustelun", "com_nav_my_files": "<PERSON><PERSON>", "com_nav_not_supported": "Ei tuettu", "com_nav_open_sidebar": "<PERSON><PERSON> si<PERSON>", "com_nav_playback_rate": "<PERSON><PERSON><PERSON> toiston nopeus", "com_nav_plugin_auth_error": "Tämän lisäosan varmentamisessa tapahtui virhe. Yrit<PERSON>.", "com_nav_plugin_install": "<PERSON><PERSON><PERSON>", "com_nav_plugin_search": "<PERSON>e lis<PERSON>osaa", "com_nav_plugin_store": "<PERSON><PERSON><PERSON>osa<PERSON><PERSON><PERSON>", "com_nav_plugin_uninstall": "Poista", "com_nav_profile_picture": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_save_drafts": "<PERSON><PERSON><PERSON> luo<PERSON>", "com_nav_search_placeholder": "Etsi keskusteluista", "com_nav_send_message": "Lähetä viesti", "com_nav_setting_account": "Käyttäjätili", "com_nav_setting_data": "Datakontrollit", "com_nav_setting_general": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_setting_speech": "<PERSON><PERSON><PERSON>", "com_nav_settings": "Asetukset", "com_nav_shared_links": "Jaetut linkit", "com_nav_show_code": "Kooditulkkia käyttäessä näytä aina koodi", "com_nav_speech_to_text": "<PERSON><PERSON><PERSON><PERSON>i", "com_nav_text_to_speech": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_theme": "<PERSON><PERSON>", "com_nav_theme_dark": "Tumma", "com_nav_theme_light": "Vaalea", "com_nav_theme_system": "<PERSON><PERSON>", "com_nav_tool_dialog": "Avustajatyökalut", "com_nav_tool_dialog_description": "Avustaja täyt<PERSON>, jotta ty<PERSON>kalu<PERSON><PERSON>.", "com_nav_tool_remove": "Poista", "com_nav_tool_search": "Hakutyökalut", "com_nav_user": "KÄYTTÄJÄ", "com_nav_user_name_display": "Näytä käyttäjänimi viesteissä", "com_nav_voice_select": "<PERSON><PERSON><PERSON>", "com_show_agent_settings": "Näytä Agentin <PERSON>", "com_show_completion_settings": "Näytä Vastausasetukset", "com_show_examples": "Näytä esimerkit", "com_sidepanel_assistant_builder": "<PERSON><PERSON><PERSON><PERSON> r<PERSON>", "com_sidepanel_attach_files": "<PERSON><PERSON><PERSON>", "com_sidepanel_hide_panel": "<PERSON><PERSON><PERSON> si<PERSON>", "com_sidepanel_manage_files": "<PERSON><PERSON><PERSON>", "com_sidepanel_parameters": "Parametrit", "com_ui_accept": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_add": "Lisää", "com_ui_add_model_preset": "Lisää malli tai esiasetus lisävastausta varten", "com_ui_admin": "Ylläpito", "com_ui_admin_settings": "Ylläpitoasetukset", "com_ui_advanced": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_all": "kaikki", "com_ui_all_proper": "<PERSON><PERSON><PERSON>", "com_ui_archive": "Arkisto", "com_ui_archive_error": "Keskustelun arkistointi epä<PERSON>nistui", "com_ui_ascending": "<PERSON><PERSON><PERSON>", "com_ui_assistant": "Avustaja", "com_ui_assistant_delete_error": "<PERSON><PERSON><PERSON><PERSON> poistos<PERSON> tapah<PERSON>i virhe", "com_ui_assistant_deleted": "<PERSON><PERSON><PERSON><PERSON> poisto on<PERSON>", "com_ui_assistants": "A<PERSON><PERSON><PERSON><PERSON>", "com_ui_assistants_output": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "com_ui_attach_error": "Tiedosto ei voi liittää. Luo tai valitse keskustelu, tai kokeile ladata sivu uudesta<PERSON>.", "com_ui_attach_error_openai": "Avustajan tiedostoja ei voi liittää muihin päätepisteisiin", "com_ui_attach_error_size": "Tiedoston koko ylittää päätepisteen rajan:", "com_ui_attach_error_type": "Päätepiste ei tue tiedostotyyppiä::", "com_ui_attach_warn_endpoint": "Ilman yhteensopivaa työkalua muut kuin Avustajan tiedostot voidaan jättää huomiotta.", "com_ui_attachment": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_authentication": "Autentikointi", "com_ui_avatar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_back_to_chat": "<PERSON><PERSON><PERSON> k<PERSON>", "com_ui_back_to_prompts": "<PERSON><PERSON><PERSON>", "com_ui_bookmark_delete_confirm": "<PERSON><PERSON><PERSON>, että haluat poistaa tämän kir<PERSON>?", "com_ui_bookmarks": "Kirjanmer<PERSON><PERSON>", "com_ui_bookmarks_add_to_conversation": "Lisää nykyiseen keskusteluun", "com_ui_bookmarks_count": "Määrä", "com_ui_bookmarks_create_error": "<PERSON><PERSON><PERSON> luomisessa", "com_ui_bookmarks_create_success": "Kirjanmerk<PERSON> luotu onnistuneesti", "com_ui_bookmarks_delete_error": "<PERSON><PERSON><PERSON> poistam<PERSON>", "com_ui_bookmarks_delete_success": "Kirjanmerk<PERSON> poistettu onnistuneesti", "com_ui_bookmarks_description": "<PERSON><PERSON><PERSON>", "com_ui_bookmarks_new": "<PERSON><PERSON><PERSON>", "com_ui_bookmarks_title": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmarks_update_error": "<PERSON><PERSON><PERSON> kir<PERSON> päivittämisessä", "com_ui_bookmarks_update_success": "Kirjanmerkki päivitetty onnistuneesti", "com_ui_cancel": "Peruuta", "com_ui_chat": "Keskustelu", "com_ui_clear": "Tyhjennä", "com_ui_close": "Sulje", "com_ui_code": "<PERSON><PERSON><PERSON>", "com_ui_command_placeholder": "Valinnainen: <PERSON><PERSON><PERSON> s<PERSON>tteelle. Oletuskäskynä on nimi.", "com_ui_command_usage_placeholder": "Valitse s<PERSON>öte käskyn tai nimen perusteella", "com_ui_confirm_action": "Vahvista toiminto", "com_ui_context": "Konteks<PERSON>", "com_ui_continue": "Jatka", "com_ui_copied": "Kopioitu!", "com_ui_copied_to_clipboard": "Ko<PERSON><PERSON>u leikepöydältä", "com_ui_copy_code": "<PERSON><PERSON><PERSON> koodi", "com_ui_copy_link": "<PERSON><PERSON><PERSON>", "com_ui_copy_to_clipboard": "<PERSON><PERSON><PERSON>yd<PERSON>", "com_ui_create": "<PERSON><PERSON>", "com_ui_create_link": "<PERSON><PERSON>", "com_ui_create_prompt": "<PERSON><PERSON>", "com_ui_dashboard": "Työpöytä", "com_ui_date": "Päivämäärä", "com_ui_date_april": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_august": "<PERSON><PERSON><PERSON>", "com_ui_date_december": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_february": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_date_january": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_july": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_date_june": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_date_march": "Maalis<PERSON><PERSON>", "com_ui_date_may": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_november": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_october": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_previous_30_days": "Edelliset 30 päivää", "com_ui_date_previous_7_days": "Edelliset 7 päivää", "com_ui_date_september": "Syyskuu", "com_ui_date_today": "Tänää<PERSON>", "com_ui_date_yesterday": "<PERSON><PERSON><PERSON>", "com_ui_decline": "En hyväksy", "com_ui_delete": "Poista", "com_ui_delete_assistant_confirm": "<PERSON><PERSON><PERSON><PERSON> varmasti poistaa tämän <PERSON>? Poistoa ei voi perua.", "com_ui_delete_confirm": "<PERSON>ämä suorittaa poiston", "com_ui_delete_confirm_prompt_version_var": "Tämä poistaa valitun version \"{{0}}\":lta. <PERSON><PERSON> muita versioita ei ole, s<PERSON><PERSON>te poiste<PERSON><PERSON> samalla.", "com_ui_delete_conversation": "Poista keskustelu?", "com_ui_delete_prompt": "Poista syöte?", "com_ui_descending": "<PERSON><PERSON><PERSON>", "com_ui_description": "<PERSON><PERSON><PERSON>", "com_ui_description_placeholder": "Valinnainen: <PERSON><PERSON><PERSON><PERSON> kuvaus s<PERSON>ötteelle", "com_ui_download_error": "<PERSON><PERSON><PERSON> la<PERSON>ami<PERSON>. <PERSON><PERSON><PERSON><PERSON> on saatettu poistaa.", "com_ui_edit": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_enter": "Syötä", "com_ui_error": "<PERSON><PERSON><PERSON>", "com_ui_error_connection": "Palvelimeen yhdistäessä tap<PERSON>i virhe. Kokeile ladata sivu u<PERSON>.", "com_ui_error_save_admin_settings": "Ylläpitoasetusten tallentamisessa tapahtui virhe.", "com_ui_examples": "Esimerkkejä", "com_ui_field_required": "Tämä kenttä on pakollinen", "com_ui_filter_prompts_name": "Syötte<PERSON> nimisuodatus", "com_ui_fork": "<PERSON><PERSON><PERSON>", "com_ui_fork_all_target": "Sisällytä kaikki tänne/täältä", "com_ui_fork_branches": "Sisällytä sivupolut", "com_ui_fork_change_default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_fork_default": "Käytä oletushaarautustapaa", "com_ui_fork_error": "<PERSON><PERSON><PERSON><PERSON><PERSON> ha<PERSON> tap<PERSON>i virhe", "com_ui_fork_from_message": "<PERSON><PERSON><PERSON>", "com_ui_fork_info_1": "Käytä tätä asetusta viestien haarauttamiseen halutulla tavalla.", "com_ui_fork_info_2": "\"Haarauttaminen\" luo uuden keskustelun siten, että se alkaa/päättyy tietyistä tämänhetkisen keskustelun viesteistä, luoden kopion halutulla tavalla.", "com_ui_fork_info_3": "\"Kohdeviesti\" tark<PERSON><PERSON>a joko viesti<PERSON>, josta tämä ponnah<PERSON>ik<PERSON> a<PERSON>, tai, jos rastitat \"{{0}}\", viimeisintä viestiä keskustelussa.", "com_ui_fork_info_branches": "Tämä vaihtoehto haarauttaa näkyvissä olevat viestit sekä niihin liittyvät sivupolut; toisin sanoen, suoran polun kohdeviestiin sisällyttäen matkalla olevat sivupolut.", "com_ui_fork_info_remember": "<PERSON><PERSON> t<PERSON> on valittu, tallentaa tehdyt valinnat tulevaa jatkokäyttöä varten nopeuttaen keskusteluhaarojen luomista samoilla asetuksilla.", "com_ui_fork_info_start": "<PERSON><PERSON> t<PERSON> on valittu, ha<PERSON><PERSON>aminen alkaa tästä viestistä keskustelun viimeiseen viestiin saakka, yllä valitun toimint<PERSON> muka<PERSON>.", "com_ui_fork_info_target": "Tämä vaihtoehto haarauttaa kaikki viestit kohdeviesti<PERSON> asti, sis<PERSON>llyttäen sen naapurit; toisin sanoen, kaikki sivupolut riippumatta siitä ovatko ne näkyvissä tai samalla polulla tulevat matkaan.", "com_ui_fork_info_visible": "Tämä vaihtoehto haarauttaa vain näkyvissä olevat viestit; toisin sano<PERSON>, su<PERSON> polun k<PERSON>, il<PERSON> si<PERSON>.", "com_ui_fork_processing": "Haarautetaan keskustelua...", "com_ui_fork_remember": "Mu<PERSON>", "com_ui_fork_remember_checked": "Valintasi mui<PERSON>taan k<PERSON>ön jälk<PERSON>. Voit muuttaa tätä milloin tahansa asetuk<PERSON>a.", "com_ui_fork_split_target": "<PERSON><PERSON><PERSON> haara tästä", "com_ui_fork_split_target_setting": "<PERSON><PERSON><PERSON> ha<PERSON> o<PERSON>a koh<PERSON>vies<PERSON>ä", "com_ui_fork_success": "<PERSON><PERSON><PERSON><PERSON><PERSON> ha<PERSON> on<PERSON>.", "com_ui_fork_visible": "Vain näkyvät viestit", "com_ui_go_to_conversation": "<PERSON><PERSON><PERSON> k<PERSON>", "com_ui_happy_birthday": "On 1. syntymäpäiväni!", "com_ui_host": "Host", "com_ui_image_gen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_import_conversation_error": "Keskustelujesi tuonnissa tapahtui virhe", "com_ui_import_conversation_file_type_error": "Tiedostotyyppi ei ole tuettu tuonnissa", "com_ui_import_conversation_info": "Tuo keskusteluja JSON-tiedostosta", "com_ui_import_conversation_success": "Keskust<PERSON><PERSON><PERSON><PERSON> tuonti on<PERSON>", "com_ui_input": "Syöte", "com_ui_instructions": "<PERSON><PERSON><PERSON>", "com_ui_latest_footer": "<PERSON><PERSON><PERSON> te<PERSON><PERSON> kaiki<PERSON>.", "com_ui_locked": "Lukittu", "com_ui_manage": "<PERSON><PERSON><PERSON>", "com_ui_max_tags": "<PERSON><PERSON><PERSON><PERSON>äär<PERSON> on {{0}}. käytetään viimeisimpiä arvoja.", "com_ui_mention": "<PERSON><PERSON><PERSON> p<PERSON>, <PERSON><PERSON><PERSON><PERSON> ta<PERSON> as<PERSON> vai<PERSON> siihen pikana", "com_ui_min_tags": "Enempää arvoja ei voida poistaa. Niiden minimimäärä on {{0}}.", "com_ui_model": "<PERSON><PERSON>", "com_ui_my_prompts": "<PERSON><PERSON>", "com_ui_name": "<PERSON><PERSON>", "com_ui_new_chat": "Uusi keskustelu", "com_ui_next": "<PERSON><PERSON><PERSON>", "com_ui_no": "<PERSON>i", "com_ui_no_category": "<PERSON><PERSON> kate<PERSON>", "com_ui_no_terms_content": "Ei käyttöehtoja näytettäväksi", "com_ui_nothing_found": "Mitään ei l<PERSON>yt", "com_ui_of": "/", "com_ui_off": "<PERSON><PERSON>", "com_ui_on": "Päällä", "com_ui_prev": "<PERSON><PERSON><PERSON>", "com_ui_preview": "Esikatsele", "com_ui_privacy_policy": "Tietosuojail<PERSON><PERSON><PERSON>", "com_ui_prompt": "Syöte", "com_ui_prompt_already_shared_to_all": "Tämä s<PERSON> on jo jaettu kaikille käyttäjille", "com_ui_prompt_name": "Syötteen nimi", "com_ui_prompt_name_required": "Syötteen nimi on pakollinen", "com_ui_prompt_preview_not_shared": "Tekijä ei ole sallinut yhteistyötä tälle s<PERSON>tteelle.", "com_ui_prompt_text": "<PERSON><PERSON><PERSON>", "com_ui_prompt_text_required": "<PERSON><PERSON><PERSON> on pakollinen", "com_ui_prompt_update_error": "Syötteen päivityksessä tap<PERSON>i virhe", "com_ui_prompts": "Syötteet", "com_ui_prompts_allow_create": "<PERSON><PERSON> s<PERSON> luominen", "com_ui_prompts_allow_share_global": "<PERSON><PERSON> s<PERSON>iden jakaminen kaikille k<PERSON>yttäjille", "com_ui_prompts_allow_use": "<PERSON>li s<PERSON>tteiden k<PERSON>yttäminen", "com_ui_read_aloud": "<PERSON><PERSON>", "com_ui_regenerate": "<PERSON><PERSON>", "com_ui_rename": "<PERSON><PERSON><PERSON>", "com_ui_result": "<PERSON><PERSON>", "com_ui_revoke": "Peruuta", "com_ui_revoke_info": "<PERSON>uta kaikki k<PERSON>jän antamat tunnist<PERSON>t", "com_ui_save": "<PERSON><PERSON><PERSON>", "com_ui_save_submit": "Tallenna & Lähetä", "com_ui_saved": "Tallennettu!", "com_ui_select": "Valitse", "com_ui_select_model": "Valitse malli", "com_ui_select_search_model": "Hae mallia nimen perusteella", "com_ui_select_search_plugin": "Hae lisäosaa nimen perusteella", "com_ui_share": "Jaa", "com_ui_share_create_message": "Nimesi ja jakamisen jälkeen lisätäämäsi viestit pysyvät yksityisinä.", "com_ui_share_delete_error": "<PERSON><PERSON><PERSON> linkin poistossa tapahtui virhe", "com_ui_share_error": "Keskustelulinkin jakamisessa tapahtui virhe", "com_ui_share_link_to_chat": "Jaa linkki keskusteluun", "com_ui_share_to_all_users": "Jaa kaikille k<PERSON>yttäjille", "com_ui_share_update_message": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, ja ma<PERSON><PERSON><PERSON>et viestit jotka lisäät jakamisen jälkeen jäävät yksityisiksi.", "com_ui_share_var": "Jaa {{0}}", "com_ui_shared_link_not_found": "Jakolinkki ei löytynyt", "com_ui_shared_prompts": "Jaetut s<PERSON>tteet", "com_ui_show_all": "Näytä kaikki", "com_ui_simple": "Yksinkertainen", "com_ui_size": "<PERSON><PERSON>", "com_ui_special_variables": "Erikoismuuttujat: K<PERSON>ytä {{current_date}} kuluvaa päivämää<PERSON><PERSON><PERSON> varten, ja {{current_user}} käyttäjätunnustasi varten.", "com_ui_stop": "Pysäytä", "com_ui_storage": "<PERSON><PERSON><PERSON>", "com_ui_submit": "Lähetä", "com_ui_terms_and_conditions": "Käyttöehdot", "com_ui_terms_of_service": "Käyttöehdot", "com_ui_tools": "Työkalut", "com_ui_unarchive": "<PERSON><PERSON><PERSON> a<PERSON>", "com_ui_unarchive_error": "<PERSON><PERSON><PERSON> a<PERSON> e<PERSON>", "com_ui_unknown": "Tuntematon", "com_ui_update": "Pä<PERSON><PERSON>", "com_ui_upload": "Lataa", "com_ui_upload_delay": "\"{{0}}\" lataaminen kestää odotettua pidempään. Ole hyvä ja odota kunnes tiedosto sa<PERSON>an indeksoitua tiedonhak<PERSON> varten.", "com_ui_upload_error": "Tiedoston lataamisessa tapahtui virhe", "com_ui_upload_files": "<PERSON><PERSON><PERSON>", "com_ui_upload_invalid": "<PERSON><PERSON><PERSON><PERSON><PERSON> ladattava tiedosto. Tiedoston täytyy olla kokorajaan mahtuva kuvatiedosto", "com_ui_upload_invalid_var": "Virheellinen ladattava tiedosto. Tiedoston täytyy olla enintään {{0}} MB kokoinen kuvatiedosto", "com_ui_upload_success": "Tiedost<PERSON> lataus on<PERSON>ui", "com_ui_use_micrphone": "Käytä mikrofonia", "com_ui_variables": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_variables_info": "Käytä kaksoisaaltosulkeita tekstissäsi muuttujien luomiseen, esim. {{esimerkkimuuttuja}}. Muuttujia voi täyttää myöhemmin syötettä käyttäessä.", "com_ui_version_var": "V<PERSON><PERSON> {{0}}", "com_ui_versions": "<PERSON><PERSON><PERSON>", "com_ui_yes": "K<PERSON><PERSON>ä", "com_user_message": "Sin<PERSON>"}