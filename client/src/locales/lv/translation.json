{"chat_direction_left_to_right": "kaut kam šeit ir jābūt. bija tukšs", "chat_direction_right_to_left": "kaut kam šeit ir jābūt. bija tukšs", "com_a11y_ai_composing": "Mākslīgais intelekts joprojām veido.", "com_a11y_end": "Mākslīgais intelekts ir pabeidzis atbildi.", "com_a11y_start": "Mākslīgais intelekts ir sācis savu atbildi.", "com_agents_allow_editing": "Atļaut citiem lietotājiem rediģēt jūsu aģentu", "com_agents_by_librechat": "no LibreChat", "com_agents_code_interpreter": "Ja šī opcija ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, jū<PERSON> aģents var izmantot LibreChat koda interpretētāja API, lai droši palaistu ģenerēto kodu, tostarp failu apstrādi. Nepieciešama derīga API atslēga.", "com_agents_code_interpreter_title": "Koda interpretētāja API", "com_agents_create_error": "Izveido<PERSON><PERSON> j<PERSON> aģentu, rad<PERSON><PERSON>.", "com_agents_description_placeholder": "Pēc izvēles: aprakstiet savu aģentu šeit", "com_agents_enable_file_search": "Iespē<PERSON><PERSON> failu me<PERSON>", "com_agents_file_context": "<PERSON><PERSON><PERSON> (OCR)", "com_agents_file_context_disabled": "Pirms failu augšupielādes failu kontekstam ir jāizveido aģents.", "com_agents_file_context_info": "<PERSON><PERSON><PERSON>, kas au<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>, tiek a<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, lai i<PERSON><PERSON><PERSON> tekstu, kas pēc tam tiek pievienots aģenta norādījumiem. Ideāli piemērots dokumentiem, attēliem ar tekstu vai PDF failiem, kuriem ne<PERSON>šams pilns faila teksta saturs.", "com_agents_file_search_disabled": "Pirms failu augšupielādes failu meklēšanai ir jāizveido aģents.", "com_agents_file_search_info": "Kad <PERSON><PERSON> opcija ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, aģents tiks informēts par precīziem tālāk norādītajiem failu nosaukumiem, <PERSON><PERSON><PERSON>t tam izgūt atbilstošu kontekstu no šiem failiem.", "com_agents_instructions_placeholder": "<PERSON><PERSON><PERSON><PERSON> instruk<PERSON>, ko <PERSON><PERSON> aģents", "com_agents_mcp_description_placeholder": "<PERSON><PERSON><PERSON> v<PERSON>, ko tas dara.", "com_agents_mcp_icon_size": "Minim<PERSON>lais izmērs 128 x 128 px", "com_agents_mcp_info": "MCP serveru <PERSON> savam aģentam, lai tas varētu veikt uzdevumus un mijiedarboties ar ārējiem pakalpojumiem.", "com_agents_mcp_name_placeholder": "Pielāgotais rīks", "com_agents_mcp_trust_subtext": "LibreChat nav validējis neofic<PERSON><PERSON><PERSON>", "com_agents_mcps_disabled": "Pirms MCP pievienošanas ir jāizveido aģents.", "com_agents_missing_provider_model": "Pirms aģenta izveides izvēlieties pakalpojumu sniedzēju un modeli.", "com_agents_name_placeholder": "Pēc izvēles: aģenta nosaukums", "com_agents_no_access": "Jums nav piekļuves šī aģenta rediģēšanai.", "com_agents_no_agent_id_error": "Nav atrasts aģenta <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka aģents ir iz<PERSON>.", "com_agents_not_available": "Aģents nav pieejams", "com_agents_search_info": "Ja <PERSON>ī opcija ir i<PERSON><PERSON>, jū<PERSON> aģents var meklēt jaunāko informāciju tīmeklī. Nepieciešama derīga API atslēga.", "com_agents_search_name": "Meklēt aģentus pēc nosaukuma", "com_agents_update_error": "<PERSON><PERSON><PERSON> aģenta atjaunin<PERSON>ā ir k<PERSON>da.", "com_assistants_action_attempt": "Asistents v<PERSON><PERSON> runāt ar {{0}}", "com_assistants_actions": "<PERSON><PERSON><PERSON><PERSON>", "com_assistants_actions_disabled": "Pirms darbību pievienošanas ir jāizveido asistents.", "com_assistants_actions_info": "Ļaujiet savam asistentam i<PERSON>t informāciju vai veikt darb<PERSON>, izmantojot API.", "com_assistants_add_actions": "<PERSON><PERSON><PERSON>", "com_assistants_add_tools": "<PERSON><PERSON><PERSON>", "com_assistants_allow_sites_you_trust": "Atļaujiet tikai tās viet<PERSON>, kurām uzticaties.", "com_assistants_append_date": "Pievienot pašreizējo datumu un laiku", "com_assistants_append_date_tooltip": "Ja <PERSON><PERSON> opcija ir i<PERSON><PERSON><PERSON>, pa<PERSON><PERSON>iz<PERSON><PERSON><PERSON> klienta datums un laiks tiks pievienots asistenta sistēmas norādījumiem.", "com_assistants_attempt_info": "Asistents v<PERSON><PERSON>:", "com_assistants_available_actions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_assistants_capabilities": "<PERSON>es<PERSON>ējas", "com_assistants_code_interpreter": "Koda interpretētājs", "com_assistants_code_interpreter_files": "<PERSON><PERSON><PERSON>āk norādītie faili attiecas tikai uz Code interpretētāju:", "com_assistants_code_interpreter_info": "Koda interpretētā<PERSON><PERSON>j asistentam rakstīt un palaist kodu. <PERSON><PERSON> rīks var apstrādāt failus ar dažādiem datiem un formatējumu, kā arī ģenerēt failus, piem<PERSON><PERSON>, diagrammas.", "com_assistants_completed_action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ar {{0}}", "com_assistants_completed_function": "<PERSON><PERSON><PERSON> {{0}}", "com_assistants_conversation_starters": "<PERSON><PERSON><PERSON>", "com_assistants_conversation_starters_placeholder": "Ievadiet sarunu s<PERSON> p<PERSON>tu", "com_assistants_create_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>, ir <PERSON><PERSON><PERSON><PERSON>.", "com_assistants_create_success": "Veiksmīgi izveidots", "com_assistants_delete_actions_error": "<PERSON><PERSON><PERSON><PERSON> d<PERSON>ot š<PERSON> da<PERSON>.", "com_assistants_delete_actions_success": "Darbība veiksmīgi dz<PERSON>sta no asistenta", "com_assistants_description_placeholder": "Pēc izvēles: Šeit aprakstiet savu asistentu", "com_assistants_domain_info": "Asistents nosūtīja š<PERSON> inform<PERSON> {{0}}", "com_assistants_file_search": "<PERSON><PERSON><PERSON>", "com_assistants_file_search_info": "Failu meklēšana <PERSON>j asistentam iegūt zināša<PERSON> no failiem, kurus augšupielādējat jūs vai jūsu lietotāji. Kad fails ir augšupielādēts, asistents automātiski izlemj, kad, pamatojoties uz lietotāja pieprasījumiem, izg<PERSON>t saturu. Failu meklēšanas vektoru krātuvju pievienošana vēl nav atbalstīta. Tos varat pievienot no pakalpojumu sniedzēja testa laukuma vai pievienot failus ziņu failu meklēšanai pēc pavediena.", "com_assistants_function_use": "<PERSON>zman<PERSON>tais asistents {{0}}", "com_assistants_image_vision": "<PERSON><PERSON><PERSON><PERSON>", "com_assistants_instructions_placeholder": "<PERSON><PERSON><PERSON><PERSON>, ko i<PERSON> asistents", "com_assistants_knowledge": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_assistants_knowledge_disabled": "Pirms failu augšupielādes kā Z<PERSON>āšanas ir jāizveido asistents un jāaktivizē un jāsaglabā koda interpretētājs vai failu atgūšana.", "com_assistants_knowledge_info": "<PERSON>a <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> failus sad<PERSON><PERSON><PERSON>, sa<PERSON><PERSON><PERSON> ar asistentu var tikt iekļauts faila saturs.", "com_assistants_max_starters_reached": "Sasnie<PERSON>s maks<PERSON><PERSON><PERSON>u <PERSON> iespēju skaits", "com_assistants_name_placeholder": "Pēc izvēles: <PERSON><PERSON><PERSON><PERSON>", "com_assistants_non_retrieval_model": "<PERSON><PERSON><PERSON> modelī failu meklēšana nav iespējota. <PERSON><PERSON><PERSON><PERSON>, izvēlieties citu modeli.", "com_assistants_retrieval": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_assistants_running_action": "Darbība palaista", "com_assistants_running_var": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{0}}", "com_assistants_search_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> asistentus pēc no<PERSON>", "com_assistants_update_actions_error": "Kļūda darbības izveidē vai atjaunināšanā.", "com_assistants_update_actions_success": "Veiksmīgi izveidota vai atjaunināta darbība", "com_assistants_update_error": "<PERSON><PERSON><PERSON> asist<PERSON>a <PERSON> notika k<PERSON>.", "com_assistants_update_success": "Veiks<PERSON><PERSON><PERSON>", "com_auth_already_have_account": "Jau ir konts?", "com_auth_apple_login": "<PERSON><PERSON><PERSON><PERSON>, izmantojot Apple", "com_auth_back_to_login": "Atgriezties pie pieteikšanās", "com_auth_click": "Noklikšķiniet uz", "com_auth_click_here": "Noklikšķiniet šeit", "com_auth_continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_create_account": "Izveidojiet savu kontu", "com_auth_discord_login": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "com_auth_email": "E-pasts", "com_auth_email_address": "E-pasta adrese", "com_auth_email_max_length": "E-pasts ne<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> gar<PERSON> par 120 rakstzīmēm.", "com_auth_email_min_length": "E-pastam j<PERSON><PERSON><PERSON><PERSON> vismaz 6 rakstzīmēm", "com_auth_email_pattern": "Jums jāievada derīga e-pasta adrese", "com_auth_email_required": "Nepieciešams e-pasts", "com_auth_email_resend_link": "Atkārtoti nosūtīt e-pastu", "com_auth_email_resent_failed": "Neizdevās atkārtoti nosūtīt verifikācijas e-pastu", "com_auth_email_resent_success": "Veiksmīgi nosūtīts atkārtots verifikācijas e-pasts", "com_auth_email_verification_failed": "E-pasta verifik<PERSON><PERSON>ja neizdev<PERSON>s", "com_auth_email_verification_failed_token_missing": "Verifik<PERSON><PERSON><PERSON>, trūks<PERSON> <PERSON>a", "com_auth_email_verification_in_progress": "Jūsu e-pasta veri<PERSON><PERSON><PERSON>, l<PERSON><PERSON><PERSON>, uz<PERSON><PERSON>t", "com_auth_email_verification_invalid": "Nederīga e-pasta verifikācija", "com_auth_email_verification_redirecting": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{0}} sekundēs...", "com_auth_email_verification_resend_prompt": "Nesaņē<PERSON>āt e-pastu?", "com_auth_email_verification_success": "E-pasts <PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_email_verifying_ellipsis": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "com_auth_error_create": "Notika k<PERSON>ūda reģistrējot jūsu kontu. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>.", "com_auth_error_invalid_reset_token": "Šis paroles atiest<PERSON><PERSON>šanas tokens vairs nav derīgs.", "com_auth_error_login": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> informāciju nav iespējams pieteikties. <PERSON><PERSON><PERSON><PERSON>, pārbaudiet sava lietotāja informāciju un mēģiniet vēlreiz.", "com_auth_error_login_ban": "<PERSON><PERSON><PERSON> konts ir uz laiku bloķēts noteikumu pārkāpumu dēļ.", "com_auth_error_login_rl": "P<PERSON>rāk daudzi pieteikšanās mēģinājumi īsā laikā. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz vēlāk.", "com_auth_error_login_server": "<PERSON><PERSON><PERSON><PERSON>a k<PERSON>. <PERSON><PERSON><PERSON><PERSON>, pagaidiet dažas minūtes un mēģiniet vēlreiz.", "com_auth_error_login_unverified": "<PERSON><PERSON><PERSON> konts nav verificēts. <PERSON><PERSON><PERSON><PERSON>, p<PERSON>rbaud<PERSON> savu e-pastu, lai saņemtu verifikācijas saiti.", "com_auth_facebook_login": "<PERSON><PERSON><PERSON><PERSON><PERSON> ar <PERSON>", "com_auth_full_name": "Pilnais vārds/uzvārds", "com_auth_github_login": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_google_login": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "com_auth_here": "ŠEIT", "com_auth_login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_login_with_new_password": "Tagad varat pieteikties, izmantojot jauno paroli.", "com_auth_name_max_length": "<PERSON><PERSON><PERSON>ma<PERSON> jābūt īs<PERSON>kam par 80 rakstzīmēm", "com_auth_name_min_length": "Vārdam jābūt vismaz 3 rakstzīmēm", "com_auth_name_required": "<PERSON><PERSON><PERSON> ir obligāts", "com_auth_no_account": "Vai jums nav vēl konta?", "com_auth_password": "Parole", "com_auth_password_confirm": "<PERSON><PERSON><PERSON>", "com_auth_password_forgot": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>?", "com_auth_password_max_length": "<PERSON><PERSON><PERSON> par 128 rakstzīmēm", "com_auth_password_min_length": "Parolei jābūt vismaz 8 rakstzīmēm", "com_auth_password_not_match": "<PERSON><PERSON><PERSON>", "com_auth_password_required": "<PERSON><PERSON><PERSON> ir oblig<PERSON>ta", "com_auth_registration_success_generic": "<PERSON><PERSON><PERSON><PERSON>, p<PERSON><PERSON><PERSON><PERSON> savu e-pastu, lai apstiprinātu savu e-pasta adresi.", "com_auth_registration_success_insecure": "Reģistrācija ir ve<PERSON>ga.", "com_auth_reset_password": "<PERSON><PERSON><PERSON>", "com_auth_reset_password_if_email_exists": "Ja konts ar šo e-pasta adresi ir iz<PERSON><PERSON>, ir nosūtīts e-pasts ar paroles atiestatīšanas norādījumiem. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ka pārbaudāt savu spam mapi.", "com_auth_reset_password_link_sent": "E-pasts no<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_auth_reset_password_success": "Parol<PERSON> atiest<PERSON><PERSON>", "com_auth_saml_login": "Turpināt ar SAML", "com_auth_sign_in": "Pierakstī<PERSON>", "com_auth_sign_up": "Reģistrēties", "com_auth_submit_registration": "<PERSON><PERSON><PERSON><PERSON> reģistrāciju", "com_auth_to_reset_your_password": "lai atiestat<PERSON> paroli.", "com_auth_to_try_again": "lai mēģinātu vēlreiz.", "com_auth_two_factor": "Pārbaudiet jūsu MFA vienreizējās paroles lietojumprogrammu, lai atrastu kodu", "com_auth_username": "Lietotājvārds (pēc izvēles)", "com_auth_username_max_length": "Lietotāj<PERSON><PERSON><PERSON><PERSON> jābūt īsākam par 20 rakstzīmēm", "com_auth_username_min_length": "Lietotājvārdam jābūt vismaz 2 rakstzīmēm garam", "com_auth_verify_your_identity": "Verificējiet savu identitāti", "com_auth_welcome_back": "Lai<PERSON><PERSON> lūgti atpakaļ", "com_citation_more_details": "<PERSON><PERSON><PERSON><PERSON> par {{label}}", "com_citation_source": "Avots", "com_click_to_download": "(noklikšķiniet šeit, lai lejupielādētu)", "com_download_expired": "(leju<PERSON><PERSON><PERSON><PERSON> derīguma <PERSON>)", "com_download_expires": "(noklikšķiniet šeit, lai leju<PERSON> — derīguma termiņ<PERSON> be<PERSON> {{0}})", "com_endpoint": "Galapunkts", "com_endpoint_agent": "Aģents", "com_endpoint_agent_model": "Aģenta modelis (ieteicams: GPT-3.5)", "com_endpoint_agent_placeholder": "Lūdzu, izvēlieties aģentu", "com_endpoint_ai": "Mākslīgais intelekts", "com_endpoint_anthropic_maxoutputtokens": "<PERSON>ks<PERSON><PERSON><PERSON><PERSON> atbildē ģenerējamo tokenu skaits. Norādiet zemāku vērtību īsākām atbildēm un augstāku vērtību garākām atbildēm. Piezīme: modeļi var apstāties pirms šī maksimā<PERSON>ā skaita sasnie<PERSON>.", "com_endpoint_anthropic_prompt_cache": "Uzvednes kešatmiņa ļauj atkārtoti izmantot lielu kontekstu vai instrukcijas API izsaukumos, samazinot izmaksas un ābildes ātrumu.", "com_endpoint_anthropic_temp": "Diapazons no 0 līdz 1. Analītiskiem/atbilžu variantiem izmantojiet temp vērtību tuvāk 0, bet radošiem un ģeneratīviem uzdevumiem — tuvāk 1. <PERSON><PERSON>ak<PERSON><PERSON> mainīt šo vai Top P, bet ne abus.", "com_endpoint_anthropic_thinking": "Iespējo i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (3.7 Sonnet). Piezīme: nepiecieša<PERSON> iestatīt \"<PERSON><PERSON><PERSON><PERSON> budžetu\", kam ar<PERSON> jābūt zemākam par \"Max Output Tokens\".", "com_endpoint_anthropic_thinking_budget": "<PERSON><PERSON><PERSON> maks<PERSON><PERSON>, ko <PERSON> dr<PERSON> izman<PERSON>t savā iekšējā domā<PERSON> procesā. <PERSON><PERSON><PERSON><PERSON> budžeti var uzlabot atbilžu kvalit<PERSON>ti, nod<PERSON><PERSON><PERSON><PERSON> rūpīgāku analīzi sarežģītām problēmām, la<PERSON> gan <PERSON> var neizmantot visu piešķirto budžetu, īpaši diapazonos virs 32 000. <PERSON><PERSON> iestatījumam jābūt zemākam par \"Maksim<PERSON><PERSON> izvades tokeni\".", "com_endpoint_anthropic_topk": "Top-k maina to, kā modelis atlasa marķierus izvadei. Ja top-k ir 1, tas noz<PERSON><PERSON><PERSON>, ka atlasītais marķieris ir visticamākais starp visiem modeļa vārdu krājumā esošajiem marķieriem (to sauc arī par alkat<PERSON><PERSON>), savu<PERSON><PERSON><PERSON>, ja top-k ir 3, tas noz<PERSON><PERSON><PERSON>, ka nākamais marķieris tiek izvēlēts no 3 visticamākajiem marķieriem (izmantojot temperatūru).", "com_endpoint_anthropic_topp": "`Top-p` maina to, kā modelis atlasa marķierus izvadei. Marķieri tiek atlasīti no K (skatīt parametru topK) ticamākās līdz vismazāk ticamajai, līdz to varb<PERSON><PERSON><PERSON>bu summa ir vienāda ar `top-p` vērt<PERSON>bu.", "com_endpoint_anthropic_use_web_search": "Iespējo<PERSON><PERSON> tīmek<PERSON><PERSON> me<PERSON><PERSON>cion<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> Anthropic iebūvētā<PERSON> meklēša<PERSON> iespējas. Tas ļauj modelim meklēt tīmek<PERSON>ī jaunāko informāciju un sniegt precīzākas un aktuālākas atbildes.", "com_endpoint_assistant": "Asistents", "com_endpoint_assistant_model": "Asistenta modelis", "com_endpoint_assistant_placeholder": "<PERSON><PERSON><PERSON><PERSON>, lab<PERSON><PERSON> sānu panelī atlasiet asistentu.", "com_endpoint_completion": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_completion_model": "Pabeigšanas modelis (ieteicams: GPT-4)", "com_endpoint_config_click_here": "Noklikšķiniet šeit", "com_endpoint_config_google_api_info": "<PERSON> iegūtu savu Ģeneratīvās valodas API atslēgu (Gemini),", "com_endpoint_config_google_api_key": "Google API atslēga", "com_endpoint_config_google_cloud_platform": "(no Google mākoņplatformas)", "com_endpoint_config_google_gemini_api": "(Gemini API)", "com_endpoint_config_google_service_key": "Google pakalpojuma konta atslēga", "com_endpoint_config_key": "Iestatīt API atslēgu", "com_endpoint_config_key_encryption": "<PERSON><PERSON><PERSON> at<PERSON>lēga tiks šifrēta un dzēsta plkst.", "com_endpoint_config_key_for": "Iestatīt API atslēgu priekš", "com_endpoint_config_key_google_need_to": "Tev vajag", "com_endpoint_config_key_google_service_account": "Izveidot servisa kontu", "com_endpoint_config_key_google_vertex_ai": "Iespējot Vertex AI", "com_endpoint_config_key_google_vertex_api": "API pakalpojumā Google Cloud, pēc tam", "com_endpoint_config_key_google_vertex_api_role": "Noteikti noklikšķiniet uz “Izveidot un turpināt”, lai piešķirtu vismaz lomu “Vertex AI User”. Visbeidzot, izveidojiet JSON atslēgu, lai to importēt šeit.", "com_endpoint_config_key_import_json_key": "Importēt servisa konta JSON atslēgu.", "com_endpoint_config_key_import_json_key_invalid": "Nederīga servisa konta JSON atslēga. Vai importējāt pareizo failu?", "com_endpoint_config_key_import_json_key_success": "Servisa konta JSON atslēga veiksmīgi importēta", "com_endpoint_config_key_name": "Atslēga", "com_endpoint_config_key_never_expires": "<PERSON><PERSON><PERSON> at<PERSON>lēgas derīguma termiņ<PERSON> nekad nebeigsies", "com_endpoint_config_placeholder": "Iestatiet savu atslēgu galvenes izvēlnē, lai izve<PERSON>tu sarunu.", "com_endpoint_config_value": "Ievadiet vērtību", "com_endpoint_context": "Konteksts", "com_endpoint_context_info": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tokenu s<PERSON>, ko var izmantot kontekstam. <PERSON><PERSON><PERSON><PERSON><PERSON> to, lai kontrol<PERSON>, cik tokenu tiek nosūtīti katrā pieprasījumā. Ja tas nav norādīts, tiks izmantoti sistēmas noklusējuma iestatījumi, pamatojoties uz zināmo modeļu konteksta lielumu. Augstāku vērtību iestatīšana var izraisīt kļūdas un/vai augstākas tokenu izmaksas.", "com_endpoint_context_tokens": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>ts", "com_endpoint_custom_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_default": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_default_blank": "pēc noklusējuma: tuk<PERSON>s", "com_endpoint_default_empty": "noklusējums: tuk<PERSON>s", "com_endpoint_default_with_num": "noklusējums: {{0}}", "com_endpoint_deprecated": "Novecojis", "com_endpoint_deprecated_info": "Šis galapunkts ir novecojis un var tikt noņemts turpmākajās versijās; l<PERSON><PERSON><PERSON>, tā vietā izmantojiet aģenta galapunktu.", "com_endpoint_deprecated_info_a11y": "Spraudņa galapunkts ir novecojis un var tikt noņemts turpmākajās versijās; l<PERSON><PERSON><PERSON>, tā vietā izmantojiet aģenta galapunktu.", "com_endpoint_disable_streaming": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> atbilžu straumēšanu un saņemt visu atbildi uzreiz. Noderīgi tādiem modeļiem kā o3, kas pieprasa organizācijas pārbaudi straumēšanai.", "com_endpoint_disable_streaming_label": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "com_endpoint_examples": " Iepriekšiestatījumi", "com_endpoint_export": "Eksportēt", "com_endpoint_export_share": "Eksportēt/kopīgot", "com_endpoint_frequency_penalty": "Biežuma sods", "com_endpoint_func_hover": "Iespē<PERSON><PERSON> spraud<PERSON> kā OpenAI funkcijas", "com_endpoint_google_custom_name_placeholder": "Iestatiet pielāgotu nosauku<PERSON> p<PERSON> Google", "com_endpoint_google_maxoutputtokens": "<PERSON>ks<PERSON><PERSON><PERSON><PERSON> atbildē ģenerējamo tokenu skaits. Norādiet zemāku vērtību īsākām atbildēm un augstāku vērtību garākām atbildēm. Piezīme: modeļi var apstāties pirms šī maksimā<PERSON>ā skaita sasnie<PERSON>.", "com_endpoint_google_temp": "<PERSON>st<PERSON><PERSON> vērtības = nejau<PERSON><PERSON><PERSON>, savukārt zemākas vērtības = fokusētāks un deterministiskāks. Iesakām mainīt šo vai Top P, bet ne abus.", "com_endpoint_google_thinking": "Iespējo vai atspējo spriešanas funkciju. Šo iestatījumu atbalsta tikai daži modeļi (2.5 sērija). Vecākiem modeļiem šim iestatījumam var nebūt nekādas ietekmes.", "com_endpoint_google_thinking_budget": "Norāda modeļa izman<PERSON>to do<PERSON> tokenu skaitu. Faktiskais skaits var pārsniegt vai būt mazāks par šo vērtību atkarībā no uzvednes.\n\nŠo iestatījumu atbalsta tikai noteikti modeļi (2.5 s<PERSON>rija). Gemini 2.5 Pro atbalsta 128–32 768 žetonus. Gemini 2.5 Flash atbalsta 0–24 576 žetonus. Gemini 2.5 Flash Lite atbalsta 512–24 576 žetonus.\n\nAtstājiet tukšu vai iestatiet uz \"-1\", lai modelis automātiski izlemtu, kad un cik daudz domāt. Pēc noklusējuma Gemini 2.5 Flash Lite nedomā.", "com_endpoint_google_topk": "Top-k maina to, kā modelis atlasa marķierus izvadei. Ja top-k ir 1, tas noz<PERSON><PERSON><PERSON>, ka atlasītais marķieris ir visticamākais starp visiem modeļa vārdu krājumā esošajiem marķieriem (to sauc arī par alkat<PERSON><PERSON>), savu<PERSON><PERSON><PERSON>, ja top-k ir 3, tas noz<PERSON><PERSON><PERSON>, ka nākamais marķieris tiek izvēlēts no 3 visticamākajiem marķieriem (izmantojot temperatūru).", "com_endpoint_google_topp": "`Top-p` maina to, kā modelis atlasa tokenus izvadei. Marķieri tiek atlasīti no K (skatīt parametru topK) ticamākās līdz vismazāk ticamajai, līdz to varb<PERSON><PERSON><PERSON>bu summa ir vienāda ar `top-p` vērt<PERSON>bu.", "com_endpoint_google_use_search_grounding": "Izmantojiet Google meklēšanas pama<PERSON>, lai uz<PERSON><PERSON>u atbildes ar reāllaika tīmekļa meklēšanas rezultātiem. Tas ļauj modeļiem piekļūt aktuālajai informācijai un sniegt prec<PERSON>, aktu<PERSON><PERSON><PERSON><PERSON> atbildes.", "com_endpoint_instructions_assistants": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_instructions_assistants_placeholder": "P<PERSON><PERSON><PERSON><PERSON> asistenta nor<PERSON>. <PERSON><PERSON> ir <PERSON><PERSON>, lai mainītu darbību katrā palai<PERSON>nas reizē.", "com_endpoint_max_output_tokens": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> iz<PERSON>s <PERSON>u skaits", "com_endpoint_message": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_message_new": "<PERSON><PERSON><PERSON><PERSON> {{0}}", "com_endpoint_message_not_appendable": "Rediģējiet savu ziņu vai ģenerējiet to atk<PERSON>rtoti.", "com_endpoint_my_preset": "Mans iepriekšiestatījums", "com_endpoint_no_presets": "<PERSON><PERSON>l nav iep<PERSON>kš iestatītu iestatījumu. <PERSON>, i<PERSON><PERSON><PERSON><PERSON> iestatījumu pogu.", "com_endpoint_open_menu": "Atvērt izvēlni", "com_endpoint_openai_custom_name_placeholder": "Iestatiet pielāgotu nosaukumu mākslīgajam intelektam", "com_endpoint_openai_detail": "Vision pieprasījumu izšķirtspēja. “Z<PERSON>” ir lētāka un ātrāka, “Augsta” ir detalizētāka un dārgāka, un “Automātiska” automātiski izvēlēsies vienu no abām, pamatojoties uz attēla izšķirtspēju.", "com_endpoint_openai_freq": "Skaitlis no -2,0 līdz 2,0. Pozitīvas vērtības soda jaunus tokenus, pamatojoties uz to esošo biežumu tekstā līdz šim, samazinot modeļa iespējamību atkārtot vienu un to pašu rindu burtiski.", "com_endpoint_openai_max": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ģenerējamo tokenu skaits. Ievades tokenu un ģenerēto tokenu kopējo garumu ierobežo modeļa konteksta garums.", "com_endpoint_openai_max_tokens": "Neoblig<PERSON>ts lauks “max_tokens”, kas nor<PERSON><PERSON> maksim<PERSON>lo tokenu skaitu, ko var ģenerēt sarunas pabe<PERSON> laikā. Ievades tokenu un ģenerēto tokenu kopējo garumu ierobežo modeļa konteksta garums. Ja šis skaitlis pārsniedz maksim<PERSON>lo konteksta tokenu skaitu, var rasties kļūdas.", "com_endpoint_openai_pres": "Skaitlis no -2,0 līdz 2,0. <PERSON><PERSON><PERSON><PERSON><PERSON> vērtības soda jaunus tokenus, pamatojoties uz to, vai tie līdz šim parādās tekstā, palielinot modeļa iespējamību runāt par jaunām tēmām.", "com_endpoint_openai_prompt_prefix_placeholder": "Iestatiet pielā<PERSON> instr<PERSON>, kas j<PERSON><PERSON><PERSON><PERSON> sistē<PERSON> ziņ<PERSON>. Noklusējuma vērtība: nav", "com_endpoint_openai_reasoning_effort": "Tikai o1 un o3 modeļi: i<PERSON><PERSON><PERSON><PERSON> spriešanas modeļu spriešanas piepūli. <PERSON><PERSON><PERSON><PERSON>nas piepūles samazin<PERSON>šana var nodrošin<PERSON>t ātrākas atbildes un mazāk spriešanas tokenus izmantošanas atbildē.", "com_endpoint_openai_reasoning_summary": "Tikai atbilžu API: modeļa veiktās spriešanas kopsavilkums. Tas var būt noderīgi atkļūdošanai un modeļa spriešanas procesa izpratnei. Iestatiet vērtību “nav”, “automātiski”, “kodolīgs” vai “detalizēts”.", "com_endpoint_openai_resend": "Nosūtiet vēlreiz visus iepriekš pievienotos attēlus. Piezīme. Tas var ievērojami palielināt tokena i<PERSON>, un ar daudziem attēlu pielikumiem var rasties k<PERSON>.", "com_endpoint_openai_resend_files": "Nosūtiet vēlreiz visus iepriekš pievienotos failus. Piezīme. Tas palielinās tokena i<PERSON>, un ar daudziem pielikumiem var rasties k<PERSON>.", "com_endpoint_openai_stop": "Līdz 4 secībām, kurās API pārtrauks turpmāku tokenu ģenerēšanu.", "com_endpoint_openai_temp": "<PERSON>st<PERSON><PERSON> vērtības = nejau<PERSON><PERSON><PERSON>, savukārt zemākas vērtības = fokusētāks un deterministiskāks. Iesakām mainīt šo vai Top P, bet ne abus.", "com_endpoint_openai_topp": "Alternatīva izlasei ar temperatūru, ko sauc par kodola izlasi, kur modelis ņem vērā tokenu rezultātus ar varbūtības masu top_p. Tātad 0,1 no<PERSON><PERSON><PERSON><PERSON>, ka tiek ņemti vērā tikai tie tokeni, kas veido augšējo 10% varbūtības masu. Mēs iesakām mainīt šo vai temperatūru, bet ne abus.", "com_endpoint_openai_use_responses_api": "Izmantojiet Response API sarunas pabeigšanas vietā, kas ietver paplašinātas OpenAI funkcijas. Nepieciešams o1-pro, o3-pro un spriešanas kopsavilkumu iespējošanai.", "com_endpoint_openai_use_web_search": "Iespējojiet tīmekļa mekl<PERSON> funkcionalitāti, izmantojot OpenAI iebūvētās meklēšanas iespējas. Tas ļauj modelim meklēt tīmeklī aktuālu informāciju un sniegt prec<PERSON>, aktu<PERSON><PERSON><PERSON><PERSON> atbildes.", "com_endpoint_output": "Izvade", "com_endpoint_plug_image_detail": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_plug_resend_files": "Atkārtoti nosūtīt failus", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "Iestatiet pielā<PERSON> instr<PERSON>, kas j<PERSON><PERSON><PERSON><PERSON> sistē<PERSON> ziņ<PERSON>. Noklusējuma vērtība: nav", "com_endpoint_plug_skip_completion": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_plug_use_functions": "<PERSON><PERSON><PERSON>", "com_endpoint_presence_penalty": "Klātbūtnes sods", "com_endpoint_preset": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_endpoint_preset_custom_name_placeholder": "kaut kam šeit ir jānotiek. bija tukšs", "com_endpoint_preset_default": "tagad ir noklus<PERSON>juma iestat<PERSON>.", "com_endpoint_preset_default_item": "Noklusējums:", "com_endpoint_preset_default_none": "Nav aktīvu noklusējuma iestatījumu.", "com_endpoint_preset_default_removed": "vairs nav noklusējuma iestatījums.", "com_endpoint_preset_delete_confirm": "Vai tiešām vēlaties dzēst šo iestatījumu?", "com_endpoint_preset_delete_error": "<PERSON><PERSON><PERSON><PERSON><PERSON> jūsu s<PERSON>, rad<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz.", "com_endpoint_preset_import": "Iepriekšiestatījums importēts!", "com_endpoint_preset_import_error": "Importējot jūsu s<PERSON>, rad<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz.", "com_endpoint_preset_name": "Iepriekšiestatījuma <PERSON>", "com_endpoint_preset_save_error": "Saglabājot jū<PERSON> s<PERSON>, rad<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz.", "com_endpoint_preset_selected": "Iepriekšiestatījums aktīvs!", "com_endpoint_preset_selected_title": "Aktīvs!", "com_endpoint_preset_title": "Iepriekšiestatījums", "com_endpoint_presets": "iepriekšiestatījumi", "com_endpoint_presets_clear_warning": "Vai tiešām vēlaties notīrīt visus iepriekšiestatījumus? Šī darbība ir neatgriezeniska.", "com_endpoint_prompt_cache": "Izmantojiet uzvednes kešatmiņu", "com_endpoint_prompt_prefix": "Pielā<PERSON><PERSON> instruk<PERSON>", "com_endpoint_prompt_prefix_assistants": "Pa<PERSON>ld<PERSON> instruk<PERSON>jas", "com_endpoint_prompt_prefix_assistants_placeholder": "Iestatiet papildu norādījumus vai kontekstu virs Asistenta galvenajiem norādījumiem. <PERSON>a lauks ir tuk<PERSON>, tas tiek ignor<PERSON>.", "com_endpoint_prompt_prefix_placeholder": "Iestatiet pielāgotas instrukcijas vai kontekstu. <PERSON>a lauk<PERSON> ir tuk<PERSON>, tas tiek ignor<PERSON>.", "com_endpoint_reasoning_effort": "<PERSON><PERSON><PERSON><PERSON> gr<PERSON>", "com_endpoint_reasoning_summary": "Argument<PERSON><PERSON><PERSON> k<PERSON>", "com_endpoint_save_as_preset": "Saglabāt kā iepriekšiestatījumu", "com_endpoint_search": "Meklēt galapunktu pēc no<PERSON>ukuma", "com_endpoint_search_endpoint_models": "Me<PERSON><PERSON><PERSON><PERSON> {{0}} modeļos...", "com_endpoint_search_models": "<PERSON><PERSON><PERSON><PERSON><PERSON> mode<PERSON>...", "com_endpoint_search_var": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{0}}...", "com_endpoint_set_custom_name": "Iestatiet pielā<PERSON>, ja varat atrast šo iepriekšiestatījumu", "com_endpoint_skip_hover": "<PERSON>es<PERSON><PERSON><PERSON><PERSON> soļ<PERSON>, kurā tiek pārskatīta galīgā atbilde un ģenerētie soļi", "com_endpoint_stop": "<PERSON><PERSON><PERSON><PERSON><PERSON> se<PERSON>", "com_endpoint_stop_placeholder": "Atdaliet vērt<PERSON>, nosp<PERSON>ž<PERSON> taustiņu `Enter`", "com_endpoint_temperature": "Temperat<PERSON>ra", "com_endpoint_thinking": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_thinking_budget": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "Izmantojiet aktīvo asistentu", "com_endpoint_use_responses_api": "Izmantot Response API", "com_endpoint_use_search_grounding": "<PERSON><PERSON><PERSON><PERSON><PERSON>, izmantojot Google meklēšanu", "com_error_expired_user_key": "Nodroš<PERSON>ā<PERSON>ā atslēga priekš {{0}} beid<PERSON><PERSON><PERSON> plkst. {{1}} <PERSON><PERSON><PERSON><PERSON>, norā<PERSON>t jaunu atslēgu un mēģiniet vēlreiz.", "com_error_files_dupe": "<PERSON><PERSON><PERSON> fails.", "com_error_files_empty": "Tukši faili nav atļauti.", "com_error_files_process": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>.", "com_error_files_unsupported_capability": "<PERSON><PERSON> faila tips nav atbalstīts.", "com_error_files_upload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>.", "com_error_files_upload_canceled": "Faila augšup<PERSON>ādes pieprasījums tika atcelts. Piezīme. Iespējams, ka faila augšupielāde joprojām tiek apstrād<PERSON>ta, un tā būs manuāli jādz<PERSON>.", "com_error_files_validation": "<PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>.", "com_error_google_tool_conflict": "Iebūvēto Google rīku lietošana netiek atbalstīta ar ārējiem rīkiem. <PERSON><PERSON><PERSON><PERSON>, atspējojiet vai nu iebūvētos rīkus, vai ārējos rīkus.", "com_error_heic_conversion": "Neizdevās konvertēt HEIC attēlu uz JPEG. Lūdzu, mēģiniet konvertēt attēlu manuāli vai izmantojiet citu formātu.", "com_error_input_length": "J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ziņas kopējais garums ir pār<PERSON>k garš, p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> garuma ierobežojumu, vai arī jūsu garuma ierobežojuma parametri ir nepareizi u<PERSON>, negatīvi ietekmējot kopējā konteksta garumu. Plašāka informācija: {{0}} <PERSON><PERSON><PERSON><PERSON>, saīsiniet savu ziņu, sarunas parametros pielāgojiet maksimālo garuma lielumu vai atdaliet sarunu, lai turpinātu.", "com_error_invalid_agent_provider": "\"{{0}}\" pakalpojumu sniedzējs nav pieejams lietošanai ar aģentiem. <PERSON><PERSON><PERSON><PERSON>, dodieties uz sava aģenta iestatījumiem un atlasiet pašlaik pieejamu pakalpojumu sniedzēju.", "com_error_invalid_user_key": "<PERSON><PERSON><PERSON><PERSON><PERSON> neder<PERSON>ga atslēga. <PERSON><PERSON><PERSON><PERSON>, ievadiet derīgu atslēgu un mēģiniet vēlreiz.", "com_error_moderation": "Šķiet, ka mūsu moderācijas sistēma ir atzīmējusi iesniegto saturu kā neatbilstošu mūsu vadlīnijām. M<PERSON>s nevaram turpināt darbu ar šo konkrēto tēmu. Ja jums ir vēl kādi jautājumi vai tēmas, kuras vēlat<PERSON> izp<PERSON>t<PERSON>t, l<PERSON><PERSON><PERSON>, rediģējiet savu ziņu vai izveidojiet jaunu sarunu.", "com_error_no_base_url": "Nav atrasts bāzes URL. <PERSON>, norā<PERSON>t to un mēģiniet vēlreiz.", "com_error_no_user_key": "Atslēga nav atrasta. <PERSON><PERSON><PERSON><PERSON>, norādiet atslēgu un mēģiniet vēlreiz.", "com_files_filter": "Filtr<PERSON>t failus...", "com_files_no_results": "Nav rezultātu.", "com_files_number_selected": "{{0}} no {{1}} atlas<PERSON>ti faili", "com_files_table": "kaut kam šeit ir jānotiek. bija tukšs", "com_generated_files": "Ģenerētie faili:", "com_hide_examples": "<PERSON><PERSON><PERSON><PERSON>", "com_info_heic_converting": "HEIC attēla konvertēšana uz JPEG...", "com_nav_2fa": "Divfaktoru autentifikācija (2FA)", "com_nav_account_settings": "Konta iestatījumi", "com_nav_always_make_prod": "Vienmēr uzlieciet jaunas versijas produkcij<PERSON>", "com_nav_archive_created_at": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datums", "com_nav_archive_name": "<PERSON><PERSON><PERSON>", "com_nav_archived_chats": "Arhivē<PERSON> sarunas", "com_nav_at_command": "@-<PERSON><PERSON><PERSON>", "com_nav_at_command_description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> komanda \"@\" gal<PERSON><PERSON><PERSON>, mode<PERSON><PERSON>, s<PERSON><PERSON><PERSON><PERSON><PERSON> iestatījumu u. c. pā<PERSON>.", "com_nav_audio_play_error": "<PERSON><PERSON><PERSON><PERSON>, atskaņojot audio: {{0}}", "com_nav_audio_process_error": "<PERSON><PERSON><PERSON><PERSON>, apstrād<PERSON>jot audio: {{0}}", "com_nav_auto_scroll": "Automātiski iet uz jaun<PERSON>, atverot sarunu", "com_nav_auto_send_prompts": "Automātiski <PERSON>", "com_nav_auto_send_text": "Automātiski no<PERSON>ū<PERSON> te<PERSON>", "com_nav_auto_send_text_disabled": "iestatiet -1, lai at<PERSON><PERSON><PERSON><PERSON>", "com_nav_auto_transcribe_audio": "Automātiski transkribēt audio", "com_nav_automatic_playback": "Automātiski atskaņot jaunā<PERSON> zi<PERSON>", "com_nav_balance": "Bilance", "com_nav_balance_auto_refill_disabled": "Automātiskā bilances papil<PERSON><PERSON><PERSON>na ir atspē<PERSON>ta.", "com_nav_balance_auto_refill_error": "<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> automātiskās bilances papildin<PERSON>šanas iestatījumus.", "com_nav_balance_auto_refill_settings": "Automātiskās bilances papildin<PERSON><PERSON>nas iestatījumi", "com_nav_balance_day": "diena", "com_nav_balance_days": "dienas", "com_nav_balance_every": "Katras", "com_nav_balance_hour": "stunda", "com_nav_balance_hours": "stun<PERSON>", "com_nav_balance_interval": "Intervāls:", "com_nav_balance_last_refill": "Pē<PERSON><PERSON><PERSON><PERSON> bilances papildišana:", "com_nav_balance_minute": "<PERSON><PERSON><PERSON>", "com_nav_balance_minutes": "<PERSON><PERSON><PERSON>", "com_nav_balance_month": "mēnesis", "com_nav_balance_months": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_balance_next_refill": "Nākamā bilances papildin<PERSON>šana:", "com_nav_balance_next_refill_info": "Nākamā bilances papildināšana notiks automātiski tikai tad, ja būs izpildīti abi nosacījumi: kopš pēdējās bilances papildināšanas ir pagājis norādītais laika intervāls un uzaicinājuma nosūtīšana izraisītu jūsu atlikuma samazināšanos zem nulles.", "com_nav_balance_refill_amount": "Bila<PERSON> papil<PERSON><PERSON><PERSON><PERSON> apjoms:", "com_nav_balance_second": "otrais", "com_nav_balance_seconds": "<PERSON><PERSON><PERSON>", "com_nav_balance_week": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_balance_weeks": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_browser": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_center_chat_input": "Cent<PERSON><PERSON><PERSON> sarunas ievadi sveiciena ekr<PERSON>", "com_nav_change_picture": "<PERSON><PERSON><PERSON> at<PERSON>", "com_nav_chat_commands": "<PERSON><PERSON><PERSON> k<PERSON>", "com_nav_chat_commands_info": "<PERSON><PERSON><PERSON> komandas tiek aktivizētas, ieraks<PERSON>t noteiktas rakstzīmes ziņas sākumā. Katru komandu aktivizē tai norādītais prefikss. V<PERSON><PERSON> t<PERSON>, ja bie<PERSON>i i<PERSON>t šīs rakstzīmes ziņojumu sākumā.", "com_nav_chat_direction": "<PERSON><PERSON><PERSON> v<PERSON>", "com_nav_clear_all_chats": "Notīrīt visas sarunas", "com_nav_clear_cache_confirm_message": "Vai tiešām vēlaties notīrīt kešatmiņu?", "com_nav_clear_conversation": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_clear_conversation_confirm_message": "Vai tiešām vēlaties notīrīt visas sarunas? Šī darbība ir neatgriezeniska.", "com_nav_close_sidebar": "<PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>u j<PERSON>", "com_nav_commands": "<PERSON><PERSON><PERSON>", "com_nav_confirm_clear": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_conversation_mode": "<PERSON><PERSON><PERSON>", "com_nav_convo_menu_options": "<PERSON>runas izvēlnes opcijas", "com_nav_db_sensitivity": "<PERSON><PERSON><PERSON> j<PERSON>", "com_nav_delete_account": "<PERSON><PERSON><PERSON><PERSON> kontu", "com_nav_delete_account_button": "Neatgriezeniski d<PERSON>ēst manu kontu", "com_nav_delete_account_confirm": "<PERSON><PERSON><PERSON><PERSON> kontu — vai tiešām?", "com_nav_delete_account_email_placeholder": "<PERSON><PERSON><PERSON><PERSON>, ievadiet sava konta e-pasta adresi", "com_nav_delete_cache_storage": "Dzēst TTS kešatmiņas glabātuvi", "com_nav_delete_data_info": "Visi jūsu dati tiks dz<PERSON>sti.", "com_nav_delete_warning": "BRĪDINĀJUMS: Tas neatgriezeniski izdzēsīs jū<PERSON> kont<PERSON>.", "com_nav_enable_cache_tts": "Iespējot kešatmiņu TTS", "com_nav_enable_cloud_browser_voice": "Izmantojiet cloud-based balsis", "com_nav_enabled": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_engine": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_enter_to_send": "Nospiediet tausti<PERSON>, lai nos<PERSON><PERSON><PERSON><PERSON> ziņ<PERSON>", "com_nav_export": "Eksportēt", "com_nav_export_all_message_branches": "Eksportēt visu ziņu zarus", "com_nav_export_conversation": "Eksportēt sarunu", "com_nav_export_filename": "<PERSON><PERSON><PERSON>", "com_nav_export_filename_placeholder": "Iestatiet faila nosaukumu", "com_nav_export_include_endpoint_options": "<PERSON><PERSON><PERSON><PERSON> opcijas", "com_nav_export_recursive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_export_recursive_or_sequential": "<PERSON><PERSON><PERSON><PERSON><PERSON>s vai secīgs?", "com_nav_export_type": "Tips", "com_nav_external": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_font_size": "<PERSON><PERSON><PERSON><PERSON> fonta lie<PERSON>", "com_nav_font_size_base": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_font_size_lg": "<PERSON><PERSON>", "com_nav_font_size_sm": "Maz<PERSON>", "com_nav_font_size_xl": "<PERSON><PERSON><PERSON><PERSON> liels", "com_nav_font_size_xs": "Īpaši mazs", "com_nav_help_faq": "Palīdzība un bieži uzdotie jautājumi", "com_nav_hide_panel": "<PERSON><PERSON><PERSON><PERSON> lab<PERSON>s malēj<PERSON>s s<PERSON>u <PERSON>i", "com_nav_info_balance": "<PERSON><PERSON><PERSON> parāda, cik daudz tokenu kredītu jums ir atlicis i<PERSON>. Tokenu kredīti tiek pārvērsti naudas vērtībā (piemēram, 1000 kredīti = 0,001 USD).", "com_nav_info_code_artifacts": "Iespējo eksperimentāla koda artefaktu rā<PERSON><PERSON><PERSON><PERSON> blak<PERSON> sa<PERSON>ai", "com_nav_info_code_artifacts_agent": "Iespējo koda artefaktu izman<PERSON>nu šim aģentam. Pēc noklusējuma tiek pievienotas papildu instrukcijas, kas attiecas uz artefaktu i<PERSON>, ja vien nav iespējots \"Pielāgots uzvednes režīms\".", "com_nav_info_custom_prompt_mode": "Ja <PERSON>ī opcija ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, noklusējuma artefaktu sistēmas uzvedne netiks iekļauta. <PERSON><PERSON><PERSON> režīmā visas artefaktu ģenerēšanas instrukcijas ir jāsniedz manuāli.", "com_nav_info_enter_to_send": "Ja šī opcija ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, nosp<PERSON><PERSON><PERSON> taustiņu `ENTER`, jūsu ziņa tiks nosūtīts. Ja šī opcija ir atspējota, nosp<PERSON><PERSON><PERSON> taustiņu <PERSON>, tiks pievienota jauna rinda, un, lai nosūt<PERSON>tu z<PERSON>, bū<PERSON> j<PERSON><PERSON><PERSON> taustiņu kombinācija `CTRL + ENTER` / `⌘ + ENTER`.", "com_nav_info_fork_change_default": "“Tikai redzamās ziņas” ietver tikai tiešo ceļu uz atlasīto ziņu. “Iekļaut saistītos zarus” pievieno zarus gar ceļu. “Iekļaut visus uz/no šejienes” ietver visus saistītās ziņas un zarus.", "com_nav_info_fork_split_target_setting": "Ja <PERSON><PERSON> opcija ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, atzarošana sāksies no mērķa ziņas uz jaunāko sarunas ziņu atbilstoši atlasītajai darbībai.", "com_nav_info_include_shadcnui": "Ja <PERSON><PERSON> opcija ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, tiks iekļ<PERSON>as instrukcijas par shadcn/ui komponentu lietošanu. shadcn/ui ir atkārtoti izmantojamu komponentu kolekcija, ka<PERSON> i<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> Radix UI un Tailwind CSS. Piezīme. Šīs ir garas instruk<PERSON>, tās vajadzētu iespējot tikai tad, ja jums ir svarīgi informēt LLM par pareiziem importēšanas datiem un komponentiem. Lai iegūtu plašāku informāciju par šiem komponentiem, apmeklējiet vietni: https://ui.shadcn.com/", "com_nav_info_latex_parsing": "Ja <PERSON><PERSON> opcija ir <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, LaTeX kods ziņās tiks atveidots kā matemātiski vienādojumi. <PERSON>īs opcijas atspējošana var uzlabot veiktspēju, ja LaTeX atveidošana nav nepieciešama.", "com_nav_info_save_badges_state": "Ja <PERSON>ī opcija ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, sarunu nozīmī<PERSON>u stāvoklis tiks saglabāts. Tas nozīmē, ka, izveidojot jaunu sarunu, noz<PERSON><PERSON><PERSON>tes paliks tādā pašā stāvoklī kā iepriekšējā sarunā. Ja atspējosiet šo opciju, noz<PERSON><PERSON>ītes tiks atiestatītas uz noklusējuma stāvokli katru reizi, kad izveidosiet jaunu sarunu.", "com_nav_info_save_draft": "Ja <PERSON><PERSON> opcija ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, sarun<PERSON> veidlapā ievadītais teksts un pielikumi tiks automātiski saglabāti lokāli kā melnraksti. <PERSON><PERSON> melnraksti būs pieejami pat tad, ja atkārtoti ielādēsiet lapu vai pārslēgsieties uz citu sarunu. Melnraksti tiek saglabāti lokāli jūsu ierīcē un tiek dzēsti, tiklīdz ziņa ir nosūtīts.", "com_nav_info_show_thinking": "Ja <PERSON><PERSON> opcija ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, sa<PERSON><PERSON> pēc noklusējuma tiks atvērtas domāšanas nolaižam<PERSON> i<PERSON>, <PERSON><PERSON><PERSON><PERSON> reālla<PERSON> skatīt mākslīgā intelekta spriešanu. Ja šī opcija ir atspējota, do<PERSON><PERSON><PERSON><PERSON> nolaižamās izvēlnes pēc noklusējuma paliks aizvērtas, lai saskarne būtu tīrāka un vienkāršāka.", "com_nav_info_user_name_display": "Ja šī opcija ir i<PERSON><PERSON><PERSON><PERSON><PERSON>, sūtīt<PERSON><PERSON> lietotājvārds tiks rādīts virs katra jūsu nosūtītās ziņas. Ja šī opcija ir at<PERSON>ējota, virs ziņām redzēsiet tikai vārdu \"Jū<PERSON>\".", "com_nav_lang_arabic": "العربية", "com_nav_lang_armenian": "Հայերեն", "com_nav_lang_auto": "Automātiska <PERSON>", "com_nav_lang_brazilian_portuguese": "Brazīlijas portugāļu", "com_nav_lang_catalan": "Katalā", "com_nav_lang_chinese": "中文", "com_nav_lang_czech": "Čeština", "com_nav_lang_danish": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_lang_dutch": "Nīderlandiešu", "com_nav_lang_english": "<PERSON><PERSON><PERSON>", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "<PERSON><PERSON><PERSON><PERSON> ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "<PERSON><PERSON><PERSON>", "com_nav_lang_hebrew": "עברית", "com_nav_lang_hungarian": "<PERSON><PERSON><PERSON>", "com_nav_lang_indonesia": "Indonēziešu", "com_nav_lang_italian": "<PERSON><PERSON>ļ<PERSON>", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_latvian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_lang_persian": "فار<PERSON>ی", "com_nav_lang_polish": "<PERSON><PERSON><PERSON>", "com_nav_lang_portuguese": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_lang_russian": "K<PERSON><PERSON>", "com_nav_lang_spanish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_lang_swedish": "Zviedru", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_uyghur": "Uyƣur tili", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "Valoda", "com_nav_latex_parsing": "LaTeX parsē<PERSON> (var ietekmēt veiktspēju)", "com_nav_log_out": "Izrakstīties", "com_nav_long_audio_warning": "Garā<PERSON> tekstu apstrāde prasīs ilgāku laiku.", "com_nav_maximize_chat_space": "<PERSON><PERSON><PERSON><PERSON><PERSON> sarunas telpu", "com_nav_mcp_configure_server": "Kon<PERSON><PERSON><PERSON><PERSON>t {{0}}", "com_nav_mcp_status_connecting": "{{0}} - Savienojas", "com_nav_mcp_vars_update_error": "<PERSON><PERSON><PERSON><PERSON>, atjauninot MCP pielāgotos lietotāja parametrus: {{0}}", "com_nav_mcp_vars_updated": "MCP pielāgotie lietotāja mainīgie ir veiksmīgi at<PERSON>.", "com_nav_modular_chat": "Iespējot galapunk<PERSON> pā<PERSON><PERSON><PERSON><PERSON><PERSON> sarunas laik<PERSON>", "com_nav_my_files": "<PERSON><PERSON> faili", "com_nav_not_supported": "Nav <PERSON>", "com_nav_open_sidebar": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>u j<PERSON>", "com_nav_playback_rate": "Audio atskaņošanas ātrums", "com_nav_plugin_auth_error": "Mēģinot autentificēt š<PERSON>, r<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vē<PERSON><PERSON><PERSON>.", "com_nav_plugin_install": "Instalēt", "com_nav_plugin_search": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> s<PERSON>", "com_nav_plugin_store": "<PERSON><PERSON><PERSON><PERSON><PERSON> veika<PERSON>", "com_nav_plugin_uninstall": "Atinstalēt", "com_nav_plus_command": "+-komanda", "com_nav_plus_command_description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> komandu \"+\", lai pievienotu vairāku at<PERSON>u iestatījumu", "com_nav_profile_picture": "<PERSON><PERSON>", "com_nav_save_badges_state": "Saglabāt no<PERSON> stāvo<PERSON>", "com_nav_save_drafts": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_scroll_button": "Ritiniet līdz beigu pogai", "com_nav_search_placeholder": "Mek<PERSON><PERSON><PERSON> zi<PERSON>", "com_nav_send_message": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_setting_account": "<PERSON><PERSON>", "com_nav_setting_balance": "Bilance", "com_nav_setting_chat": "<PERSON><PERSON><PERSON>", "com_nav_setting_data": "<PERSON><PERSON> k<PERSON>", "com_nav_setting_general": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_setting_mcp": "MCP iestatījumi", "com_nav_setting_personalization": "Personalizācija", "com_nav_setting_speech": "<PERSON><PERSON>", "com_nav_settings": "Iestatījumi", "com_nav_shared_links": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_nav_show_code": "<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON> kod<PERSON>, i<PERSON><PERSON><PERSON><PERSON> koda <PERSON>", "com_nav_show_thinking": "Pēc noklusējuma atvērt do<PERSON>ā<PERSON>", "com_nav_slash_command": "/-<PERSON><PERSON><PERSON>", "com_nav_slash_command_description": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> komandu \"/\", lai atlas<PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> ta<PERSON>ru", "com_nav_speech_to_text": "<PERSON><PERSON> p<PERSON><PERSON><PERSON> te<PERSON>t<PERSON>", "com_nav_stop_generating": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ģenerēšanu", "com_nav_text_to_speech": "<PERSON><PERSON><PERSON> runā", "com_nav_theme": "<PERSON><PERSON><PERSON>", "com_nav_theme_dark": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_theme_light": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_theme_system": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_tool_dialog": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_tool_dialog_agents": "Aģenta rīki", "com_nav_tool_dialog_description": "<PERSON> saglab<PERSON>tu rīku atlasi, ir jā<PERSON>g<PERSON><PERSON><PERSON> asistents.", "com_nav_tool_remove": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_tool_search": "Me<PERSON><PERSON><PERSON><PERSON><PERSON> r<PERSON>", "com_nav_user": "LIETOTĀJS", "com_nav_user_msg_markdown": "Atveidot lietotāja ziņas k<PERSON> Markdown", "com_nav_user_name_display": "<PERSON><PERSON><PERSON><PERSON><PERSON> lietotāj<PERSON><PERSON><PERSON><PERSON> zi<PERSON>", "com_nav_voice_select": "<PERSON><PERSON><PERSON>", "com_show_agent_settings": "<PERSON>ā<PERSON><PERSON><PERSON> aģenta iestatījumus", "com_show_completion_settings": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>", "com_show_examples": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_sidepanel_agent_builder": "Aģentu veidotājs", "com_sidepanel_assistant_builder": "Asistenta izveidotājs", "com_sidepanel_attach_files": "<PERSON><PERSON><PERSON> failus", "com_sidepanel_conversation_tags": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_sidepanel_hide_panel": "Slēpt paneli", "com_sidepanel_manage_files": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> failus", "com_sidepanel_mcp_no_servers_with_vars": "Nav MCP serveru ar konfigurējamiem <PERSON>ga<PERSON>em.", "com_sidepanel_mcp_variables_for": "MCP parametri {{0}}", "com_sidepanel_parameters": "Parametri", "com_sources_image_alt": "Mek<PERSON>ē<PERSON>nas rezultāta attēls", "com_sources_more_sources": "+{{count}} avoti", "com_sources_tab_all": "Visi", "com_sources_tab_images": "<PERSON><PERSON><PERSON><PERSON>", "com_sources_tab_news": "<PERSON><PERSON><PERSON><PERSON>", "com_sources_title": "<PERSON><PERSON><PERSON>", "com_ui_2fa_account_security": "Divfakt<PERSON><PERSON> autentifikācija piešķir jūsu kontam papildu droš<PERSON><PERSON> slāni", "com_ui_2fa_disable": "Atspējot 2FA", "com_ui_2fa_disable_error": "Atspējojot divfaktoru <PERSON>, r<PERSON><PERSON><PERSON>.", "com_ui_2fa_disabled": "2FA ir atspējots", "com_ui_2fa_enable": "Iespējot 2FA", "com_ui_2fa_enabled": "2FA ir iespējots", "com_ui_2fa_generate_error": "Ģenerējot divfaktoru autentifik<PERSON>ī<PERSON>, r<PERSON><PERSON><PERSON>.", "com_ui_2fa_invalid": "Nederīgs divfaktoru autentifikācijas kods", "com_ui_2fa_setup": "Iestatīt 2FA", "com_ui_2fa_verified": "Divfaktoru autentifikācija veiksmīgi verificēta", "com_ui_accept": "<PERSON><PERSON>", "com_ui_action_button": "Darbības poga", "com_ui_active": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_add": "<PERSON><PERSON><PERSON>", "com_ui_add_mcp": "<PERSON><PERSON><PERSON> MCP", "com_ui_add_mcp_server": "Pievienot MCP serveri", "com_ui_add_model_preset": "Pievienot modeli vai iepriekš iestatītu iestatījumu papildu atbildei", "com_ui_add_multi_conversation": "<PERSON><PERSON><PERSON> v<PERSON> sarunas", "com_ui_adding_details": "Detalizētas informācijas pievienošana", "com_ui_admin": "Administrators", "com_ui_admin_access_warning": "Administratora piekļuves atspējošana šai funkcijai var izraisīt neparedzētas lietotāja saskarnes problēmas, kurām nepiecieša<PERSON> atsvaidzināšana. Ja izmaiņas ir saglab<PERSON>, vien<PERSON><PERSON><PERSON> veids, k<PERSON> t<PERSON><PERSON> at<PERSON>, ir, i<PERSON><PERSON>jot saskarnes iestatījumu librechat.yaml konfigurācijā, kas ietekmē visas lomas.", "com_ui_admin_settings": "<PERSON><PERSON>", "com_ui_advanced": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_advanced_settings": "Advancē<PERSON>īju<PERSON>", "com_ui_agent": "Aģents", "com_ui_agent_chain": "Aģentu ķēde (aģentu maisījums)", "com_ui_agent_chain_info": "Ļauj izveidot aģentu secību ķēdes. Katrs aģents var piekļūt iepriekšējo ķēdē esošo aģentu izvades datiem. Balstīts uz \"Aģentu sajaukuma\" ar<PERSON><PERSON><PERSON><PERSON><PERSON>, kurā aģenti izmanto iepriekš<PERSON> izvades datus kā palīginformāciju.", "com_ui_agent_chain_max": "<PERSON><PERSON><PERSON> esat sasniedzis maks<PERSON> s<PERSON> {{0}} aģentu.", "com_ui_agent_delete_error": "<PERSON><PERSON><PERSON><PERSON><PERSON> aģentu, rad<PERSON><PERSON> k<PERSON>.", "com_ui_agent_deleted": "Aģents veiksmīgi iz<PERSON>z<PERSON>", "com_ui_agent_duplicate_error": "<PERSON><PERSON><PERSON><PERSON><PERSON> aģentu, rad<PERSON><PERSON>.", "com_ui_agent_duplicated": "Aģents veik<PERSON><PERSON>gi dub<PERSON>", "com_ui_agent_editing_allowed": "Citi lietotāji jau var rediģēt šo aģentu", "com_ui_agent_recursion_limit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aģenta soļu skaits", "com_ui_agent_recursion_limit_info": "<PERSON><PERSON><PERSON><PERSON><PERSON>, cik soļus aģents var veikt vienā izpildes reizē, pirms sniedz galīgo atbildi. Noklusējuma vērtība ir 25 soļi. Solis ir vai nu AI API pieprasījums, vai rīka lietošanas kārta. <PERSON><PERSON><PERSON><PERSON>, pamata rīka mi<PERSON>edarbība ietver 3 soļus: s<PERSON><PERSON><PERSON><PERSON><PERSON> pieprasījumu, rīka lietošanu un turpmāko pieprasījumu.", "com_ui_agent_shared_to_all": "kaut kam šeit ir jānotiek. bija tukšs", "com_ui_agent_var": "{{0}} aģents", "com_ui_agent_version": "<PERSON><PERSON><PERSON>", "com_ui_agent_version_active": "Aktīvā versija", "com_ui_agent_version_duplicate": "Atrasta dublikāta versija. <PERSON><PERSON> darb<PERSON>ba izve<PERSON> versiju, kas ir identiska citai, jau eso<PERSON>i versijai. {{versionIndex}}.", "com_ui_agent_version_empty": "Nav pieejamu versiju", "com_ui_agent_version_error": "<PERSON><PERSON><PERSON><PERSON>, ielā<PERSON><PERSON><PERSON><PERSON> versijas", "com_ui_agent_version_history": "Versiju vēsture", "com_ui_agent_version_no_agent": "Nav atlasīts neviens aģents. <PERSON><PERSON><PERSON><PERSON>, atlasiet aģentu, lai skatītu versiju vēsturi.", "com_ui_agent_version_no_date": "Datums nav pieejams", "com_ui_agent_version_restore": "At<PERSON>uno<PERSON>", "com_ui_agent_version_restore_confirm": "Vai tiešām vēlaties atjaunot šo versiju?", "com_ui_agent_version_restore_error": "Neizdevā<PERSON> atjaunot versiju", "com_ui_agent_version_restore_success": "<PERSON><PERSON><PERSON>", "com_ui_agent_version_title": "<PERSON><PERSON><PERSON> {{versionNumber}}", "com_ui_agent_version_unknown_date": "Nezināms datums", "com_ui_agents": "Aģenti", "com_ui_agents_allow_create": "Atļaut aģentu izveidi", "com_ui_agents_allow_share_global": "Atļaut koplie<PERSON>šanas aģentus visiem lietotājiem", "com_ui_agents_allow_use": "<PERSON><PERSON><PERSON> aģentu <PERSON><PERSON>", "com_ui_all": "visi", "com_ui_all_proper": "Visi", "com_ui_analyzing": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_analyzing_finished": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_api_key": "API atslēga", "com_ui_archive": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_archive_delete_error": "Neizdevās izdz<PERSON>st arhivēto sa<PERSON>.", "com_ui_archive_error": "Neizdev<PERSON><PERSON> a<PERSON>hi<PERSON>.", "com_ui_artifact_click": "Noklikšķiniet, lai atvērtu", "com_ui_artifacts": "Artefakti", "com_ui_artifacts_options": "Artefaktu opcijas", "com_ui_artifacts_toggle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> artefaktu lietotāja sa<PERSON>ni", "com_ui_artifacts_toggle_agent": "<PERSON>espē<PERSON><PERSON> artefaktus", "com_ui_ascending": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_assistant": "Asistents", "com_ui_assistant_delete_error": "<PERSON><PERSON><PERSON><PERSON><PERSON> as<PERSON>, r<PERSON><PERSON><PERSON>.", "com_ui_assistant_deleted": "Asistents ir veiksmīgi izdzēsts.", "com_ui_assistants": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_assistants_output": "Asistentu izvade", "com_ui_attach_error": "Nevar pievienot failu. Izveidojiet vai atlasiet sarunu vai mēģiniet atsvaidzināt lapu.", "com_ui_attach_error_openai": "<PERSON><PERSON>r pievienot asistenta failus citiem galapunktiem", "com_ui_attach_error_size": "Galapunkta faila lieluma ierobežojums ir pārsniegts:", "com_ui_attach_error_type": "Neatbalstīts faila tips galapunktam:", "com_ui_attach_remove": "Noņemt failu", "com_ui_attach_warn_endpoint": "<PERSON><PERSON><PERSON>, ka<PERSON> as<PERSON>, tiek ignor<PERSON>ti bez saderīga rīka.", "com_ui_attachment": "Pielikums", "com_ui_auth_type": "Autorizācijas veids", "com_ui_auth_url": "Autorizācijas URL", "com_ui_authenticate": "Autentificēt", "com_ui_authentication": "Autentifikācija", "com_ui_authentication_type": "Autentifikācijas veids", "com_ui_auto": "Auto", "com_ui_available_tools": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_avatar": "Avatars", "com_ui_azure": "Azure", "com_ui_back": "Atpakaļ", "com_ui_back_to_chat": "Atpakaļ uz sarunu", "com_ui_back_to_prompts": "Atpakaļ pie uzvednēm", "com_ui_backup_codes": "<PERSON><PERSON><PERSON> kodi", "com_ui_backup_codes_regenerate_error": "Atkārtoti ģenerējot rezerves kodus, rad<PERSON><PERSON> k<PERSON>.", "com_ui_backup_codes_regenerated": "Rezerves kodi ir veiksmīgi at<PERSON>", "com_ui_basic": "Pamata", "com_ui_basic_auth_header": "Pamata autorizācijas galvene", "com_ui_bearer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmark_delete_confirm": "Vai tiešām vēlaties dzēst šo grāmat<PERSON>?", "com_ui_bookmarks": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmarks_add": "<PERSON><PERSON><PERSON>", "com_ui_bookmarks_add_to_conversation": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmarks_count": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmarks_create_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>", "com_ui_bookmarks_create_exists": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON>āv", "com_ui_bookmarks_create_success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>gi izveido<PERSON>", "com_ui_bookmarks_delete": "Dzē<PERSON> gr<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmarks_delete_error": "<PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>.", "com_ui_bookmarks_delete_success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>sm<PERSON>gi izdz<PERSON>", "com_ui_bookmarks_description": "<PERSON><PERSON><PERSON>", "com_ui_bookmarks_edit": "Rediģēt gr<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_bookmarks_filter": "<PERSON><PERSON><PERSON><PERSON><PERSON> gr<PERSON>...", "com_ui_bookmarks_new": "<PERSON><PERSON><PERSON>", "com_ui_bookmarks_title": "Nosa<PERSON>ms", "com_ui_bookmarks_update_error": "<PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>.", "com_ui_bookmarks_update_success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ve<PERSON>", "com_ui_bulk_delete_error": "Neizdevās izdz<PERSON> koplietotās sa<PERSON>", "com_ui_callback_url": "Atzvanīšanas URL", "com_ui_cancel": "Atcelt", "com_ui_cancelled": "Atcelts", "com_ui_category": "Kategorija", "com_ui_chat": "<PERSON><PERSON><PERSON>", "com_ui_chat_history": "Sarunu vēsture", "com_ui_clear": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_clear_all": "<PERSON><PERSON><PERSON><PERSON><PERSON> visu", "com_ui_client_id": "Klienta ID", "com_ui_client_secret": "Klienta no<PERSON>lēpums", "com_ui_close": "Aizvērt", "com_ui_close_menu": "Aizvērt izvēlni", "com_ui_close_window": "<PERSON><PERSON><PERSON><PERSON><PERSON> logu", "com_ui_code": "Kods", "com_ui_collapse_chat": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON>u", "com_ui_command_placeholder": "Pēc izvēles: ievadiet komandu uzvednei vai tiks izmantots nosaukums", "com_ui_command_usage_placeholder": "Atlasiet uzvedni pēc komandas vai nosaukuma", "com_ui_complete_setup": "<PERSON><PERSON><PERSON>", "com_ui_concise": "Īss", "com_ui_configure_mcp_variables_for": "<PERSON>z<PERSON><PERSON><PERSON><PERSON>t parametrus {{0}}", "com_ui_confirm_action": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_confirm_admin_use_change": "<PERSON>ot <PERSON><PERSON>, <PERSON><PERSON><PERSON>, to<PERSON><PERSON> jums, tiks liegta <PERSON>. Vai tiešām vēlaties turpināt?", "com_ui_confirm_change": "Apstiprin<PERSON><PERSON> izma<PERSON>ņ<PERSON>", "com_ui_connecting": "Savienojas", "com_ui_context": "Konteksts", "com_ui_continue": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_continue_oauth": "<PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON>", "com_ui_controls": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_convo_delete_error": "Neizdevās iz<PERSON> sarunu", "com_ui_copied": "Nokopēts!", "com_ui_copied_to_clipboard": "Kopēts starpliktuvē", "com_ui_copy_code": "<PERSON><PERSON><PERSON><PERSON> kodu", "com_ui_copy_link": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_copy_to_clipboard": "Kopēt starpliktuvē", "com_ui_create": "Izveidot", "com_ui_create_link": "Izveidot saiti", "com_ui_create_memory": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_create_prompt": "Izveidot uzvedni", "com_ui_creating_image": "Attēla izveide. Var a<PERSON> br<PERSON>.", "com_ui_current": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_currently_production": "<PERSON><PERSON><PERSON><PERSON> produk<PERSON>", "com_ui_custom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_custom_header_name": "<PERSON><PERSON><PERSON><PERSON><PERSON> galvenes nosaukums", "com_ui_custom_prompt_mode": "Pielāgots uzvednes režīms", "com_ui_dashboard": "Informāci<PERSON>is", "com_ui_date": "Datums", "com_ui_date_april": "<PERSON><PERSON><PERSON>", "com_ui_date_august": "Augusts", "com_ui_date_december": "Decembris", "com_ui_date_february": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_january": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_july": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_date_june": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_date_march": "Marts", "com_ui_date_may": "<PERSON><PERSON><PERSON>", "com_ui_date_november": "Novembris", "com_ui_date_october": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_date_previous_30_days": "Iepriekšējās 30 dienas", "com_ui_date_previous_7_days": "Iepriekšējās 7 dienas", "com_ui_date_september": "Septembris", "com_ui_date_today": "Šodien", "com_ui_date_yesterday": "<PERSON><PERSON><PERSON>", "com_ui_decline": "<PERSON><PERSON>", "com_ui_default_post_request": "Noklusējums (POST pieprasījums)", "com_ui_delete": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_delete_action": "<PERSON><PERSON><PERSON><PERSON> darb<PERSON>", "com_ui_delete_action_confirm": "Vai tiešām vēlaties dzēst šo darbību?", "com_ui_delete_agent_confirm": "Vai tiešām vēlaties dzēst šo aģentu?", "com_ui_delete_assistant_confirm": "Vai tiešām vēlaties dzēst šo asistentu? Šo darbību nevar atsaukt.", "com_ui_delete_confirm": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "com_ui_delete_confirm_prompt_version_var": "<PERSON><PERSON> da<PERSON> izdzēs<PERSON>s atlas<PERSON>to versiju \"{{0}}\" Ja citu versiju nebūs <PERSON>, uz<PERSON>ne tiks d<PERSON>.", "com_ui_delete_conversation": "<PERSON><PERSON>ē<PERSON> sarunu?", "com_ui_delete_mcp": "Dzēst MCP", "com_ui_delete_mcp_confirm": "Vai tiešām vēlaties dzēst šo MCP serveri?", "com_ui_delete_mcp_error": "Neizdevās i<PERSON>dzēst MCP serveri.", "com_ui_delete_mcp_success": "MCP serveris veiksmīgi izdz<PERSON>sts", "com_ui_delete_memory": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_delete_not_allowed": "<PERSON><PERSON><PERSON><PERSON>nas darbība nav atļauta", "com_ui_delete_prompt": "Vai d<PERSON>ē<PERSON> uzvedni?", "com_ui_delete_shared_link": "Vai d<PERSON>st kop<PERSON>to sa<PERSON>?", "com_ui_delete_success": "Veiksmīgi d<PERSON>", "com_ui_delete_tool": "D<PERSON>ē<PERSON> rīku", "com_ui_delete_tool_confirm": "Vai tiešām vēlaties dzēst šo rīku?", "com_ui_deleted": "Dzēsts", "com_ui_deleting_file": "Tiek d<PERSON> fails...", "com_ui_descending": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_description": "<PERSON><PERSON><PERSON>", "com_ui_description_placeholder": "Pēc izvēles: ieva<PERSON><PERSON>, kas jā<PERSON><PERSON> u<PERSON>", "com_ui_deselect_all": "Noņemt atlasi visam", "com_ui_detailed": "Detalizēta", "com_ui_disabling": "Atspējošana...", "com_ui_download": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_download_artifact": "Lejupielādēt artefaktu", "com_ui_download_backup": "Lejupielād<PERSON><PERSON> rezerves kodus", "com_ui_download_backup_tooltip": "<PERSON><PERSON> t<PERSON>, le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> rezerves kodus. <PERSON><PERSON> b<PERSON><PERSON>, lai at<PERSON><PERSON><PERSON>, ja paz<PERSON><PERSON><PERSON>t autentifikat<PERSON>.", "com_ui_download_error": "<PERSON><PERSON><PERSON><PERSON>, le<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> failu. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, fails ir izdzē<PERSON>.", "com_ui_drag_drop": "kaut kam šeit ir jānotiek. bija tukšs", "com_ui_dropdown_variables": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> izvēlnes mainīgie:", "com_ui_dropdown_variables_info": "Izveidojiet pielāgotas nolaižamās izvēlnes savām uzvednēm:{{variable_name:option1|option2|option3}}`", "com_ui_duplicate": "Dub<PERSON>āts", "com_ui_duplication_error": "<PERSON><PERSON><PERSON> la<PERSON> rad<PERSON>.", "com_ui_duplication_processing": "<PERSON><PERSON><PERSON>...", "com_ui_duplication_success": "<PERSON><PERSON><PERSON>", "com_ui_edit": "Rediģēt", "com_ui_edit_editing_image": "Attēla rediģēšana", "com_ui_edit_mcp_server": "Rediģēt MCP serveri", "com_ui_edit_memory": "Rediģēt atmiņu", "com_ui_empty_category": "-", "com_ui_endpoint": "Galapunkts", "com_ui_endpoint_menu": "LLM galapunkta izvēlne", "com_ui_enter": "<PERSON><PERSON><PERSON>", "com_ui_enter_api_key": "Ievadiet API atslēgu", "com_ui_enter_key": "<PERSON><PERSON>", "com_ui_enter_openapi_schema": "Ievadiet savu OpenAPI shēmu šeit", "com_ui_enter_value": "Ievadiet vērtību", "com_ui_error": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_error_connection": "<PERSON><PERSON><PERSON><PERSON>, iz<PERSON><PERSON><PERSON><PERSON> a<PERSON>, mēģiniet atsvaid<PERSON>t lapu.", "com_ui_error_save_admin_settings": "Saglabājot <PERSON>a <PERSON>, r<PERSON><PERSON><PERSON>.", "com_ui_error_updating_preferences": "<PERSON><PERSON><PERSON><PERSON>, at<PERSON><PERSON>not preferences", "com_ui_examples": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_expand_chat": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_export_convo_modal": "Eksportēt sarunas modālo logu", "com_ui_feedback_more": "Vairāk...", "com_ui_feedback_more_information": "<PERSON><PERSON><PERSON> papil<PERSON> at<PERSON>", "com_ui_feedback_negative": "Nepieciešami uzlabojumi", "com_ui_feedback_placeholder": "<PERSON><PERSON><PERSON><PERSON>, snied<PERSON>t šeit jebkādas papildu atsauksmes.", "com_ui_feedback_positive": "<PERSON> tas patīk", "com_ui_feedback_tag_accurate_reliable": "Precīzs un uzticams", "com_ui_feedback_tag_attention_to_detail": "Uzmanība <PERSON>", "com_ui_feedback_tag_bad_style": "Slikts stils vai tonis", "com_ui_feedback_tag_clear_well_written": "Skaidri un labi uzrakstīts", "com_ui_feedback_tag_creative_solution": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_feedback_tag_inaccurate": "Neprecīza vai nepareiza atbilde", "com_ui_feedback_tag_missing_image": "<PERSON><PERSON> g<PERSON> att<PERSON>", "com_ui_feedback_tag_not_helpful": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_feedback_tag_not_matched": "Neatbilda manam piepra<PERSON>", "com_ui_feedback_tag_one": " ", "com_ui_feedback_tag_other": "<PERSON><PERSON> problēma", "com_ui_feedback_tag_unjustified_refusal": "<PERSON><PERSON><PERSON><PERSON><PERSON> bez i<PERSON>", "com_ui_feedback_tag_zero": "<PERSON><PERSON> problēma", "com_ui_field_required": "<PERSON><PERSON> lauks ir oblig<PERSON>", "com_ui_file_size": "<PERSON><PERSON><PERSON> lie<PERSON>", "com_ui_files": "<PERSON><PERSON><PERSON>", "com_ui_filter_prompts": "Filtrēt uzved<PERSON>", "com_ui_filter_prompts_name": "Filtrēt uzvednes pēc no<PERSON>ukuma", "com_ui_final_touch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_finance": "<PERSON><PERSON><PERSON>", "com_ui_fork": "Atzarojums", "com_ui_fork_all_target": "<PERSON><PERSON><PERSON><PERSON> visus uz/no šejienes", "com_ui_fork_branches": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_fork_change_default": "Noklusējuma atzarojuma opcija", "com_ui_fork_default": "Izmantot noklusējuma atzarojuma opciju", "com_ui_fork_error": "<PERSON><PERSON><PERSON> laik<PERSON> radā<PERSON>.", "com_ui_fork_error_rate_limit": "<PERSON><PERSON><PERSON><PERSON><PERSON> daudz atzaru pieprasījumu. <PERSON><PERSON>, mēģiniet vēlreiz vēlāk", "com_ui_fork_from_message": "Izvēlieties atzarojuma opciju", "com_ui_fork_info_1": "<PERSON><PERSON><PERSON><PERSON><PERSON>, lai at<PERSON>tu ziņu ar vēlamo darb<PERSON>.", "com_ui_fork_info_2": "\"Sadalīšana\" attiecas uz jaunas sarunas izveidi, kas s<PERSON>/beidzas ar konkrētām ziņām pašreizējā sarun<PERSON>, izveidojot kopiju atbilstoši atlasītajām opcijām.", "com_ui_fork_info_3": "\"Mērķa ziņa\" attiecas vai nu uz ziņu, no kura šis uz<PERSON> logs tika atvērts, vai, ja atzīmējat \"{{0}}\", jaun<PERSON><PERSON><PERSON> ziņa sarunā.", "com_ui_fork_info_branches": "<PERSON>ī opcija sadala redzamās ziņas kopā ar saistītaj<PERSON>m zariem; citiem vārdiem sakot, tie<PERSON>o ceļu uz mērķa ziņu, i<PERSON><PERSON><PERSON><PERSON> atzarus gar ceļu.", "com_ui_fork_info_button_label": "S<PERSON>īt informāciju par sarunu <PERSON>", "com_ui_fork_info_remember": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lai atcerētos izvēlētās opcijas turpmākai lietošanai, tā<PERSON><PERSON><PERSON><PERSON><PERSON> ātrāk sadalot sarunas atbilstoši vēlamajām iespējām.", "com_ui_fork_info_start": "<PERSON>a at<PERSON><PERSON><PERSON><PERSON><PERSON> opcija, atzarošana sāksies no šīs ziņas uz jaunāko ziņu sarunā atbilstoši iepriekš atlasītajai darbībai.", "com_ui_fork_info_target": "<PERSON><PERSON> opcija atzaro visas zi<PERSON><PERSON>, kas ved uz mērķa ziņu, i<PERSON><PERSON><PERSON><PERSON> tā kaimiņus; citiem vārdiem sakot, tiek iekļautas visas ziņu atzarošanas neatkarīgi no tā, vai tās ir redzamas vai atrodas vienā un tajā pašā ceļā.", "com_ui_fork_info_visible": "<PERSON><PERSON> opcija atzaro tikai redzam<PERSON> ziņ<PERSON>; citiem vārdiem sakot, tie<PERSON>o ceļu uz mērķa ziņām bez atzariem.", "com_ui_fork_more_details_about": "Skatiet papildu informāciju un detaļas par \"{{0}}\" atzarojuma variantu", "com_ui_fork_more_info_options": "Skatiet detalizētu visu atzarojuma opciju un to da<PERSON><PERSON><PERSON> skaidrojumu", "com_ui_fork_processing": "<PERSON><PERSON><PERSON>...", "com_ui_fork_remember": "Atcerēties", "com_ui_fork_remember_checked": "<PERSON><PERSON><PERSON> izvēle tiks atcerēta pēc lietošanas. To var jebkurā laikā mainīt iestatījumos.", "com_ui_fork_split_target": "Sāciet at<PERSON><PERSON>", "com_ui_fork_split_target_setting": "Pēc noklusējuma sākt atzarojumu no mērķa ziņas", "com_ui_fork_success": "<PERSON><PERSON><PERSON> sa<PERSON>", "com_ui_fork_visible": "<PERSON><PERSON><PERSON>", "com_ui_generate_backup": "Ģenerēt rezerves kodus", "com_ui_generate_qrcode": "Ģenerēt QR kodu", "com_ui_generating": "Notiek ģenerēšana...", "com_ui_generation_settings": "Ģenerēšanas iestatījumi", "com_ui_getting_started": "<PERSON><PERSON>", "com_ui_global_group": "kaut kam šeit ir jānotiek. bija tukšs", "com_ui_go_back": "Atgriezties", "com_ui_go_to_conversation": "Doties uz sarunu", "com_ui_good_afternoon": "Labdien", "com_ui_good_evening": "<PERSON><PERSON><PERSON>", "com_ui_good_morning": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_happy_birthday": "Man <PERSON>odien ir pirmā dzi<PERSON> diena!", "com_ui_hide_image_details": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "com_ui_hide_password": "<PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "com_ui_hide_qr": "Slēpt QR kodu", "com_ui_high": "Augsts", "com_ui_host": "<PERSON><PERSON>", "com_ui_icon": "<PERSON><PERSON><PERSON>", "com_ui_idea": "<PERSON><PERSON><PERSON>", "com_ui_image_created": "Attēls izveidots", "com_ui_image_details": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_image_edited": "Attēls rediģēts", "com_ui_image_gen": "Attēlu ģenerēšana", "com_ui_import": "Importēt", "com_ui_import_conversation_error": "Import<PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>.", "com_ui_import_conversation_file_type_error": "Neatbalstīts importēšanas veids", "com_ui_import_conversation_info": "<PERSON><PERSON>u importē<PERSON>na no JSON faila", "com_ui_import_conversation_success": "<PERSON><PERSON><PERSON> ir veik<PERSON>īgi import<PERSON>tas", "com_ui_include_shadcnui": "Iekļaujiet shadcn/ui komponentu instrukcijas", "com_ui_input": "<PERSON><PERSON><PERSON>", "com_ui_instructions": "Instrukcijas", "com_ui_key": "Atslēga", "com_ui_late_night": "Priecīgu vēlu nakti", "com_ui_latest_footer": "Katrs m<PERSON>kslīgais intelekts ikvienam.", "com_ui_latest_production_version": "Jaunākā produkcijas versija", "com_ui_latest_version": "Jaunākā versija", "com_ui_librechat_code_api_key": "Iegūstiet savu LibreChat koda interpretatora API atslēgu", "com_ui_librechat_code_api_subtitle": "Droši. Daudzvalodu. Ievades/izvades faili.", "com_ui_librechat_code_api_title": "Palaist mākslīgā intelekta kodu", "com_ui_loading": "Notiek iel<PERSON>de...", "com_ui_locked": "Bloķēts", "com_ui_logo": "{{0}} Logotips", "com_ui_low": "Zems", "com_ui_manage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_max_tags": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ļaut<PERSON> skaits ir {{0}}, <PERSON><PERSON><PERSON><PERSON><PERSON> j<PERSON><PERSON><PERSON><PERSON><PERSON> v<PERSON>.", "com_ui_mcp_authenticated_success": "MCP serveris '{{0}}' veik<PERSON><PERSON><PERSON> autentificēts", "com_ui_mcp_dialog_desc": "<PERSON><PERSON><PERSON><PERSON>, ievadiet nepieciešamo informāciju zem<PERSON>k.", "com_ui_mcp_enter_var": "Ievadiet vērtību {{0}}", "com_ui_mcp_init_failed": "Neizdevās inicializēt MCP serveri", "com_ui_mcp_initialize": "Inicializēt", "com_ui_mcp_initialized_success": "MCP serveris '{{0}}' ve<PERSON><PERSON><PERSON><PERSON> iniciali<PERSON>", "com_ui_mcp_not_authenticated": "{{0}} nav autentificēts (nepieciešams OAuth).", "com_ui_mcp_not_initialized": "{{0}} nav inicializēts", "com_ui_mcp_oauth_cancelled": "<PERSON><PERSON><PERSON> at<PERSON> {{0}}", "com_ui_mcp_oauth_timeout": "<PERSON><PERSON><PERSON> {{0}}", "com_ui_mcp_server_not_found": "Serveris nav atrasts.", "com_ui_mcp_servers": "MCP serveri", "com_ui_mcp_update_var": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{0}}", "com_ui_mcp_url": "MCP servera URL", "com_ui_medium": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_memories": "<PERSON><PERSON>ņ<PERSON>", "com_ui_memories_allow_create": "<PERSON><PERSON><PERSON>", "com_ui_memories_allow_opt_out": "Atļaut lietotājiem atteikties no atmiņu funkcijas", "com_ui_memories_allow_read": "<PERSON><PERSON><PERSON>", "com_ui_memories_allow_update": "<PERSON><PERSON><PERSON>", "com_ui_memories_allow_use": "<PERSON><PERSON><PERSON>", "com_ui_memories_filter": "<PERSON><PERSON><PERSON><PERSON><PERSON>...", "com_ui_memory": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_memory_already_exceeded": "Atmiņas krātuve jau ir pilna - tokeni pārsniegti par {{tokens}}. Izdzēsiet esoš<PERSON><PERSON> at<PERSON>, pirms pievienojat jaunas.", "com_ui_memory_created": "Atmiņa veiksmīgi izveidota", "com_ui_memory_deleted": "Atmiņa izdzēsta", "com_ui_memory_deleted_items": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_memory_error": "Atmiņas <PERSON>", "com_ui_memory_key_exists": "Atmiņ<PERSON> ar <PERSON>o atslēgu jau pastāv. <PERSON><PERSON>, izman<PERSON>jiet citu atslēgu.", "com_ui_memory_key_validation": "Atmiņas atslēgā drī<PERSON>t būt tikai mazie burti un pasvītrojumi.", "com_ui_memory_storage_full": "Atmiņas krātuve ir pilna", "com_ui_memory_updated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> saglabātā at<PERSON>a", "com_ui_memory_updated_items": "Atjauninātas atmiņas", "com_ui_memory_would_exceed": "<PERSON><PERSON>r sagla<PERSON>t - pārsniegtu tokenu limitu par {{tokens}}. Izdzēsiet eso<PERSON><PERSON><PERSON> at<PERSON>, lai atbrīvotu vietu.", "com_ui_mention": "<PERSON><PERSON><PERSON>, assist<PERSON><PERSON> vai s<PERSON>, lai ā<PERSON> uz to pārslēgtos", "com_ui_min_tags": "<PERSON><PERSON><PERSON> v<PERSON><PERSON><PERSON> v<PERSON>, vismaz {{0}} ir <PERSON><PERSON><PERSON>.", "com_ui_misc": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_model": "Modelis", "com_ui_model_parameters": "<PERSON><PERSON><PERSON> paramet<PERSON>", "com_ui_more_info": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_my_prompts": "<PERSON><PERSON>", "com_ui_name": "<PERSON><PERSON><PERSON>", "com_ui_new": "<PERSON><PERSON><PERSON>", "com_ui_new_chat": "<PERSON><PERSON><PERSON> sa<PERSON>a", "com_ui_new_conversation_title": "<PERSON><PERSON><PERSON> sarunas no<PERSON>", "com_ui_next": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_no": "Nē", "com_ui_no_backup_codes": "Nav pieejamu rezerves kodu. <PERSON><PERSON><PERSON><PERSON>, ģenerējiet jaunus.", "com_ui_no_bookmarks": "Šķiet, ka jums vēl nav grāmatz<PERSON>mju. Noklikšķiniet uz sarunas un pievieno<PERSON>et jaunu.", "com_ui_no_category": "Nav kate<PERSON>", "com_ui_no_changes": "Nav at<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_no_data": "kaut kam šeit ir jānotiek. bija tukšs", "com_ui_no_personalization_available": "Pašlaik nav pieejamas personalizācijas opcijas", "com_ui_no_read_access": "Jums nav atļaujas skatīt atmiņas", "com_ui_no_terms_content": "Nav noteikumu un nosacījumu satura, ko parādīt", "com_ui_no_valid_items": "kaut kam šeit ir jānotiek. bija tukšs", "com_ui_none": "Neviens", "com_ui_not_used": "Nav izmantots", "com_ui_nothing_found": "Nekas nav atrasts", "com_ui_oauth": "OAuth", "com_ui_oauth_connected_to": "Izveidots savienojums ar", "com_ui_oauth_error_callback_failed": "Autentifikācijas callback neizdev<PERSON><PERSON>. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>.", "com_ui_oauth_error_generic": "Autentifikācija neizdev<PERSON>. <PERSON><PERSON>, mēģiniet vēlreiz.", "com_ui_oauth_error_invalid_state": "Nederīgs stāvokļa parametrs. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>.", "com_ui_oauth_error_missing_code": "Trūkst autorizācijas koda. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlreiz.", "com_ui_oauth_error_missing_state": "<PERSON>r<PERSON><PERSON>t stāvokļa parametrs. <PERSON><PERSON><PERSON><PERSON>, mēģiniet vēlre<PERSON>.", "com_ui_oauth_error_title": "Autentifikācija <PERSON>izdev<PERSON>", "com_ui_oauth_flow_desc": "Pabeidziet OAuth plūsmu jaunajā logā un pēc tam atgriezieties šeit.", "com_ui_oauth_success_description": "Jūsu autentifikācija bija veiksmīga. Šis logs aizvērsies pēc", "com_ui_oauth_success_title": "Autentifikācija veiksmīga", "com_ui_of": "no", "com_ui_off": "<PERSON>z<PERSON>lē<PERSON><PERSON>", "com_ui_offline": "Bezsaistē", "com_ui_on": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_openai": "OpenAI", "com_ui_optional": "(pēc izv<PERSON>)", "com_ui_page": "<PERSON><PERSON>", "com_ui_preferences_updated": "Preferences ve<PERSON><PERSON><PERSON><PERSON>", "com_ui_prev": "Iepriek<PERSON><PERSON><PERSON><PERSON>", "com_ui_preview": "Priekšskatījums", "com_ui_privacy_policy": "Privātuma politika", "com_ui_privacy_policy_url": "Privātuma politika web adrese", "com_ui_prompt": "Uzvedne", "com_ui_prompt_already_shared_to_all": "<PERSON><PERSON> jau ir kopīgota ar visiem lietotā<PERSON>em.", "com_ui_prompt_name": "Uzvednes nosaukums", "com_ui_prompt_name_required": "Uzvednes nosaukums ir obligāts", "com_ui_prompt_preview_not_shared": "Autors nav atļāvis sadarbību šajā uzvednē.", "com_ui_prompt_text": "Teksts", "com_ui_prompt_text_required": "<PERSON><PERSON><PERSON> ir obligāts", "com_ui_prompt_update_error": "<PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>.", "com_ui_prompts": "Uzvednes", "com_ui_prompts_allow_create": "Atļaut uzvedņu izveidi", "com_ui_prompts_allow_share_global": "Atļaut kopīgot uzvednes visiem lietotājiem", "com_ui_prompts_allow_use": "Atļaut i<PERSON>t uzvednes", "com_ui_provider": "Pakalpojumu sniedzējs", "com_ui_quality": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_read_aloud": "<PERSON><PERSON><PERSON>", "com_ui_redirecting_to_provider": "Pāradresācija uz {{0}}, lūdzu, uzgaidiet...", "com_ui_reference_saved_memories": "Atsauces uz saglabātajām at<PERSON>m", "com_ui_reference_saved_memories_description": "Ļaujiet asistentam atsaukties uz jūsu saglabātajām atmiņām un izmantot tās, atbildot", "com_ui_refresh_link": "Atsvaid<PERSON><PERSON>t saiti", "com_ui_regenerate": "At<PERSON>uno<PERSON>", "com_ui_regenerate_backup": "Atjaunot rezerves kodus", "com_ui_regenerating": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "com_ui_region": "Reģions", "com_ui_reinitialize": "Reinicializēt", "com_ui_rename": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_rename_conversation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON>u", "com_ui_rename_failed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON><PERSON>u", "com_ui_rename_prompt": "Pārdēvēt uzvedni", "com_ui_requires_auth": "Nepieciešama autentifikāci<PERSON>", "com_ui_reset_var": "Atiestat<PERSON>t {{0}}", "com_ui_reset_zoom": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_result": "Re<PERSON>ltā<PERSON>", "com_ui_revoke": "Atsaukt", "com_ui_revoke_info": "Atsaukt visus lietotāja sniegtos kredenciā<PERSON> datus", "com_ui_revoke_key_confirm": "Vai tiešām vēlaties atsaukt šo atslēgu?", "com_ui_revoke_key_endpoint": "Atsaukt atslēgu priekš {{0}}", "com_ui_revoke_keys": "Atsaukt atslēgas", "com_ui_revoke_keys_confirm": "Vai tiešām vēlaties atsaukt visas atslēgas?", "com_ui_role_select": "<PERSON><PERSON>", "com_ui_roleplay": "<PERSON><PERSON>", "com_ui_run_code": "Palaišanas kods", "com_ui_run_code_error": "<PERSON><PERSON><PERSON><PERSON>, i<PERSON><PERSON><PERSON><PERSON> kodu", "com_ui_save": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_save_badge_changes": "Vai saglabāt emblē<PERSON> i<PERSON>?", "com_ui_save_submit": "Saglabāt un iesniegt", "com_ui_saved": "Saglabāts!", "com_ui_saving": "Saglabā...", "com_ui_schema": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_scope": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_search": "Meklēt", "com_ui_seconds": "<PERSON><PERSON><PERSON>", "com_ui_secret_key": "Slepenā at<PERSON>lēga", "com_ui_select": "<PERSON><PERSON><PERSON>", "com_ui_select_all": "<PERSON><PERSON><PERSON> visu", "com_ui_select_file": "Atlasiet failu", "com_ui_select_model": "Izvēlieties modeli", "com_ui_select_provider": "Izvēlieties pakalpojumu sniedzēju", "com_ui_select_provider_first": "Vispirms izvēlieties pakalpojumu sniedzēju", "com_ui_select_region": "Izvēlieties reģionu", "com_ui_select_search_model": "Meklēt modeli pēc no<PERSON>ukuma", "com_ui_select_search_plugin": "Me<PERSON><PERSON><PERSON><PERSON> spraudni pēc no<PERSON>", "com_ui_select_search_provider": "Me<PERSON><PERSON><PERSON><PERSON><PERSON> pakalpojumu sniedzējs pēc no<PERSON>ma", "com_ui_select_search_region": "Meklēt reģionu pēc nosaukuma", "com_ui_set": "Uzlikts", "com_ui_share": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_share_create_message": "<PERSON><PERSON><PERSON> vārds un visas zi<PERSON>, ko pievienojat pēc kop<PERSON>, paliek priv<PERSON>.", "com_ui_share_delete_error": "<PERSON><PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>, r<PERSON><PERSON><PERSON>.", "com_ui_share_error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>.", "com_ui_share_form_description": "kaut kam šeit ir jānotiek. bija tukšs", "com_ui_share_link_to_chat": "<PERSON><PERSON><PERSON><PERSON> sa<PERSON> sa<PERSON>ai", "com_ui_share_to_all_users": "<PERSON><PERSON><PERSON><PERSON> ar visiem lie<PERSON>", "com_ui_share_update_message": "<PERSON><PERSON><PERSON>, pie<PERSON><PERSON><PERSON><PERSON> norādījumi un visas zi<PERSON>, ko pievienojat pēc kop<PERSON>nas, paliek priv<PERSON>.", "com_ui_share_var": "<PERSON><PERSON><PERSON><PERSON> {{0}}", "com_ui_shared_link_bulk_delete_success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON> ir veiksmīgi iz<PERSON>z<PERSON>.", "com_ui_shared_link_delete_success": "Koplietotā saite ir veiksmīgi izdzē<PERSON>.", "com_ui_shared_link_not_found": "<PERSON><PERSON><PERSON><PERSON><PERSON> saite nav atrasta", "com_ui_shared_prompts": "Koplietotas uzvednes", "com_ui_shop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_show": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_show_all": "<PERSON><PERSON><PERSON><PERSON><PERSON> visu", "com_ui_show_image_details": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_show_password": "<PERSON><PERSON><PERSON><PERSON><PERSON> paroli", "com_ui_show_qr": "R<PERSON><PERSON><PERSON>t QR kodu", "com_ui_sign_in_to_domain": "<PERSON><PERSON><PERSON>ī<PERSON> {{0}}", "com_ui_simple": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_size": "Izmērs", "com_ui_special_var_current_date": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> datums", "com_ui_special_var_current_datetime": "Pašreizējais datums un laiks", "com_ui_special_var_current_user": "Pašreiz<PERSON><PERSON><PERSON>", "com_ui_special_var_iso_datetime": "UTC ISO datums un laiks", "com_ui_special_variables": "Īpašie mainīgie:", "com_ui_special_variables_more_info": "Nolaižamajā izvēlnē varat atlasīt īpašos mainīgos:{{current_date}}` (šodienas datums un nedēļas diena), `{{current_datetime}}` (vietējais datums un laiks), `{{utc_iso_datetime}}` (UTC ISO datums/laiks) un `{{current_user}} (jūsu konta nosaukums).", "com_ui_speech_while_submitting": "<PERSON><PERSON><PERSON> i<PERSON> runu, kam<PERSON>r tiek ģenerēta atbilde.", "com_ui_sr_actions_menu": "<PERSON><PERSON><PERSON>rt darbību izvēlni priekš \"{{0}}\"", "com_ui_stop": "Apstāties", "com_ui_storage": "<PERSON>zg<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_submit": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_teach_or_explain": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_temporary": "<PERSON><PERSON><PERSON> saruna", "com_ui_terms_and_conditions": "<PERSON><PERSON><PERSON> un nosacījumi", "com_ui_terms_of_service": "Pakalpojumu <PERSON><PERSON>", "com_ui_thinking": "<PERSON><PERSON><PERSON><PERSON>...", "com_ui_thoughts": "<PERSON><PERSON>", "com_ui_token": "tokens", "com_ui_token_exchange_method": "To<PERSON>u a<PERSON> metode", "com_ui_token_url": "Tokena URL", "com_ui_tokens": "<PERSON>i", "com_ui_tool_collection_prefix": "<PERSON><PERSON><PERSON> kole<PERSON> no", "com_ui_tool_info": "Informācija par rīku", "com_ui_tool_more_info": "Vairāk informācijas par šo rīku", "com_ui_tools": "<PERSON><PERSON><PERSON>", "com_ui_travel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_trust_app": "Es uzticos šai lietotnei", "com_ui_unarchive": "Atarhivēt", "com_ui_unarchive_error": "<PERSON><PERSON>zde<PERSON><PERSON><PERSON> at<PERSON> sa<PERSON>u", "com_ui_unknown": "<PERSON><PERSON>inā<PERSON>", "com_ui_unset": "Neuzlikts", "com_ui_untitled": "<PERSON><PERSON>", "com_ui_update": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_update_mcp_error": "Izveidojot vai atjauninot MCP, r<PERSON><PERSON><PERSON>.", "com_ui_update_mcp_success": "Veiksmīgi izveidots vai atjaunināts MCP", "com_ui_upload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_upload_code_files": "Augšupielā<PERSON><PERSON>t koda interpretētājam", "com_ui_upload_delay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> \"{{0}}\" a<PERSON><PERSON><PERSON> vairāk laika nekā paredz<PERSON>ts. <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, kamēr faila indek<PERSON>na ir pabe<PERSON>ta izguvei.", "com_ui_upload_error": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, r<PERSON><PERSON><PERSON>.", "com_ui_upload_file_context": "Augšupielā<PERSON><PERSON>t failu kontekstā", "com_ui_upload_file_search": "Augšupielādēt failu me<PERSON>", "com_ui_upload_files": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> failus", "com_ui_upload_image": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>", "com_ui_upload_image_input": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> att<PERSON>", "com_ui_upload_invalid": "Nederīgs augšupiel<PERSON> fails. Attē<PERSON> jāb<PERSON>t tā<PERSON>, kas nepā<PERSON><PERSON><PERSON>z ierobežojumu.", "com_ui_upload_invalid_var": "Nederīgs augšupielādējams fails. Att<PERSON><PERSON> jābūt ne lielākam par {{0}} MB", "com_ui_upload_ocr_text": "Augšupielād<PERSON><PERSON> kā tekstu", "com_ui_upload_success": "Fails veiksmīgi aug<PERSON>", "com_ui_upload_type": "Atlasiet augšupielādes veidu", "com_ui_usage": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_use_2fa_code": "Izmantojiet 2FA kodu", "com_ui_use_backup_code": "Izmantojiet rezerves kodu", "com_ui_use_memory": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_use_micrphone": "Izmantot mikrofonu", "com_ui_used": "Lietots", "com_ui_value": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_variables": "Mainīgie", "com_ui_variables_info": "<PERSON><PERSON>go <PERSON> tekstā izmantojiet dubultā<PERSON>, pie<PERSON><PERSON><PERSON>, `{{example variable}}`, lai vē<PERSON><PERSON><PERSON> a<PERSON>, i<PERSON><PERSON><PERSON><PERSON>.", "com_ui_verify": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_version_var": "<PERSON><PERSON><PERSON> {{0}}", "com_ui_versions": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_view_memory": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_view_source": "S<PERSON>īt avota sarunu", "com_ui_web_search": "Tīmekļ<PERSON>", "com_ui_web_search_cohere_key": "Ievadiet Cohere API atslēgu", "com_ui_web_search_firecrawl_url": "Firecrawl API URL (pēc izvēles)", "com_ui_web_search_jina_key": "Ievadiet Jina API atslēgu", "com_ui_web_search_processing": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON>", "com_ui_web_search_provider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_web_search_provider_searxng": "SearXNG", "com_ui_web_search_provider_serper": "Serper API", "com_ui_web_search_provider_serper_key": "Iegūstiet savu Serper API atslēgu", "com_ui_web_search_reading": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "com_ui_web_search_reranker": "<PERSON><PERSON><PERSON>", "com_ui_web_search_reranker_cohere": "Cohere", "com_ui_web_search_reranker_cohere_key": "Iegūstiet savu Cohere API atslēgu", "com_ui_web_search_reranker_jina": "Jina AI", "com_ui_web_search_reranker_jina_key": "Iegūstiet savu Jina API atslēgu", "com_ui_web_search_scraper": "<PERSON><PERSON><PERSON>", "com_ui_web_search_scraper_firecrawl": "Firecrawl API", "com_ui_web_search_scraper_firecrawl_key": "Iegūstiet savu Firecrawl API atslēgu", "com_ui_web_search_searxng_api_key": "Ievadiet SearXNG API atslēgu (pēc izvēles)", "com_ui_web_search_searxng_instance_url": "SearXNG Instance URL", "com_ui_web_searching": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "com_ui_web_searching_again": "<PERSON><PERSON><PERSON><PERSON><PERSON> meklē tīmeklī", "com_ui_weekend_morning": "Priecīgu <PERSON><PERSON>", "com_ui_write": "Raks<PERSON><PERSON><PERSON><PERSON>", "com_ui_x_selected": "{{0}} atlasīts", "com_ui_yes": "Jā", "com_ui_zoom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "com_user_message": "Tu"}