{"chat_direction_left_to_right": "Iets moet hier. Was leeg", "chat_direction_right_to_left": "Iets moet hier. Was leeg", "com_a11y_ai_composing": "De AI is nog bezig met het formuleren van een antwoord.", "com_a11y_end": "De <PERSON> is klaar met het antwoord.", "com_a11y_start": "De <PERSON> is begonnen met antwo<PERSON><PERSON>.", "com_agents_allow_editing": "<PERSON>a sndere gebruikers toe om je agent te bewerken", "com_agents_by_librechat": "door LibreChat", "com_agents_code_interpreter": "<PERSON><PERSON> ing<PERSON><PERSON>, kan je agent de LibreChat Code Interpreter API gebruiken om gegenereerde code, inclusief het verwerken van bestanden, veilig uit te voeren. Vereist een geldige API-sleutel.", "com_agents_code_interpreter_title": "Code Interpreter API", "com_agents_create_error": "<PERSON>r is een fout opgetreden bij het aanmaken van je agent.", "com_agents_description_placeholder": "Optioneel: <PERSON><PERSON><PERSON><PERSON><PERSON> hier je agent", "com_agents_enable_file_search": "File Search inschakelen", "com_agents_file_context": "File Context (OCR)", "com_agents_file_context_disabled": "Agent moet worden aangemaakt voordat bestanden worden geüpload voor File Context", "com_agents_file_context_info": "Bestanden die als \"Context\" worden geüpload, worden verwerkt met OCR voor tekstherkenning. De tekst wordt daarna toegevoegd aan de instructies van de Agent. Ideaal voor documenten, a<PERSON><PERSON><PERSON><PERSON> met tekst of PDF's waarvan je de volledige tekstinhoud nodig hebt.\"", "com_agents_file_search_disabled": "<PERSON>ak eerst een Agent aan voordat je bestanden uploadt voor File Search.", "com_agents_file_search_info": "Als deze functie is ingeschakeld, krijgt de agent informatie over de exacte bestandsnamen die hieronder staan vermeld, zodat deze relevante context uit deze bestanden kan ophalen.", "com_agents_instructions_placeholder": "De systeeminstructies die de agent gebruikt", "com_agents_missing_provider_model": "Selecteer een provider en model voordat je een agent aanmaakt.", "com_agents_name_placeholder": "<PERSON> <PERSON> van de <PERSON>", "com_agents_no_access": "Je hebt geen toegang om deze agent te bewerken.", "com_agents_not_available": "Agent ni<PERSON>", "com_agents_search_info": "<PERSON><PERSON>, stelt het je agent in staat om het web te doorzoeken naar actuele informatie. Vereist een geldige API-sleutel.", "com_agents_search_name": "Agents zoeken op naam", "com_agents_update_error": "<PERSON>r is een fout opgetreden bij het updaten van je agent.", "com_assistants_action_attempt": "Assistent wil praten met {{0}}", "com_assistants_actions": "Actions", "com_assistants_actions_disabled": "Maak een assistent aan voordat je actions toevoegt.", "com_assistants_actions_info": "Laat je Assistent informatie ophalen of acties uitvoeren via API’s", "com_assistants_add_actions": "<PERSON><PERSON>", "com_assistants_add_tools": "<PERSON><PERSON>", "com_assistants_allow_sites_you_trust": "Sta alleen sites to die je vertrouwt", "com_assistants_append_date": "Voeg huidige datum & tijd toe", "com_assistants_append_date_tooltip": "<PERSON><PERSON>, worden de huidige datum en tijd van de client toegevoegd aan de systeeminstructies van de assistent.", "com_assistants_attempt_info": "Assistent wil het volgende sturen:", "com_assistants_available_actions": "Beschikbare Acties", "com_assistants_capabilities": "Mogelijkheden", "com_assistants_code_interpreter": "Code Interpreter", "com_assistants_code_interpreter_files": "Bestanden hieronder zijn alleen voor Code Interpreter", "com_assistants_code_interpreter_info": "Code Interpreter stelt de assistent in staat om code te schrijven en uit te voeren. Deze tool kan bestanden met uiteenlopende data en opmaak verwerken en bestanden genereren, zoals grafieken.", "com_assistants_completed_action": "<PERSON><PERSON><PERSON><PERSON> met {{0}}", "com_assistants_completed_function": "{{0}} uitgevoerd", "com_assistants_conversation_starters": "Gesprekstarters", "com_assistants_conversation_starters_placeholder": "Voeg een gesprekstarters toe", "com_assistants_create_error": "Er was een error bij het maken van je assistant", "com_assistants_create_success": "Succesvol gecreëerd", "com_assistants_delete_actions_error": "Er was een error bij het verwijderen van de actie", "com_assistants_delete_actions_success": "Succesvol een actie verwij<PERSON><PERSON> van Assistent", "com_assistants_description_placeholder": "Optioneel: <PERSON><PERSON><PERSON><PERSON><PERSON> Assistant hier", "com_assistants_domain_info": "Assistant he<PERSON><PERSON> deze informatie verstuurd naar {{0}}", "com_assistants_file_search": "<PERSON><PERSON> naar bestanden", "com_assistants_file_search_info": "Met bestandszoekopdrachten krijgt de assistent kennis van bestanden die u of uw gebruikers uploaden. Z<PERSON>ra een bestand is geüpload, beslist de assistent automatisch wanneer de content moet worden opgehaald op basis van gebruikersverzoeken. Het toevoegen van vectoropslag voor bestandszoekopdrachten wordt nog niet ondersteund. U kunt ze toevoegen vanuit de Provider Playground of bestanden toevoegen aan berichten voor bestandszoekopdrachten op basis van een thread.", "com_assistants_function_use": "Assistent gebruikt {{0}}", "com_assistants_image_vision": "Beeldvisie", "com_assistants_instructions_placeholder": "De systeeminstructies die de assistent gebruikt", "com_assistants_knowledge": "<PERSON><PERSON>", "com_assistants_knowledge_disabled": "Er moet een assistent worden aangemaakt en de code-interpreter of het ophaalprogramma moet worden ingeschakeld en opgeslagen voordat u bestanden als kennis kunt uploaden.", "com_assistants_knowledge_info": "Als u bestanden uploadt on<PERSON>, kunnen gesprekken met uw Assistent de inhoud van bestanden bevatten.", "com_assistants_max_starters_reached": "Maximaal aantal gespreksstarters bereikt", "com_assistants_name_placeholder": "Optioneel: <PERSON>", "com_assistants_non_retrieval_model": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> is niet ingeschakeld op dit model. Selecteer een ander model.", "com_assistants_retrieval": "<PERSON><PERSON><PERSON>", "com_assistants_running_action": "Lopende actie", "com_assistants_running_var": "{{0}} uitvoeren", "com_assistants_search_name": "Zoek assistenten op naam", "com_assistants_update_actions_error": "Er is een fout opgetreden bij het maken of bijwerken van de actie.", "com_assistants_update_actions_success": "Actie succesvol aangemaakt of bijgewerkt", "com_assistants_update_error": "Er is een fout opgetreden bij het bijwerken van uw assistent.", "com_assistants_update_success": "Succesvol bijgewerkt", "com_auth_already_have_account": "Heb je al een account?", "com_auth_apple_login": "<PERSON><PERSON><PERSON><PERSON> met <PERSON>", "com_auth_back_to_login": "Terug naar Inloggen", "com_auth_click": "Klik", "com_auth_click_here": "<PERSON><PERSON> hier", "com_auth_continue": "Doorgaan", "com_auth_create_account": "Maak uw account aan", "com_auth_discord_login": "Inloggen met Discord", "com_auth_email": "E-mailadres", "com_auth_email_address": "E-mailadres", "com_auth_email_max_length": "E-mailadres mag niet langer zijn dan 120 tekens", "com_auth_email_min_length": "E-mailadres moet minstens 6 tekens bevatten", "com_auth_email_pattern": "Je moet een geldig e-mailadres invoeren", "com_auth_email_required": "E-mailadres is verplicht", "com_auth_email_resend_link": "E-mail opnieuw verzenden", "com_auth_email_resent_failed": "Het is niet gelukt om de verificatie-e-mail opnieuw te verzenden", "com_auth_email_resent_success": "Verificatie-e-mail succesvol opnieuw verzonden", "com_auth_email_verification_failed": "E-mailverificatie mislukt", "com_auth_email_verification_failed_token_missing": "Verifica<PERSON> mislukt, token ontbreekt", "com_auth_email_verification_in_progress": "Email verifieren, een moment geduld a.u.b.", "com_auth_email_verification_invalid": "Ongeldige e-mailverificatie", "com_auth_email_verification_redirecting": "<PERSON><PERSON><PERSON> in {{0}} seconden...", "com_auth_email_verification_resend_prompt": "E-mail niet ontvangen?", "com_auth_email_verification_success": "E-mail succesvol geverifieerd", "com_auth_email_verifying_ellipsis": "<PERSON><PERSON> met veri<PERSON><PERSON><PERSON>...", "com_auth_error_create": "Er is een fout opgetreden bij het registreren van uw account. Probeer het opnieuw.", "com_auth_error_invalid_reset_token": "Dit wachtwoord resettoken is niet langer geldig.", "com_auth_error_login": "<PERSON><PERSON> niet in<PERSON><PERSON> met de verstrekte informatie. Controleer uw referenties en probeer het opnieuw.", "com_auth_error_login_ban": "Uw account is tijdelijk verbannen vanwege schendingen van onze service.", "com_auth_error_login_rl": "Te veel inlogpogingen in een korte tijd. <PERSON><PERSON>r het later nog eens.", "com_auth_error_login_server": "Er was een interne serverfout. Wacht een paar momenten en probeer het opnieuw.", "com_auth_error_login_unverified": "Je account is nog niet geverifieerd. Controleer je e-mail voor een verificatielink.", "com_auth_facebook_login": "Inloggen met Facebook", "com_auth_full_name": "Volledige naam", "com_auth_github_login": "Inloggen met <PERSON><PERSON><PERSON>", "com_auth_google_login": "Inloggen met Google", "com_auth_here": "HIER", "com_auth_login": "Inloggen", "com_auth_login_with_new_password": "Je kunt nu inloggen met je nieuwe wachtwo<PERSON>.", "com_auth_name_max_length": "<PERSON>am mag niet langer zijn dan 80 tekens", "com_auth_name_min_length": "Naam moet minstens 3 tekens bevatten", "com_auth_name_required": "Naam is verplicht", "com_auth_no_account": "Heb je geen account?", "com_auth_password": "Wachtwoord", "com_auth_password_confirm": "Bevestig wachtwoord", "com_auth_password_forgot": "Wachtwoord vergeten?", "com_auth_password_max_length": "Wachtwoord moet minder dan 128 tekens bevatten", "com_auth_password_min_length": "Wachtwoord moet minstens 8 tekens bevatten", "com_auth_password_not_match": "Wachtwoorden komen niet overeen", "com_auth_password_required": "Wachtwoord is verplicht", "com_auth_registration_success_generic": "Controleer je e-mail om je e-mailadres te verifiëren.", "com_auth_registration_success_insecure": "Registratie succesvol.", "com_auth_reset_password": "Stel uw wachtwoord opnieuw in", "com_auth_reset_password_if_email_exists": "Als er een account bestaat met dat e-mailadres, is er een e-mail met instructies voor het opnieuw instellen van je wachtwoord verzonden. Vergeet niet je spamfolder te controleren.", "com_auth_reset_password_link_sent": "E-mail verzonden", "com_auth_reset_password_success": "Wachtwoord opnieuw ingesteld", "com_auth_saml_login": "Ga door met SAML", "com_auth_sign_in": "Inloggen", "com_auth_sign_up": "Aanmelden", "com_auth_submit_registration": "Registratie indienen", "com_auth_to_reset_your_password": "om uw wachtwoord opnieuw in te stellen.", "com_auth_to_try_again": "om het opnieuw te proberen.", "com_auth_username": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (optioneel)", "com_auth_username_max_length": "Gebruikersnaam mag niet langer zijn dan 20 tekens", "com_auth_username_min_length": "Gebruikersnaam moet minstens 2 tekens bevatten", "com_auth_verify_your_identity": "Verifieer je identiteit", "com_auth_welcome_back": "Welkom terug", "com_citation_more_details": "<PERSON><PERSON> details van {{label}}", "com_citation_source": "<PERSON><PERSON>", "com_click_to_download": "(klik hier om te downloaden)", "com_download_expired": "(download verlopen)", "com_download_expires": "(klik hier om te downloaden - verloopt op {{0}})", "com_endpoint": "Eindpunt", "com_endpoint_agent": "Agent", "com_endpoint_agent_model": "Agentmodel (Aanbevolen: GPT-3,5)", "com_endpoint_agent_placeholder": "Selecteer een Agent", "com_endpoint_ai": "AI", "com_endpoint_anthropic_maxoutputtokens": "Maximum aantal tokens dat kan worden gegenereerd in de reactie. Geef een lagere waarde op voor kortere reacties en een hogere waarde voor langere reacties.", "com_endpoint_anthropic_prompt_cache": "Promptcaching maakt het mogelijk om een grote context of instructies opnieuw te gebruiken bij API-aanroepen, wat kosten en vertraging vermindert.", "com_endpoint_anthropic_temp": "Vari<PERSON>t van 0 tot 1. Gebruik een lagere temp voor analytische / meerkeuze taken en een hogere temp voor creatieve en generatieve taken. We raden aan dit of Top P te wijzigen, maar niet beide.", "com_endpoint_anthropic_thinking": "<PERSON><PERSON><PERSON>t interne redenering in voor ondersteunde Claude-modellen (3.7 Sonnet). Let op: vereist dat het \"Thinking Budget\" is ingesteld en lager is dan \"Max Output Tokens\".", "com_endpoint_anthropic_thinking_budget": "Bepaalt het maximale aantal tokens dat <PERSON> mag gebruiken voor zijn interne redeneerproces. Grotere budgetten kunnen de kwaliteit van de antwoorden verbeteren door grondigere analyses mogelijk te maken voor complexe problemen, hoewel <PERSON> mogelijk niet het volledige toegewezen budget gebruikt, vooral bij bereiken boven de 32K. Deze instelling moet lager zijn dan \"Max Output Tokens\".", "com_endpoint_anthropic_topk": "Top-k verandert hoe het model tokens selecteert voor uitvoer. Een top-k van 1 betekent dat het geselecteerde token het meest waarschijnlijk is van alle tokens in de vocabulaire van het model (ook wel 'greedy decoding' genoemd), terwijl een top-k van 3 betekent dat het volgende token wordt geselecteerd uit de 3 meest waarschijnlijke tokens (met behulp van temperatuur).", "com_endpoint_anthropic_topp": "Top-p verandert hoe het model tokens selecteert voor uitvoer. Tokens worden geselecteerd van meest K (zie topK-parameter) waarschijnlijk tot minst waarschijnlijk totdat de som van hun kansen gelijk is aan de top-p-waarde.", "com_endpoint_assistant": "Assistent", "com_endpoint_assistant_model": "Assistent Model", "com_endpoint_completion": "Volto<PERSON>ing", "com_endpoint_completion_model": "Voltooiingsmodel (Aanbevolen: GPT-4)", "com_endpoint_config_click_here": "<PERSON><PERSON>", "com_endpoint_config_google_api_key": "Google API Key", "com_endpoint_config_google_cloud_platform": "(van Google Cloud Platform)", "com_endpoint_config_google_gemini_api": "(Gemini API)", "com_endpoint_config_google_service_key": "Google Service Account Key", "com_endpoint_config_key": "API-sleutel instellen", "com_endpoint_config_key_encryption": "<PERSON><PERSON> sleutel wordt versleuteld en verwijderd op", "com_endpoint_config_key_for": "API-sleutel instellen voor", "com_endpoint_config_key_google_need_to": "U moet", "com_endpoint_config_key_google_service_account": "Maak een serviceaccount", "com_endpoint_config_key_google_vertex_ai": "Vertex AI inschakelen", "com_endpoint_config_key_google_vertex_api": "API op Google Cloud, dan", "com_endpoint_config_key_google_vertex_api_role": "Zorg ervoor dat u op 'Maken en doorgaan' klikt om ten minste de 'Vertex AI-gebruiker'-rol te geven. Maak ten slotte een JSON-sleutel aan om hier te importeren.", "com_endpoint_config_key_import_json_key": "Serviceaccount-JSON-sleutel importeren.", "com_endpoint_config_key_import_json_key_invalid": "Ongeldige Serviceaccount-JSON-<PERSON>le<PERSON><PERSON>, heb je het juiste bestand geïmporteerd?", "com_endpoint_config_key_import_json_key_success": "Serviceaccount-JSON-sleutel succesvol geïmporteerd", "com_endpoint_config_key_name": "<PERSON><PERSON><PERSON><PERSON>", "com_endpoint_config_key_never_expires": "Je sleutel zal nooit verlopen.", "com_endpoint_config_placeholder": "<PERSON><PERSON> je sleutel in via de header om te kunnen chatten.", "com_endpoint_config_value": "<PERSON><PERSON><PERSON> waarde in voor", "com_endpoint_context": "Context", "com_endpoint_context_info": "Het maximale aantal tokens dat kan worden gebruikt voor context. Gebruik dit om te bepalen hoeveel tokens per verzoek worden verzonden. Als dit niet wordt opgegeven, worden de standaardwaarden van het systeem gebruikt op basis van de bekende contextgrootte van modellen. Het instellen van hogere waarden kan leiden tot fouten en/of hogere kosten per token.", "com_endpoint_context_tokens": "Maximale Context Tokens", "com_endpoint_custom_name": "<PERSON>ang<PERSON><PERSON><PERSON> naam", "com_endpoint_default": "standaard", "com_endpoint_default_blank": "standaard: leeg", "com_endpoint_default_empty": "standaard: leeg", "com_endpoint_default_with_num": "standaard: {{0}}", "com_endpoint_examples": " Voorinstellingen", "com_endpoint_export": "Exporteren", "com_endpoint_export_share": "Exporteer/Deel", "com_endpoint_frequency_penalty": "Frequent<PERSON>raf", "com_endpoint_func_hover": "<PERSON><PERSON><PERSON> het gebru<PERSON> van plug-ins als OpenAI-functies in", "com_endpoint_google_custom_name_placeholder": "<PERSON><PERSON> een aangepaste naam in voor Google", "com_endpoint_google_maxoutputtokens": "Maximum aantal tokens dat kan worden gegenereerd in de reactie. Geef een lagere waarde op voor kortere reacties en een hogere waarde voor langere reacties.", "com_endpoint_google_temp": "Hogere waarden = meer will<PERSON>, terwij<PERSON> lagere waarden = meer gericht en deterministisch. We raden aan dit of Top P te wijzigen, maar niet beide.", "com_endpoint_google_topk": "Top-k verandert hoe het model tokens selecteert voor uitvoer. Een top-k van 1 betekent dat het geselecteerde token het meest waarschijnlijk is van alle tokens in de vocabulaire van het model (ook wel 'greedy decoding' genoemd), terwijl een top-k van 3 betekent dat het volgende token wordt geselecteerd uit de 3 meest waarschijnlijke tokens (met behulp van temperatuur).", "com_endpoint_google_topp": "Top-p verandert hoe het model tokens selecteert voor uitvoer. Tokens worden geselecteerd van meest K (zie topK-parameter) waarschijnlijk tot minst waarschijnlijk totdat de som van hun kansen gelijk is aan de top-p-waarde.", "com_endpoint_max_output_tokens": "<PERSON><PERSON>", "com_endpoint_message": "Bericht", "com_endpoint_my_preset": "<PERSON><PERSON>", "com_endpoint_no_presets": "Nog geen voorinstellingen, gebruik de instellingenknop om er een te maken", "com_endpoint_open_menu": "Open menu", "com_endpoint_openai_custom_name_placeholder": "<PERSON>el een aangepaste naam in voor ChatGPT", "com_endpoint_openai_freq": "Getal tussen -2,0 en 2,0. <PERSON><PERSON><PERSON>ve waarden straffen nieuwe tokens op basis van hun bestaande frequentie in de tekst tot nu toe, waard<PERSON> de kans dat het model de<PERSON><PERSON>de regel letterlijk herhaalt, afneemt.", "com_endpoint_openai_max": "Het max. aantal tokens dat kan worden gegenereerd. De totale lengte van invoer-tokens en gegenereerde tokens is beperkt door de contextlengte van het model.", "com_endpoint_openai_pres": "Getal tussen -2,0 en 2,0. <PERSON><PERSON><PERSON>ve waarden straffen nieuwe tokens op basis van of ze al voorkomen in de tekst tot nu toe, waardoor de kans dat het model over nieuwe onderwerpen praat toeneemt.", "com_endpoint_openai_prompt_prefix_placeholder": "<PERSON>el aangepaste instructies in om op te nemen in Systeembericht. Standaard: geen", "com_endpoint_openai_temp": "Hogere waarden = meer will<PERSON>, terwij<PERSON> lagere waarden = meer gericht en deterministisch. We raden aan dit of Top P te wijzigen, maar niet beide.", "com_endpoint_openai_topp": "Een alternatief voor sampling met temperatuur, genaamd nucleus sampling, waarbij het model de resultaten van de tokens met de top_p waarschijnlijkheidsmassa in overweging neemt. Dus 0,1 betekent dat alleen de tokens die de bovenste 10% waarschijnlijkheidsmassa omvatten, in overweging worden genomen. We raden aan dit of temperatuur te wijzigen, maar niet beide.", "com_endpoint_output": "Uit<PERSON><PERSON>", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "<PERSON>el aangepaste instructies in om op te nemen in Systeembericht. Standaard: geen", "com_endpoint_plug_skip_completion": "<PERSON><PERSON><PERSON><PERSON> overs<PERSON>an", "com_endpoint_plug_use_functions": "Gebruik functies inschakelen", "com_endpoint_presence_penalty": "Aanwezigheidsstraf", "com_endpoint_preset": "voorinstelling", "com_endpoint_preset_name": "<PERSON><PERSON> v<PERSON>ins<PERSON>ling", "com_endpoint_preset_selected": "Voorinstelling actief!", "com_endpoint_presets": "voorinstellingen", "com_endpoint_presets_clear_warning": "Weet u zeker dat u alle voorinstellingen wilt wissen? Dit is onomkeer<PERSON><PERSON>.", "com_endpoint_prompt_prefix": "Prompt-voorvoegsel", "com_endpoint_prompt_prefix_placeholder": "Stel aangepaste instructies of context in. Wordt genegeerd indien leeg.", "com_endpoint_save_as_preset": "Opslaan als voorinstelling", "com_endpoint_set_custom_name": "Stel een aangepaste naam in, voor het geval je deze voorinstelling kunt vinden", "com_endpoint_skip_hover": "<PERSON><PERSON><PERSON> het <PERSON><PERSON><PERSON> van de voltooiingsstap in, die het definitieve antwoord en gegenereerde stappen beoorde<PERSON>t", "com_endpoint_temperature": "Temperatuur", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_nav_archive_created_at": "Gemaakt op", "com_nav_archive_name": "<PERSON><PERSON>", "com_nav_archived_chats": "Gearchiveerde chats", "com_nav_auto_scroll": "Automatisch scrollen naar Nieuwste bij openen", "com_nav_balance": "Evenwich<PERSON>", "com_nav_clear_all_chats": "Alle chats wissen", "com_nav_clear_conversation": "Conversaties wissen", "com_nav_clear_conversation_confirm_message": "Weet u zeker dat u alle conversaties wilt wissen? Dit is onom<PERSON><PERSON><PERSON><PERSON>.", "com_nav_close_sidebar": "Zijbalk sluiten", "com_nav_confirm_clear": "Wissen bevestigen", "com_nav_enabled": "Ingeschakeld", "com_nav_export": "Exporteren", "com_nav_export_all_message_branches": "Alle berichtvertakkingen exporteren", "com_nav_export_conversation": "Conversatie exporteren", "com_nav_export_filename": "Bestandsnaam", "com_nav_export_filename_placeholder": "Stel de bestandsnaam in", "com_nav_export_include_endpoint_options": "Eindpuntopties opnemen", "com_nav_export_recursive": "Recursief", "com_nav_export_recursive_or_sequential": "Recursief of sequentieel?", "com_nav_export_type": "Type", "com_nav_font_size": "Lettertypegrootte", "com_nav_help_faq": "Help & FAQ", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "Auto detect", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_chinese": "中文", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "Taal", "com_nav_log_out": "Uitloggen", "com_nav_not_supported": "<PERSON><PERSON>", "com_nav_open_sidebar": "Zijbalk openen", "com_nav_plugin_auth_error": "Er trad een fout op bij het authenticeren van deze plugin. Probeer het opnieuw.", "com_nav_plugin_search": "Plug<PERSON> zoeken", "com_nav_plugin_store": "Plugin-opslag", "com_nav_search_placeholder": "Berichten doorzoeken", "com_nav_send_message": "Bericht verzenden", "com_nav_setting_data": "Gegevensbesturing", "com_nav_setting_general": "<PERSON><PERSON><PERSON><PERSON>", "com_nav_settings": "Instellingen", "com_nav_shared_links": "Gedeelde links", "com_nav_theme": "<PERSON>a", "com_nav_theme_dark": "<PERSON><PERSON>", "com_nav_theme_light": "Licht", "com_nav_theme_system": "Systeem", "com_nav_user": "GEBRUIKER", "com_ui_accept": "<PERSON>k accepteer", "com_ui_all": "alle", "com_ui_archive": "<PERSON><PERSON>", "com_ui_archive_error": "Kan conversatie niet archiveren", "com_ui_bookmark_delete_confirm": "Weet je zeker dat je deze bladwijzer wilt verwijderen?", "com_ui_bookmarks": "Bladwijzers", "com_ui_bookmarks_add_to_conversation": "Toevoegen aan huidig gesprek", "com_ui_bookmarks_count": "Aantal", "com_ui_bookmarks_create_error": "Er is een fout opgetreden bij het maken van de bladwi<PERSON>zer", "com_ui_bookmarks_create_success": "<PERSON><PERSON><PERSON><PERSON><PERSON> succesvol aangemaakt", "com_ui_bookmarks_delete_error": "Er is een fout opgetreden bij het verwij<PERSON>en van de bladwi<PERSON>", "com_ui_bookmarks_delete_success": "Bladwi<PERSON>zer succesvol verwijderd", "com_ui_bookmarks_description": "Beschrijving", "com_ui_bookmarks_new": "<PERSON><PERSON><PERSON> b<PERSON>", "com_ui_bookmarks_title": "Titel", "com_ui_bookmarks_update_error": "Er is een fout opgetreden bij het bijwerken van de bladwi<PERSON>zer", "com_ui_bookmarks_update_success": "Bladwijzer succesvol bijgewerkt", "com_ui_cancel": "<PERSON><PERSON><PERSON>", "com_ui_clear": "Wissen", "com_ui_close": "Sluiten", "com_ui_confirm_action": "Bevestig actie", "com_ui_continue": "Doorgaan", "com_ui_copied_to_clipboard": "Gekopieerd naar klembord", "com_ui_copy_link": "<PERSON>", "com_ui_copy_to_clipboard": "Kopiëren naar klembord", "com_ui_create_link": "<PERSON>", "com_ui_decline": "<PERSON>k accepteer niet", "com_ui_delete": "Verwijderen", "com_ui_delete_confirm": "<PERSON><PERSON><PERSON> wordt", "com_ui_delete_conversation": "Chat verwijderen?", "com_ui_edit": "Bewerken", "com_ui_enter": "Invoeren", "com_ui_examples": "Voorbeelden", "com_ui_happy_birthday": "Het is mijn eerste verjaard<PERSON>!", "com_ui_import_conversation_error": "Er is een fout opgetreden bij het importeren van je gesprekken", "com_ui_import_conversation_info": "Gesprekken importeren vanuit een JSON-bestand", "com_ui_import_conversation_success": "Gesprekken succesvol geïmporteerd", "com_ui_input": "Invoer", "com_ui_model": "Model", "com_ui_new_chat": "<PERSON><PERSON><PERSON> chat", "com_ui_next": "Volgende", "com_ui_no_terms_content": "<PERSON>n gebruiksvoorwaarden-inhoud om weer te geven", "com_ui_of": "van", "com_ui_prev": "Vorige", "com_ui_regenerate": "Opnieuw genereren", "com_ui_rename": "<PERSON><PERSON><PERSON><PERSON>", "com_ui_revoke": "Intrekken", "com_ui_revoke_info": "Trek alle door de gebruiker verstrekte referenties in", "com_ui_save": "Opsla<PERSON>", "com_ui_select_model": "Selecteer een model", "com_ui_share": "<PERSON><PERSON>", "com_ui_share_create_message": "<PERSON>w naam en alle berichten die u na het delen toevo<PERSON>t, blijven privé.", "com_ui_share_delete_error": "Er is een fout opgetreden bij het verwij<PERSON>en van de g<PERSON>e link.", "com_ui_share_error": "Er is een fout opgetreden bij het delen van de chatlink", "com_ui_share_link_to_chat": "Deel link naar chat", "com_ui_share_update_message": "<PERSON><PERSON> na<PERSON>, aangepaste instructies en alle berichten die u na het delen toevo<PERSON>t, blijven privé.", "com_ui_shared_link_not_found": "Gedeelde link niet gevonden", "com_ui_stop": "Stop", "com_ui_submit": "<PERSON><PERSON>", "com_ui_terms_and_conditions": "Gebruiksvoorwaarden", "com_ui_unarchive": "Uit archiveren", "com_ui_unarchive_error": "Kan conversatie niet uit archiveren", "com_ui_upload_success": "Bestand succesvol geüpload"}