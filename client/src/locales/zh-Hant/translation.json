{"com_a11y_ai_composing": "AI 仍在撰寫中", "com_a11y_end": "AI 已完成回覆", "com_a11y_start": "AI 已開始回覆。", "com_agents_allow_editing": "允許其他使用者編輯您的助理", "com_agents_by_librechat": "由 LibreChat 提供", "com_agents_code_interpreter": "啟用後，您的代理可以安全地使用 LibreChat 程式碼解譯器 API 來執行產生的程式碼，包括檔案處理功能。需要有效的 API 金鑰。", "com_agents_code_interpreter_title": "程式碼解譯器 API", "com_agents_create_error": "建立您的代理時發生錯誤。", "com_agents_description_placeholder": "選填：在此描述您的代理程式", "com_agents_enable_file_search": "啟用檔案搜尋", "com_agents_file_search_disabled": "必須先建立代理才能上傳檔案進行檔案搜尋。", "com_agents_file_search_info": "啟用後，代理將會被告知以下列出的確切檔案名稱，使其能夠從這些檔案中擷取相關內容。", "com_agents_instructions_placeholder": "代理程式使用的系統指令", "com_agents_missing_provider_model": "請在建立代理前選擇供應商和模型。", "com_agents_name_placeholder": "選填：代理人的名稱", "com_agents_no_access": "您沒有權限編輯此助理", "com_agents_not_available": "代理不可用", "com_agents_search_name": "依名稱搜尋代理", "com_agents_update_error": "更新您的代理時發生錯誤。", "com_assistants_actions": "操作", "com_assistants_actions_disabled": "您需要先建立一個助理，才能新增動作。", "com_assistants_actions_info": "讓您的助理透過 API 取得資訊或執行操作", "com_assistants_add_actions": "新增操作", "com_assistants_add_tools": "新增工具", "com_assistants_append_date": "添加當前日期和時間", "com_assistants_append_date_tooltip": "啟用後，當前客戶的日期和時間將附加到助手的系統指令中。", "com_assistants_available_actions": "可用操作", "com_assistants_capabilities": "功能", "com_assistants_code_interpreter": "程式碼解譯器", "com_assistants_code_interpreter_files": "以下檔案僅適用於程式碼解譯器：", "com_assistants_code_interpreter_info": "程式碼解譯器可讓助理撰寫和執行程式碼。此工具能處理各種資料格式的檔案，並產生圖表等檔案。", "com_assistants_completed_action": "與 {{0}} 對話完成", "com_assistants_completed_function": "已執行 {{0}}", "com_assistants_conversation_starters": "對話起點", "com_assistants_conversation_starters_placeholder": "輸入對話開場白", "com_assistants_create_error": "建立您的助理時發生錯誤。", "com_assistants_create_success": "已成功建立", "com_assistants_delete_actions_error": "刪除操作時發生錯誤", "com_assistants_delete_actions_success": "已成功刪除助理的操作", "com_assistants_description_placeholder": "選填：在此描述您的助理", "com_assistants_domain_info": "助理將此資訊傳送給 {{0}}", "com_assistants_file_search": "檔案搜尋", "com_assistants_file_search_info": "目前尚不支援為檔案搜尋附加向量儲存。您可以從提供者遊樂場附加它們，或在每個主題的基礎上為檔案搜尋附加檔案。", "com_assistants_function_use": "助理使用了 {{0}}", "com_assistants_image_vision": "影像視覺", "com_assistants_instructions_placeholder": "系統指令是助理使用的提示指令", "com_assistants_knowledge": "知識", "com_assistants_knowledge_disabled": "助理必須先建立，並啟用及儲存「程式碼解譯器」或「資訊檢索」功能，才能上傳檔案作為知識庫。", "com_assistants_knowledge_info": "如果您在「知識」下上傳檔案，與您的助理的對話可能會包含檔案內容。", "com_assistants_max_starters_reached": "已達對話起始項目的最大數量", "com_assistants_name_placeholder": "選填：助理的名稱", "com_assistants_non_retrieval_model": "此模型未啟用檔案搜尋功能。請選擇其他模型。", "com_assistants_retrieval": "檢索", "com_assistants_running_action": "執行中的動作", "com_assistants_search_name": "搜尋助理名稱", "com_assistants_update_actions_error": "更新或建立動作時發生錯誤。", "com_assistants_update_actions_success": "動作已成功建立或更新", "com_assistants_update_error": "更新您的助理時發生錯誤。", "com_assistants_update_success": "更新成功", "com_auth_already_have_account": "已經有帳號了？", "com_auth_back_to_login": "返回登入", "com_auth_click": "點選", "com_auth_click_here": "點選這裡", "com_auth_continue": "繼續", "com_auth_create_account": "建立您的帳號", "com_auth_discord_login": "使用 Discord 登入", "com_auth_email": "電子郵件", "com_auth_email_address": "電子郵件地址", "com_auth_email_max_length": "電子郵件不應超過 120 個字元", "com_auth_email_min_length": "電子郵件長度必須至少有 6 個字元", "com_auth_email_pattern": "您必須輸入有效的電子郵件地址", "com_auth_email_required": "電子郵件必填", "com_auth_email_resend_link": "重新傳送電子郵件", "com_auth_email_resent_failed": "重新傳送驗證電子郵件失敗", "com_auth_email_resent_success": "驗證郵件已重新傳送成功", "com_auth_email_verification_failed": "電子郵件驗證失敗", "com_auth_email_verification_failed_token_missing": "驗證失敗，缺少驗證令牌", "com_auth_email_verification_in_progress": "正在驗證您的電子郵件，請稍候", "com_auth_email_verification_invalid": "電子郵件驗證無效", "com_auth_email_verification_redirecting": "{{0}} 秒後重新導向...", "com_auth_email_verification_resend_prompt": "沒收到電子郵件？", "com_auth_email_verification_success": "電子郵件驗證成功", "com_auth_error_create": "嘗試註冊您的帳號時發生錯誤。請重試。", "com_auth_error_invalid_reset_token": "此密碼重設令牌已無效。", "com_auth_error_login": "無法使用提供的資訊登入。請檢查您的登入資訊後重試。", "com_auth_error_login_ban": "由於違反我們的服務條款，您的帳號已被暫時停用。", "com_auth_error_login_rl": "短時間內嘗試登入的次數過多。請稍後再試。", "com_auth_error_login_server": "發生內部伺服器錯誤。請稍候片刻，然後重試。", "com_auth_error_login_unverified": "您的帳號尚未驗證。請檢查您的電子郵件以取得驗證連結。", "com_auth_facebook_login": "使用 Facebook 登入", "com_auth_full_name": "全名", "com_auth_github_login": "使用 GitHub 登入", "com_auth_google_login": "使用 Google 登入", "com_auth_here": "這裡", "com_auth_login": "登入", "com_auth_login_with_new_password": "您現在可以使用新密碼登入。", "com_auth_name_max_length": "名稱長度長度必須少於 80 個字元", "com_auth_name_min_length": "名稱長度必須至少有 3 個字元", "com_auth_name_required": "名稱必填", "com_auth_no_account": "還沒有帳號？", "com_auth_password": "密碼", "com_auth_password_confirm": "確認密碼", "com_auth_password_forgot": "忘記密碼？", "com_auth_password_max_length": "密碼長度必須少於 128 個字元", "com_auth_password_min_length": "密碼長度必須至少有 8 個字元", "com_auth_password_not_match": "密碼不符", "com_auth_password_required": "密碼必填", "com_auth_registration_success_generic": "請檢查您的電子郵件以驗證電子郵件地址。", "com_auth_registration_success_insecure": "註冊成功。", "com_auth_reset_password": "重設密碼", "com_auth_reset_password_if_email_exists": "如果存在使用該電子郵件的帳號，我們已寄送一封含有密碼重設說明的郵件。請記得檢查您的垃圾郵件匣。", "com_auth_reset_password_link_sent": "電子郵件已傳送", "com_auth_reset_password_success": "密碼重設成功", "com_auth_sign_in": "登入", "com_auth_sign_up": "註冊", "com_auth_submit_registration": "送出", "com_auth_to_reset_your_password": "重設您的密碼。", "com_auth_to_try_again": "重試。", "com_auth_username": "使用者名稱（選填）", "com_auth_username_max_length": "使用者名稱長度必須少於 20 個字元", "com_auth_username_min_length": "使用者名稱長度必須至少有 2 個字元", "com_auth_welcome_back": "歡迎回來", "com_click_to_download": "（點選此處下載）", "com_download_expired": "下載已過期", "com_download_expires": "(點擊此處下載 - {{0}} 後過期)", "com_endpoint": "選項", "com_endpoint_agent": "代理", "com_endpoint_agent_model": "代理模型（建議：GPT-3.5）", "com_endpoint_agent_placeholder": "請選擇代理", "com_endpoint_ai": "AI", "com_endpoint_anthropic_maxoutputtokens": "設定回應中可生成的最大 token 數。若希望回應簡短，請設定較低的數值；若需較長的回應，則設定較高的數值。", "com_endpoint_anthropic_prompt_cache": "提示快取功能可在 API 呼叫間重複使用大型上下文或指令，以降低成本和延遲", "com_endpoint_anthropic_temp": "範圍從 0 到 1。對於分析/多選題，使用接近 0 的溫度，對於創意和生成式任務，使用接近 1 的溫度。我們建議修改這個或 Top P，但不建議兩者都修改。", "com_endpoint_anthropic_topk": "Top-k 改變模型選擇輸出 token 的方式。Top-k 為 1 表示所選 token 在模型詞彙表中所有 token 中最可能（也稱為貪婪解碼），而 Top-k 為 3 表示下一個 token 從最可能的 3 個 token 中選擇（使用溫度）。", "com_endpoint_anthropic_topp": "Top-p 改變模型選擇輸出 token 的方式。從最可能的 K（見 topK 參數）開始選擇 token，直到它們的機率之和達到 top-p 值。", "com_endpoint_assistant": "助理", "com_endpoint_assistant_model": "AI 模型", "com_endpoint_assistant_placeholder": "請從右側面板選擇一位助理", "com_endpoint_completion": "完成", "com_endpoint_completion_model": "完成模型（建議：GPT-4）", "com_endpoint_config_click_here": "點此", "com_endpoint_config_google_api_info": "要取得 Generative Language API 金鑰（適用於 Gemini），", "com_endpoint_config_google_api_key": "Google API 金鑰", "com_endpoint_config_google_cloud_platform": "Google 雲端平臺設定", "com_endpoint_config_google_gemini_api": "Google Gemini API 設定", "com_endpoint_config_google_service_key": "Google 服務帳戶金鑰", "com_endpoint_config_key": "設定 API 金鑰", "com_endpoint_config_key_encryption": "您的金鑰將被加密並在此到期時間刪除：", "com_endpoint_config_key_for": "設定 API 金鑰給", "com_endpoint_config_key_google_need_to": "您需要", "com_endpoint_config_key_google_service_account": "建立一個服務帳戶", "com_endpoint_config_key_google_vertex_ai": "在 Google Cloud 上啟用 Vertex AI", "com_endpoint_config_key_google_vertex_api": "API，然後", "com_endpoint_config_key_google_vertex_api_role": "確保點選「建立並繼續」並至少給予「Vertex AI 使用者」角色。最後，建立一個 JSON 金鑰以在此處匯入。", "com_endpoint_config_key_import_json_key": "匯入服務帳戶 JSON 金鑰。", "com_endpoint_config_key_import_json_key_invalid": "無效的服務帳戶 JSON 金鑰，您是否匯入了正確的檔案？", "com_endpoint_config_key_import_json_key_success": "成功匯入服務帳戶 JSON 金鑰", "com_endpoint_config_key_name": "金鑰", "com_endpoint_config_key_never_expires": "您的金鑰永遠不會過期", "com_endpoint_config_placeholder": "在標頭選單中設定您的金鑰以開始對話。", "com_endpoint_config_value": "輸入", "com_endpoint_context": "前後文", "com_endpoint_context_info": "可用於上下文的最大 token 數量。用於控制每個請求傳送的 token 數量。如果未指定，將根據已知模型的上下文大小使用系統預設值。設定較高的值可能會導致錯誤和/或更高的 token 成本。", "com_endpoint_context_tokens": "最大前後文 token 數", "com_endpoint_custom_name": "自訂名稱", "com_endpoint_default": "預設", "com_endpoint_default_blank": "預設：空白", "com_endpoint_default_empty": "預設：空", "com_endpoint_default_with_num": "預設：{{0}}", "com_endpoint_examples": "預設設定", "com_endpoint_export": "匯出", "com_endpoint_export_share": "匯出/分享", "com_endpoint_frequency_penalty": "頻率懲罰", "com_endpoint_func_hover": "啟用將外掛用作 OpenAI 函式", "com_endpoint_google_custom_name_placeholder": "為 Google 設定自訂名稱", "com_endpoint_google_maxoutputtokens": "設定回應中可生成的最大 token 數。若希望回應簡短，請設定較低的數值；若需較長的回應，則設定較高的數值。", "com_endpoint_google_temp": "較高的值表示更隨機，而較低的值表示更集中和確定。我們建議修改這個或 Top P，但不建議兩者都修改。", "com_endpoint_google_topk": "Top-k 調整模型如何選取輸出的 token。當 Top-k 設為 1 時，模型會選取在其詞彙庫中機率最高的 token 進行輸出（這也被稱為貪婪解碼）。相對地，當 Top-k 設為 3 時，模型會從機率最高的三個 token 中選取下一個輸出 token（這會涉及到所謂的「溫度」調整）", "com_endpoint_google_topp": "Top-p 調整模型在輸出 token 時的選擇機制。從最可能的 K（見 topK 參數）開始選擇 token，直到它們的機率之和達到 top-p 值。", "com_endpoint_instructions_assistants": "覆寫提示指令", "com_endpoint_instructions_assistants_placeholder": "覆寫助理的提示指令。這對於在每次執行時修改行為很有用。", "com_endpoint_max_output_tokens": "最大輸出 token 數", "com_endpoint_message": "訊息", "com_endpoint_message_new": "訊息 {{0}}", "com_endpoint_message_not_appendable": "無法附加訊息或重新生成。", "com_endpoint_my_preset": "我的預設設定", "com_endpoint_no_presets": "尚無預設設定", "com_endpoint_open_menu": "開啟選單", "com_endpoint_openai_custom_name_placeholder": "為 ChatGPT 設定自訂名稱", "com_endpoint_openai_detail": "「低」解析度的視覺請求較便宜且快速，「高」解析度則更詳細但成本較高，而「自動」會根據影像解析度自動在兩者之間選擇。", "com_endpoint_openai_freq": "數值範圍介於 -2.0 和 2.0 之間。正值會根據該 token 在目前的文字中出現的頻率進行懲罰，減少模型產生重複內容的可能性。", "com_endpoint_openai_max": "要生成的最大 token 數。輸入 token 和生成 token 的總長度受到模型前後文長度的限制。", "com_endpoint_openai_max_tokens": "可選的 `max_tokens` 欄位，代表在對話完成中可以生成的最大 token 數。\n\n輸入 token 和生成 token 的總長度受限於模型的上下文長度。如果此數字超過最大上下文 token 數，您可能會遇到錯誤。", "com_endpoint_openai_pres": "數值範圍介於 -2.0 和 2.0 之間。正值會根據該 token 是否在目前的文字中出現來進行懲罰，增加模型談及新主題的可能性。", "com_endpoint_openai_prompt_prefix_placeholder": "在系統訊息中設定自訂提示。", "com_endpoint_openai_resend": "重新傳送之前所有附加的圖片。注意：這可能會大幅增加 token 成本，如果附加了太多圖片，您可能會遇到錯誤。", "com_endpoint_openai_resend_files": "重新傳送之前附加的所有檔案。注意：這將增加 token 成本，如果附件過多，您可能會遇到錯誤。", "com_endpoint_openai_stop": "最多 4 個序列，API 將在生成更多 token 時停止。", "com_endpoint_openai_temp": "較高的值表示更隨機，而較低的值表示更集中和確定。我們建議修改這個或 Top P，但不建議兩者都修改。", "com_endpoint_openai_topp": "與溫度取樣的替代方法，稱為核心取樣，其中模型考慮 top_p 機率質量的 token 結果。所以 0.1 表示只考慮佔 top 10% 機率質量的 token。我們建議修改這個或溫度，但不建議兩者都修改。", "com_endpoint_output": "輸出", "com_endpoint_plug_image_detail": "影像詳細資訊", "com_endpoint_plug_resend_files": "重新傳送檔案", "com_endpoint_plug_set_custom_instructions_for_gpt_placeholder": "在系統訊息中新增自訂提示。", "com_endpoint_plug_skip_completion": "跳過完成步驟", "com_endpoint_plug_use_functions": "使用外掛作為 OpenAI 函式", "com_endpoint_presence_penalty": "出現懲罰", "com_endpoint_preset": "預設設定", "com_endpoint_preset_default": "現在是預設的預設設定。", "com_endpoint_preset_default_item": "預設值", "com_endpoint_preset_default_none": "無啟用的預設設定。", "com_endpoint_preset_default_removed": "不再是預設設定", "com_endpoint_preset_delete_confirm": "您確定要刪除這個預設設定嗎？", "com_endpoint_preset_delete_error": "刪除您的預設設定時發生錯誤。請重試。", "com_endpoint_preset_import": "預設設定已匯入！", "com_endpoint_preset_import_error": "匯入您的預設設定時發生錯誤。請再試一次。", "com_endpoint_preset_name": "名稱", "com_endpoint_preset_save_error": "儲存您的預設設定時發生錯誤。請再試一次。", "com_endpoint_preset_selected": "已選擇預設設定！", "com_endpoint_preset_selected_title": "已選取！", "com_endpoint_preset_title": "預設項目", "com_endpoint_presets": "預設設定", "com_endpoint_presets_clear_warning": "您確定要清除所有預設設定嗎？此操作無法復原。", "com_endpoint_prompt_cache": "使用提示快取", "com_endpoint_prompt_prefix": "提示起始字串", "com_endpoint_prompt_prefix_assistants": "提示字首", "com_endpoint_prompt_prefix_assistants_placeholder": "在助理的主要提示指令之上設定額外的提示指令或上下文。如果為空白，則會被忽略。", "com_endpoint_prompt_prefix_placeholder": "設定自訂提示或前後文。如果為空則忽略。", "com_endpoint_save_as_preset": "另存為預設設定", "com_endpoint_search": "依名稱搜尋選項", "com_endpoint_set_custom_name": "設定自訂名稱，以便您找到此預設設定", "com_endpoint_skip_hover": "啟用跳過完成步驟，評估最終答案和生成步驟", "com_endpoint_stop": "停止序列", "com_endpoint_stop_placeholder": "以 `Enter` 鍵分隔值", "com_endpoint_temperature": "溫度", "com_endpoint_top_k": "Top K", "com_endpoint_top_p": "Top P", "com_endpoint_use_active_assistant": "使用活躍助理", "com_error_expired_user_key": "提供給 {{0}} 的金鑰已於 {{1}} 到期。請提供一個新的金鑰並重試。", "com_error_files_dupe": "偵測到重複的檔案。", "com_error_files_empty": "不允許空白檔案。", "com_error_files_process": "處理檔案時發生錯誤。", "com_error_files_unsupported_capability": "未啟用支援此檔案類型的功能。", "com_error_files_upload": "上傳檔案時發生錯誤", "com_error_files_upload_canceled": "檔案上傳請求已取消。注意：檔案上傳可能仍在處理中，需要手動刪除。", "com_error_files_validation": "驗證檔案時發生錯誤。", "com_error_input_length": "最新訊息的字元數過長，已超過字元限制（{{0}}）。請縮短您的訊息內容、在對話參數中調整最大上下文大小，或是建立分支對話以繼續。", "com_error_invalid_user_key": "提供的金鑰無效。請提供有效的金鑰並重試。", "com_error_moderation": "您所提交的內容似乎被我們的內容審查系統標記為不符合社群準則。我們無法就此特定主題繼續進行討論。如果您有任何其他問題或想要探討的主題，請編輯您的訊息或開啟新的對話。", "com_error_no_base_url": "找不到基礎 URL。請提供一個基礎 URL 後再試一次。", "com_error_no_user_key": "找不到金鑰，請提供金鑰後再試一次。", "com_files_filter": "篩選檔案...", "com_files_no_results": "沒有結果。", "com_files_number_selected": "已選取 {{0}} 個檔案，共 {{1}} 個檔案", "com_generated_files": "已生成的檔案：", "com_hide_examples": "隱藏範例", "com_info_heic_converting": "正在將 HEIC 圖片轉換為 JPEG...", "com_nav_2fa": "雙因子認證 (2FA)", "com_nav_account_settings": "帳號設定", "com_nav_always_make_prod": "永遠將新版本設為正式版", "com_nav_archive_created_at": "建立時間", "com_nav_archive_name": "名稱", "com_nav_archived_chats": "封存的對話", "com_nav_at_command": "@-指令", "com_nav_at_command_description": "使用「@」指令切換端點、模型和預設值等", "com_nav_audio_play_error": "播放音訊時發生錯誤：{{0}}", "com_nav_audio_process_error": "處理音訊時發生錯誤：{{0}}", "com_nav_auto_scroll": "開啟時自動捲動至最新內容", "com_nav_auto_send_prompts": "自動傳送提示", "com_nav_auto_send_text": "自動傳送訊息", "com_nav_auto_send_text_disabled": "設定為 -1 以停用", "com_nav_auto_transcribe_audio": "自動轉錄語音", "com_nav_automatic_playback": "自動播放最新訊息", "com_nav_balance": "餘額", "com_nav_browser": "瀏覽器", "com_nav_center_chat_input": "在歡迎畫面置中顯示聊天輸入框", "com_nav_change_picture": "更換圖片", "com_nav_chat_commands": "對話指令", "com_nav_chat_commands_info": "這些指令是透過在訊息開頭輸入特定字元來啟動的。每個指令都由其專屬的前綴觸發。如果您經常在訊息開頭使用這些字元，可以選擇停用這些指令。", "com_nav_chat_direction": "對話方向", "com_nav_clear_all_chats": "清除所有對話", "com_nav_clear_cache_confirm_message": "您確定要清除快取嗎？", "com_nav_clear_conversation": "清除對話", "com_nav_clear_conversation_confirm_message": "您確定要清除所有對話嗎？此操作無法復原。", "com_nav_close_sidebar": "關閉側邊選單", "com_nav_commands": "指令", "com_nav_confirm_clear": "確認清除", "com_nav_conversation_mode": "對話模式", "com_nav_convo_menu_options": "對話選單選項", "com_nav_db_sensitivity": "分貝靈敏度", "com_nav_delete_account": "刪除帳號", "com_nav_delete_account_button": "永久刪除我的帳號", "com_nav_delete_account_confirm": "確定要刪除帳號嗎？", "com_nav_delete_account_email_placeholder": "請輸入您的帳號電子郵件", "com_nav_delete_cache_storage": "刪除語音快取儲存空間", "com_nav_delete_data_info": "所有資料都將被刪除。", "com_nav_delete_warning": "警告：此操作將永久刪除您的帳號。", "com_nav_enable_cache_tts": "啟用語音快取", "com_nav_enable_cloud_browser_voice": "使用雲端語音", "com_nav_enabled": "已啟用", "com_nav_engine": "引擎", "com_nav_enter_to_send": "按 Enter 鍵傳送訊息", "com_nav_export": "匯出", "com_nav_export_all_message_branches": "匯出所有訊息分支", "com_nav_export_conversation": "匯出對話", "com_nav_export_filename": "檔名", "com_nav_export_filename_placeholder": "設定檔案名稱", "com_nav_export_include_endpoint_options": "包含 AI 選項", "com_nav_export_recursive": "遞迴", "com_nav_export_recursive_or_sequential": "遞迴還是序列？", "com_nav_export_type": "類型", "com_nav_external": "外部", "com_nav_font_size": "字體大小", "com_nav_font_size_base": "中", "com_nav_font_size_lg": "大", "com_nav_font_size_sm": "小", "com_nav_font_size_xl": "特大", "com_nav_font_size_xs": "超小", "com_nav_help_faq": "說明與常見問題", "com_nav_hide_panel": "隱藏最右側的面板", "com_nav_info_code_artifacts": "啟用在對話旁顯示實驗性程式碼內容", "com_nav_info_custom_prompt_mode": "啟用後，系統將不會包含預設的成品提示詞。在此模式下，所有生成成品的指令都需要手動提供。", "com_nav_info_enter_to_send": "啟用時，按下 `ENTER` 鍵即可傳送訊息。停用時，按下 Enter 鍵會換行，您需要按下 `CTRL + ENTER` / `⌘ + ENTER` 來傳送訊息。", "com_nav_info_fork_change_default": "「僅顯示分支訊息」只包含通往所選訊息的直接路徑。「包含相關分支」會加入路徑上的分支。「包含所有從這裡開始」則包含所有相連的訊息和分支。", "com_nav_info_fork_split_target_setting": "啟用時，系統將根據所選的行為，從目標訊息開始分支到對話中的最新訊息。", "com_nav_info_include_shadcnui": "啟用後，將包含使用 shadcn/ui 元件的相關說明。shadcn/ui 是一個使用 Radix UI 和 Tailwind CSS 建置的可重複使用元件集。注意：這些說明較為冗長，建議僅在需要告知 LLM 正確的匯入方式和元件使用方法時才啟用。若要了解這些元件的更多資訊，請造訪：https://ui.shadcn.com/", "com_nav_info_latex_parsing": "啟用時，訊息中的 LaTeX 程式碼將會被轉換為數學方程式。如果您不需要 LaTeX 轉換功能，停用此選項可以改善效能。", "com_nav_info_save_badges_state": "啟用此功能後，聊天徽章的狀態將會被儲存。這表示當您建立新的對話時，徽章會維持和上一個對話相同的狀態。如果停用此選項，每次建立新的對話時，徽章都會重設為預設狀態。", "com_nav_info_save_draft": "啟用後，您在聊天表單中輸入的文字和附件將自動儲存為本地草稿。即使重新載入頁面或切換至其他對話，這些草稿仍會保留。草稿僅儲存在您的裝置上，並會在訊息送出後自動刪除。", "com_nav_info_user_name_display": "啟用時，每則您發送的訊息上方都會顯示您的使用者名稱。停用時，您的訊息上方只會顯示「您」。", "com_nav_lang_arabic": "العربية", "com_nav_lang_auto": "自動偵測", "com_nav_lang_brazilian_portuguese": "Portuguê<PERSON>", "com_nav_lang_chinese": "中文", "com_nav_lang_dutch": "Nederlands", "com_nav_lang_english": "English", "com_nav_lang_estonian": "<PERSON><PERSON><PERSON> keel", "com_nav_lang_finnish": "<PERSON><PERSON>", "com_nav_lang_french": "Français ", "com_nav_lang_georgian": "ქართული", "com_nav_lang_german": "De<PERSON>ch", "com_nav_lang_hebrew": "עברית", "com_nav_lang_indonesia": "Indonesia", "com_nav_lang_italian": "Italiano", "com_nav_lang_japanese": "日本語", "com_nav_lang_korean": "한국어", "com_nav_lang_polish": "<PERSON><PERSON>", "com_nav_lang_portuguese": "Português", "com_nav_lang_russian": "Русский", "com_nav_lang_spanish": "Español", "com_nav_lang_swedish": "Svenska", "com_nav_lang_thai": "ไทย", "com_nav_lang_traditional_chinese": "繁體中文", "com_nav_lang_turkish": "Türkçe", "com_nav_lang_vietnamese": "Tiếng <PERSON>", "com_nav_language": "語言", "com_nav_latex_parsing": "解析訊息中的 LaTeX 內容（可能影響效能）", "com_nav_log_out": "登出", "com_nav_long_audio_warning": "較長的文字需要較多處理時間", "com_nav_maximize_chat_space": "最大化聊天視窗", "com_nav_modular_chat": "允許在對話中途切換端點", "com_nav_my_files": "我的檔案", "com_nav_not_supported": "不支援", "com_nav_open_sidebar": "開啟側邊選單", "com_nav_playback_rate": "音訊播放速率", "com_nav_plugin_auth_error": "嘗試驗證此外掛時發生錯誤。請重試。", "com_nav_plugin_install": "安裝", "com_nav_plugin_search": "搜尋外掛", "com_nav_plugin_store": "外掛商店", "com_nav_plugin_uninstall": "解除安裝", "com_nav_plus_command": "指令選項", "com_nav_plus_command_description": "切換「+」指令以新增多重回應設定", "com_nav_profile_picture": "個人頭像", "com_nav_save_badges_state": "儲存徽章狀態", "com_nav_save_drafts": "儲存本機草稿", "com_nav_scroll_button": "滾動至底部按鈕", "com_nav_search_placeholder": "搜尋訊息", "com_nav_send_message": "傳送訊息", "com_nav_setting_account": "帳號", "com_nav_setting_chat": "聊天", "com_nav_setting_data": "資料控制", "com_nav_setting_general": "一般", "com_nav_setting_mcp": "MCP 設定", "com_nav_setting_personalization": "個性化", "com_nav_setting_speech": "語音", "com_nav_settings": "設定", "com_nav_shared_links": "共享連結", "com_nav_show_code": "一律顯示使用程式碼解譯器時的程式碼", "com_nav_show_thinking": "預設展開思考過程", "com_nav_slash_command": "/指令", "com_nav_slash_command_description": "使用鍵盤按下 \"/\" 快速選擇提示詞", "com_nav_speech_to_text": "語音轉文字", "com_nav_stop_generating": "停止產生", "com_nav_text_to_speech": "文字轉語音", "com_nav_theme": "主題", "com_nav_theme_dark": "深色", "com_nav_theme_light": "淺色", "com_nav_theme_system": "跟隨系統設定", "com_nav_tool_dialog": "AI 工具", "com_nav_tool_dialog_agents": "AI 代理工具", "com_nav_tool_dialog_description": "必須儲存 Assistant 才能保留工具選擇。", "com_nav_tool_remove": "移除", "com_nav_tool_search": "搜尋工具", "com_nav_user": "使用者", "com_nav_user_msg_markdown": "以 Markdown 格式呈現使用者訊息", "com_nav_user_name_display": "在訊息中顯示使用者名稱", "com_nav_voice_select": "語音", "com_show_agent_settings": "顯示代理設定", "com_show_completion_settings": "顯示完成設定", "com_show_examples": "顯示範例", "com_sidepanel_agent_builder": "代理建構器", "com_sidepanel_assistant_builder": "助理建構器", "com_sidepanel_attach_files": "附加檔案", "com_sidepanel_conversation_tags": "書籤", "com_sidepanel_hide_panel": "隱藏側邊選單", "com_sidepanel_manage_files": "管理檔案", "com_sidepanel_parameters": "參數", "com_sources_image_alt": "搜尋結果圖片", "com_sources_more_sources": "+{{count}} 個來源", "com_sources_tab_all": "全部", "com_sources_tab_images": "圖片", "com_sources_tab_news": "新聞", "com_sources_title": "來源", "com_ui_2fa_account_security": "雙因子驗證為您的帳戶增添一層額外的安全保護", "com_ui_2fa_disable": "停用雙因子驗證", "com_ui_2fa_disable_error": "停用雙因子驗證時發生錯誤", "com_ui_2fa_disabled": "已停用雙因子驗證", "com_ui_2fa_enable": "啟用雙因子驗證", "com_ui_2fa_enabled": "已啟用雙因子驗證", "com_ui_2fa_generate_error": "產生雙因子驗證設定時發生錯誤", "com_ui_2fa_invalid": "雙因子驗證碼無效", "com_ui_2fa_setup": "設定雙因子驗證", "com_ui_2fa_verified": "雙因子驗證成功", "com_ui_accept": "我接受", "com_ui_add": "新增", "com_ui_add_model_preset": "新增模型或預設設定以取得額外回應", "com_ui_add_multi_conversation": "新增多重對話", "com_ui_admin": "管理員", "com_ui_admin_access_warning": "停用管理員對此功能的存取權限可能會導致意外的介面問題，需要重新整理頁面。若儲存此設定，唯一的還原方式是透過 librechat.yaml 設定檔中的介面設定，這會影響所有角色。", "com_ui_admin_settings": "管理員設定", "com_ui_advanced": "進階", "com_ui_agent": "助理", "com_ui_agent_delete_error": "刪除助理時發生錯誤", "com_ui_agent_deleted": "已成功刪除助理", "com_ui_agent_duplicate_error": "複製助理時發生錯誤", "com_ui_agent_duplicated": "已成功複製助理", "com_ui_agent_editing_allowed": "其他使用者已可編輯此助理", "com_ui_agents": "助理", "com_ui_agents_allow_create": "允許建立代理", "com_ui_agents_allow_share_global": "允許與所有使用者共享助理", "com_ui_agents_allow_use": "允許使用代理", "com_ui_all": "全部", "com_ui_all_proper": "全部", "com_ui_archive": "封存", "com_ui_archive_error": "封存對話時發生錯誤", "com_ui_artifact_click": "點擊開啟", "com_ui_artifacts": "成品", "com_ui_artifacts_toggle": "切換成品介面", "com_ui_ascending": "遞增", "com_ui_assistant": "助理", "com_ui_assistant_delete_error": "刪除助理時發生錯誤", "com_ui_assistant_deleted": "已成功刪除助理", "com_ui_assistants": "助理", "com_ui_assistants_output": "助理輸出", "com_ui_attach_error": "無法附加檔案。請建立或選擇對話，或嘗試重新整理頁面。", "com_ui_attach_error_openai": "無法將助理檔案附加至其他端點", "com_ui_attach_error_size": "檔案大小超過端點的限制", "com_ui_attach_error_type": "不支援的檔案類型，無法上傳至端點：", "com_ui_attach_warn_endpoint": "非相容工具的非助理檔案可能會被忽略", "com_ui_attachment": "附件", "com_ui_authentication": "驗證", "com_ui_avatar": "大頭照", "com_ui_back_to_chat": "返回對話", "com_ui_back_to_prompts": "返回提示", "com_ui_bookmark_delete_confirm": "你確定要刪除這個書籤嗎？", "com_ui_bookmarks": "書籤", "com_ui_bookmarks_add": "新增書籤", "com_ui_bookmarks_add_to_conversation": "添加到當前對話", "com_ui_bookmarks_count": "計數", "com_ui_bookmarks_create_error": "創建書籤時出錯", "com_ui_bookmarks_create_exists": "此書籤已存在", "com_ui_bookmarks_create_success": "書籤創建成功", "com_ui_bookmarks_delete": "刪除書籤", "com_ui_bookmarks_delete_error": "刪除書籤時出錯", "com_ui_bookmarks_delete_success": "書籤刪除成功", "com_ui_bookmarks_description": "描述", "com_ui_bookmarks_edit": "編輯書籤", "com_ui_bookmarks_filter": "搜尋書籤...", "com_ui_bookmarks_new": "新書籤", "com_ui_bookmarks_title": "標題", "com_ui_bookmarks_update_error": "更新書籤時出錯", "com_ui_bookmarks_update_success": "書籤更新成功", "com_ui_cancel": "取消", "com_ui_chat": "對話", "com_ui_chat_history": "對話紀錄", "com_ui_clear": "清除", "com_ui_clear_all": "清除全部", "com_ui_close": "關閉", "com_ui_code": "程式碼", "com_ui_collapse_chat": "收合對話", "com_ui_command_placeholder": "選填：輸入指令，若未填寫將使用名稱", "com_ui_command_usage_placeholder": "透過指令或名稱選擇提示", "com_ui_confirm_action": "確認操作", "com_ui_context": "情境", "com_ui_continue": "繼續", "com_ui_controls": "控制項", "com_ui_copied": "已複製！", "com_ui_copied_to_clipboard": "已複製到剪貼簿", "com_ui_copy_code": "複製程式碼", "com_ui_copy_link": "複製連結", "com_ui_copy_to_clipboard": "複製到剪貼簿", "com_ui_create": "建立", "com_ui_create_link": "建立連結", "com_ui_create_memory": "建立記憶", "com_ui_create_prompt": "建立提示", "com_ui_custom_prompt_mode": "自訂提示模式", "com_ui_dashboard": "儀表板", "com_ui_date": "日期", "com_ui_date_april": "四月", "com_ui_date_august": "八月", "com_ui_date_december": "十二月", "com_ui_date_february": "二月", "com_ui_date_january": "一月", "com_ui_date_july": "七月", "com_ui_date_june": "六月", "com_ui_date_march": "三月", "com_ui_date_may": "五月", "com_ui_date_november": "十一月", "com_ui_date_october": "十月", "com_ui_date_previous_30_days": "過去 30 天", "com_ui_date_previous_7_days": "過去 7 天", "com_ui_date_september": "九月", "com_ui_date_today": "今天", "com_ui_date_yesterday": "昨天", "com_ui_decline": "我不同意", "com_ui_delete": "刪除", "com_ui_delete_action": "刪除動作", "com_ui_delete_action_confirm": "您確定要刪除這個操作嗎？", "com_ui_delete_agent_confirm": "您確定要刪除這個代理嗎？", "com_ui_delete_assistant_confirm": "您確定要刪除這個助理嗎？此操作無法復原。", "com_ui_delete_confirm": "這將刪除", "com_ui_delete_confirm_prompt_version_var": "這將刪除「{{0}}」的所選版本。如果沒有其他版本存在，該提示將被刪除。", "com_ui_delete_conversation": "刪除對話？", "com_ui_delete_prompt": "刪除提示？", "com_ui_delete_shared_link": "刪除共享連結？", "com_ui_delete_tool": "刪除工具", "com_ui_delete_tool_confirm": "您確定要刪除這個工具嗎？", "com_ui_descending": "遞減", "com_ui_description": "描述", "com_ui_description_placeholder": "選填：輸入要顯示的提示描述", "com_ui_download_error": "下載檔案時發生錯誤。該檔案可能已被刪除。", "com_ui_dropdown_variables": "下拉式變數：", "com_ui_dropdown_variables_info": "為您的提示建立自訂下拉選單：`{{variable_name:選項1|選項2|選項3}}`", "com_ui_duplicate": "複製", "com_ui_duplication_error": "複製對話時發生錯誤", "com_ui_duplication_processing": "正在複製對話...", "com_ui_duplication_success": "已成功複製對話", "com_ui_edit": "編輯", "com_ui_endpoint": "端點", "com_ui_endpoint_menu": "語言模型端點選單", "com_ui_enter": "輸入", "com_ui_enter_api_key": "輸入 API 金鑰", "com_ui_enter_openapi_schema": "在此輸入您的 OpenAPI 結構描述", "com_ui_error": "錯誤", "com_ui_error_connection": "連線至伺服器時發生錯誤，請重新整理頁面。", "com_ui_error_save_admin_settings": "儲存管理員設定時發生錯誤", "com_ui_examples": "範例", "com_ui_export_convo_modal": "匯出對話視窗", "com_ui_field_required": "此欄位為必填", "com_ui_filter_prompts_name": "依名稱篩選提示", "com_ui_fork": "分支", "com_ui_fork_all_target": "包含所有從這裡開始", "com_ui_fork_branches": "包含相關分支", "com_ui_fork_change_default": "預設分支選項", "com_ui_fork_default": "使用預設分支選項", "com_ui_fork_error": "分支對話時發生錯誤", "com_ui_fork_from_message": "選擇分支選項", "com_ui_fork_info_1": "使用此設定來分支訊息，以獲得所需的行為。", "com_ui_fork_info_2": "「分支」是指從目前對話中的特定訊息開始/結束，根據所選的選項建立新對話的副本。", "com_ui_fork_info_3": "「目標訊息」指的是此彈出視窗所開啟的訊息，或者如果您勾選「{{0}}」，則是對話中最新的訊息。", "com_ui_fork_info_branches": "此選項會分叉可見的訊息，以及相關的分支；換句話說，它包含了通往目標訊息的直接路徑，包括路徑上的所有分支。", "com_ui_fork_info_remember": "勾選此項目可記住您選擇的選項，以便日後分支對話時更快速地套用您偏好的設定。", "com_ui_fork_info_start": "如果勾選，則從此訊息開始，根據上方選擇的行為，將會分支出一個新的對話直到最新的訊息。", "com_ui_fork_info_target": "這個選項會分叉所有導向目標訊息的訊息，包括其鄰近訊息；換句話說，不論是否可見或在同一路徑上，所有訊息分支都會包含在內。", "com_ui_fork_info_visible": "此選項只會分支顯示的訊息，換句話說，只會顯示直接通往目標訊息的路徑，而不會顯示任何分支。", "com_ui_fork_processing": "分支對話中...", "com_ui_fork_remember": "記住", "com_ui_fork_remember_checked": "您的選擇將在使用後被記住。您可以隨時在設定中更改。", "com_ui_fork_split_target": "在此分叉", "com_ui_fork_split_target_setting": "預設從目標訊息開始分支", "com_ui_fork_success": "已成功分支對話", "com_ui_fork_visible": "僅顯示分支訊息", "com_ui_generate_qrcode": "產生 QR 碼", "com_ui_generating": "產生中...", "com_ui_go_to_conversation": "前往對話", "com_ui_happy_birthday": "這是我的第一個生日！", "com_ui_host": "主機", "com_ui_image_gen": "影像生成", "com_ui_import_conversation_error": "匯入對話時發生錯誤", "com_ui_import_conversation_file_type_error": "不支援的匯入檔案類型", "com_ui_import_conversation_info": "從 JSON 文件匯入對話", "com_ui_import_conversation_success": "對話匯入成功", "com_ui_include_shadcnui": "包含 shadcn/ui 元件說明", "com_ui_input": "輸入", "com_ui_instructions": "說明", "com_ui_latest_footer": "讓每個人都能使用 AI", "com_ui_librechat_code_api_key": "取得你的 LibreChat 程式碼解譯器 API 金鑰", "com_ui_librechat_code_api_subtitle": "安全性高。多語言支援。檔案輸入/輸出。", "com_ui_librechat_code_api_title": "執行 AI 程式碼", "com_ui_locked": "已鎖定", "com_ui_logo": "{{0}} 標誌", "com_ui_manage": "管理", "com_ui_max_tags": "允許的最大數量為 {{0}}，已使用最新值。", "com_ui_mcp_dialog_desc": "請在下方輸入必要資訊。", "com_ui_mcp_enter_var": "請輸入 {{0}} 的值", "com_ui_mcp_server_not_found": "找不到伺服器。", "com_ui_mcp_url": "MCP 伺服器", "com_ui_memories": "記憶", "com_ui_memories_allow_create": "允許建立記憶", "com_ui_memories_allow_opt_out": "允許用戶選擇不使用記憶功能", "com_ui_memories_allow_read": "允許讀取記憶", "com_ui_memories_allow_update": "允許更新記憶", "com_ui_memories_allow_use": "允許使用記憶", "com_ui_memories_filter": "篩選記憶...", "com_ui_memory_created": "記憶建立成功", "com_ui_memory_deleted": "記憶已刪除", "com_ui_memory_deleted_items": "已刪除的記憶", "com_ui_memory_key_exists": "已存在具有此鍵值的記憶。請使用不同的鍵值。", "com_ui_memory_updated": "已更新儲存的記憶", "com_ui_memory_updated_items": "已更新的記憶", "com_ui_mention": "提及端點、助理或預設設定以快速切換", "com_ui_min_tags": "無法再移除更多值，至少需要 {{0}} 個。", "com_ui_model": "模型", "com_ui_model_parameters": "模型參數", "com_ui_more_info": "更多資訊", "com_ui_my_prompts": "我的提示", "com_ui_name": "名稱", "com_ui_new_chat": "新對話", "com_ui_next": "下一個", "com_ui_no": "否", "com_ui_no_bookmarks": "看來您還沒有任何書籤。請點選對話並新增一個書籤", "com_ui_no_category": "無分類", "com_ui_no_changes": "沒有需要更新的變更", "com_ui_no_terms_content": "沒有條款和條件內容顯示", "com_ui_nothing_found": "找不到任何內容", "com_ui_of": "的", "com_ui_off": "關閉", "com_ui_on": "開啟", "com_ui_page": "頁面", "com_ui_prev": "上一個", "com_ui_preview": "預覽", "com_ui_privacy_policy": "隱私權政策", "com_ui_privacy_policy_url": "隱私權政策網址", "com_ui_prompt": "提示", "com_ui_prompt_already_shared_to_all": "此提示已與所有使用者共享", "com_ui_prompt_name": "提示名稱", "com_ui_prompt_name_required": "提示名稱為必填", "com_ui_prompt_preview_not_shared": "作者尚未開放此提示的協作權限。", "com_ui_prompt_text": "文字", "com_ui_prompt_text_required": "必須輸入文字", "com_ui_prompt_update_error": "更新提示時發生錯誤", "com_ui_prompts": "提示", "com_ui_prompts_allow_create": "允許建立提示", "com_ui_prompts_allow_share_global": "允許與所有使用者共享提示詞", "com_ui_prompts_allow_use": "允許使用提示", "com_ui_provider": "提供者", "com_ui_read_aloud": "朗讀", "com_ui_redirecting_to_provider": "正在重新導向至 {{0}}，請稍候...", "com_ui_reference_saved_memories": "參考儲存的記憶", "com_ui_reference_saved_memories_description": "允許助理在回應時使用記憶", "com_ui_regenerate": "重新生成", "com_ui_region": "地區", "com_ui_rename": "重新命名", "com_ui_reset_var": "重設 {{0}}", "com_ui_result": "結果", "com_ui_revoke": "撤銷", "com_ui_revoke_info": "撤銷所有使用者提供的憑證。", "com_ui_revoke_key_confirm": "您確定要撤銷這個金鑰嗎？", "com_ui_revoke_key_endpoint": "撤銷 {{0}} 的金鑰", "com_ui_revoke_keys": "撤銷金鑰", "com_ui_revoke_keys_confirm": "您確定要撤銷所有金鑰嗎？", "com_ui_role_select": "角色", "com_ui_run_code": "執行程式碼", "com_ui_run_code_error": "執行程式碼時發生錯誤", "com_ui_save": "儲存", "com_ui_save_submit": "儲存並送出", "com_ui_saved": "已儲存！", "com_ui_schema": "綱要", "com_ui_select": "選擇", "com_ui_select_file": "選擇檔案", "com_ui_select_model": "選擇模型", "com_ui_select_provider": "選擇供應商", "com_ui_select_provider_first": "請先選擇提供者", "com_ui_select_region": "選擇地區", "com_ui_select_search_model": "依名稱搜尋模型", "com_ui_select_search_plugin": "依名稱搜尋外掛程式", "com_ui_select_search_provider": "依名稱搜尋供應商", "com_ui_select_search_region": "依名稱搜尋區域", "com_ui_share": "分享", "com_ui_share_create_message": "您的姓名以及您在共享後新增的任何訊息都會保密。", "com_ui_share_delete_error": "刪除共享連結時發生錯誤。", "com_ui_share_error": "分享聊天連結時發生錯誤", "com_ui_share_link_to_chat": "分享連結到聊天", "com_ui_share_to_all_users": "分享給所有使用者", "com_ui_share_update_message": "您的姓名、自訂提示指令以及您在共享後新增的任何訊息都會保密。", "com_ui_share_var": "分享{{0}}", "com_ui_shared_link_not_found": "未找到共享連結", "com_ui_shared_prompts": "共享提示", "com_ui_show_all": "顯示全部", "com_ui_simple": "簡單", "com_ui_size": "大小", "com_ui_special_variables": "特殊變數：", "com_ui_speech_while_submitting": "正在產生回應時無法送出語音", "com_ui_stop": "停止", "com_ui_storage": "儲存空間", "com_ui_submit": "送出", "com_ui_terms_and_conditions": "條款和條件", "com_ui_terms_of_service": "服務條款", "com_ui_tools": "工具", "com_ui_unarchive": "取消封存", "com_ui_unarchive_error": "取消封存對話時發生錯誤", "com_ui_unknown": "未知", "com_ui_update": "更新", "com_ui_upload": "上傳", "com_ui_upload_code_files": "上傳程式碼解譯器檔案", "com_ui_upload_delay": "正在上傳 \"{{0}}\" 的過程比預期花費更多時間。請耐心等候檔案完成索引以供檢索。", "com_ui_upload_error": "檔案上傳時發生錯誤", "com_ui_upload_file_search": "上傳檔案以供搜尋", "com_ui_upload_files": "上傳檔案", "com_ui_upload_image": "上傳圖片", "com_ui_upload_image_input": "上傳圖片", "com_ui_upload_invalid": "上傳的檔案無效。必須是不超過限制的圖片檔", "com_ui_upload_invalid_var": "上傳的檔案無效。必須是不超過 {{0}} MB 的圖片檔案", "com_ui_upload_success": "檔案上傳成功", "com_ui_upload_type": "選擇上傳類型", "com_ui_usage": "使用率", "com_ui_use_memory": "使用記憶", "com_ui_use_micrphone": "使用麥克風", "com_ui_variables": "變數", "com_ui_variables_info": "在文字中使用雙大括號來建立變數，例如 `{{example variable}}`，以便在使用提示時填入。", "com_ui_version_var": "版本 {{0}}", "com_ui_versions": "版本", "com_ui_yes": "是", "com_ui_zoom": "縮放", "com_user_message": "您"}