import { useCallback } from 'react';
import { useNavigate } from 'react-router-dom';

import { useAuthContext } from './useAuthContext';

/**
 * Hook for handling user logout with navigation
 * @returns A logout function that accepts an optional redirect path
 */
export const useLogout = () => {
  const { logout } = useAuthContext();
  const navigate = useNavigate();

  return useCallback(
    (redirect?: string) => {
      return logout(navigate, redirect);
    },
    [logout, navigate],
  );
};