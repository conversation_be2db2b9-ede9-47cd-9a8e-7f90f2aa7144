import { useContext } from 'react';

import { AuthContext } from '~/Providers';

/**
 * Hook to access authentication context
 * @throws {Error} If used outside of AuthContextProvider
 */
export const useAuthContext = () => {
  const context = useContext(AuthContext);

  if (context === undefined) {
    throw new Error('useAuthContext should be used inside AuthContextProvider');
  }

  return context;
};