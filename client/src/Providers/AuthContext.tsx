import { create<PERSON>ontext, ReactNode, useCallback, useEffect, useMemo, useState } from 'react';
import { useRecoilState } from 'recoil';

import type * as t from 'librechat-data-provider';
import { SystemRoles } from 'librechat-data-provider';
import { TAuthConfig } from '~/common';
import { useGetRole } from '~/data-provider';
import store from '~/store';
import { authService, createUserDataFromToken, hasKeycloakCallbackParams } from '~/utils/virava';

/**
 * Authentication context type definition
 */
interface AuthContextType {
  user: t.TUser | undefined;
  token: string | undefined;
  error: string | undefined;
  onLogin: () => Promise<void>;
  logout: (navigateFn: (path: string) => void, redirect?: string) => Promise<void>;
  setError: (error: string | undefined) => void;
  roles: {
    [SystemRoles.USER]: unknown;
  };
  isAuthenticated: boolean;
  getAccessToken: () => string | null;
  username: string | null;
  setUsername: (value: string | null) => void;
  isProcessingCallback: boolean;
}

interface AuthContextProviderProps {
  authConfig?: TAuthConfig;
  children: ReactNode;
}

const extractAndSetUserData = (
  setUsername: (value: string | null) => void,
  setToken: (value: string | undefined) => void,
  setUser: (user: t.TUser | undefined) => void,
  shouldSetUser: boolean = true,
): void => {
  try {
    const userToken = authService.getAccessToken();
    setUsername(userToken?.preferred_username || userToken?.email || null);
    setToken(authService.getAccessTokenRaw());

    if (userToken && shouldSetUser) {
      const userData = createUserDataFromToken(userToken);
      setUser(userData);
    }
  } catch (_error) {
    // Silent error handling for token extraction
  }
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const AuthContextProvider = ({ children }: AuthContextProviderProps) => {
  const [user, setUser] = useRecoilState(store.user);
  const [isAuthenticated, setIsAuthenticated] = useState(() => {
    const authenticated = authService.isAuthenticated();
    return authenticated;
  });
  const [username, setUsername] = useState<string | null>(null);
  const [token, setToken] = useState<string | undefined>(undefined);
  const [error, setError] = useState<string | undefined>(undefined);
  const [isProcessingCallback, setIsProcessingCallback] = useState(() => {
    // Check immediately if we have Keycloak callback params
    return hasKeycloakCallbackParams();
  });

  const { data: userRole = null } = useGetRole(SystemRoles.USER, {
    enabled: !!(isAuthenticated && user?.role === SystemRoles.USER),
  });

  /**
   * Check authentication on mount and handle Keycloak callbacks
   */
  useEffect(() => {
    const checkAuth = async () => {
      if (hasKeycloakCallbackParams()) {
        setIsProcessingCallback(true);

        try {
          // Process the callback
          await authService.login();

          // Update state after callback
          const authenticated = authService.isAuthenticated();
          setIsAuthenticated(authenticated);

          if (authenticated) {
            extractAndSetUserData(setUsername, setToken, setUser);
            // Clean URL (let HomePage handle the redirect)
            window.history.replaceState({}, document.title, '/');
          }
        } catch (_error) {
          // Silent error handling for callback processing
        } finally {
          setIsProcessingCallback(false);
        }
      } else {
        // Regular authentication check (for page refresh with existing tokens)
        const authenticated = authService.isAuthenticated();
        if (authenticated && !isAuthenticated) {
          setIsAuthenticated(true);
          extractAndSetUserData(setUsername, setToken, setUser, !user);
        }
      }
    };

    checkAuth();
  }, [isAuthenticated, user, setUser]);

  /**
   * Handles user login via Keycloak OAuth flow
   */
  const onLogin = useCallback(async () => {
    try {
      console.log('🔐 Attempting login...');

      // Check if service is properly initialized
      if (!authService.isAuthenticated && typeof authService.login !== 'function') {
        throw new Error('Authentication service is not properly initialized');
      }

      await authService.login();
      setIsAuthenticated(true);

      // Extract user data from token
      extractAndSetUserData(setUsername, setToken, setUser);
      console.log('✅ Login successful');
    } catch (error) {
      console.error('❌ Login failed:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown authentication error';
      setError(`Authentication failed: ${errorMessage}`);

      // Additional debugging information
      console.error('Auth service state:', {
        hasLogin: typeof authService.login === 'function',
        isAuthenticated: authService.isAuthenticated?.(),
        authConfig: {
          clientId: import.meta.env.VITE_CLIENT_ID,
          baseUrl: import.meta.env.VITE_BASE_URL,
          realm: import.meta.env.VITE_REALM,
        },
      });

      throw error;
    }
  }, [setUser, setError]);

  /**
   * Handles user logout and cleanup
   */
  const logout = useCallback(
    async (navigateFn: (path: string) => void, redirect?: string) => {
      await authService.logout(window.location.origin);
      setIsAuthenticated(false);
      setUsername(null);
      setToken(undefined);
      setUser(undefined);

      const redirectPath = redirect && redirect !== window.location.origin ? redirect : '/';
      navigateFn(redirectPath);
    },
    [setUser],
  );

  /**
   * Gets the current access token
   */
  const getAccessToken = useCallback(() => {
    try {
      return authService.getAccessTokenRaw();
    } catch {
      return null;
    }
  }, []);

  /**
   * User roles object
   */
  const roles = useMemo(
    () => ({
      [SystemRoles.USER]: userRole,
    }),
    [userRole],
  );

  /**
   * Context value with all authentication state and methods
   */
  const contextValue = useMemo(
    () => ({
      user,
      token,
      error,
      onLogin,
      logout,
      setError,
      roles,
      isAuthenticated,
      getAccessToken,
      username,
      setUsername,
      isProcessingCallback,
    }),
    [
      user,
      token,
      error,
      onLogin,
      logout,
      roles,
      isAuthenticated,
      username,
      isProcessingCallback,
      getAccessToken,
    ],
  );

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};

export { AuthContext };
export default AuthContextProvider;
