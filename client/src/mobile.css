.hover-button.active {
  display: block;
  visibility: visible;
}

.nav-close-button {
  margin-left: 8px;
}

.nav {
  position: fixed;
  z-index: 64;
  top: 0;

  /* max-width: 260px; */

  bottom: 0;
  opacity: 0;
}

.nav.active {
  position: relative;
  left: 0;
  opacity: 1;
}

.nav-mask.active {
  opacity: 0;
  pointer-events: auto;
}

@media (max-width: 767px) {
  .nav {
    width: calc(100% - 10px)  ;
    transition: all 0.15s;
  }
  .nav-mask {
    position: fixed;
    z-index: 63;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(7, 7, 7, 0.4);
    padding-left: 420px;
    padding-top: 12px;
    opacity: 0;
    transition: all 0.5s;
    pointer-events: none;
  }

  .nav-open-button {
    opacity: 0;
  }

  .nav-mask.active {
    opacity: 1;
    pointer-events: auto;
  }

  .nav.active {
    position: fixed;
  }
}

@media (min-width: 1024px) {
  .switch-container {
    display: none;
  }
}


  .switch-result {
    display: block !important;
    visibility: visible;
  }

@media (max-width: 1024px) {
  /* .sibling-switch {
        left: 114px;
        top: unset;
        bottom: 4px;
        visibility: visible;
        z-index: 2;
    } */
  .sibling-switch {
    display: none;
  }

  .hover-button {
    display: block;
    visibility: visible;
  }
}

@media (max-width: 767px) {
  .input-panel-button {
    border: 0;
  }

  .input-panel-button svg {
    width: 16px;
    height: 16px;
  }

  .input-panel {
  }

  .nav-open-button
  {
    visibility: hidden;
  }
}

.hide-scrollbar {
  /* Hide scrollbar for Chrome, Safari, and Opera */
  scrollbar-width: none; /* For Firefox */
  -ms-overflow-style: none; /* For Internet Explorer and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none; /* For WebKit browsers */
}

.gemini-gradient {
  /* Adjust the colors and positioning as necessary to match the image */
  background-image: radial-gradient(circle at center, #0000ff, #87cefa, #ffffff);
  /* More styling for demonstration purposes */
  border-radius: 50%;
  height: 100px;
  width: 100px;
}

@media (max-width: 767px) {
  .sidenav {
    /* width: calc(100% - 10px)  ; */
    transition: all 0.15s;
    position: fixed;
    z-index: 66;
    top: 0;
    max-width: 320px;

    /* max-width: 260px; */

    bottom: 0;
    right: 0
    /* opacity: 0; */
  }
  .sidenav-mask {
    position: fixed;
    z-index: 65;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background-color: rgba(86, 88, 105, 0.75);
    padding-left: 420px;
    padding-top: 12px;
    opacity: 0;
    transition: all 0.5s;
    pointer-events: none;
  }

  .sidenav-mask.active {
    opacity: 1;
    pointer-events: auto;
  }

  .sidenav.active {
    position: fixed;
  }
}

@keyframes tuning {
  0% { transform: rotate(30deg); }
  25% { transform: rotate(110deg); }
  50% { transform: rotate(-40deg); }
  75% { transform: rotate(80deg) }
  100% { transform: rotate(30deg) }
}

.rotate {
  animation: tuning 2.1s 1;
}

@-webkit-keyframes slideUpAndFade {
  0% {
    opacity:0;
    -webkit-transform:translateY(2px);
    transform:translateY(2px)
  }
  to {
    opacity:1;
    -webkit-transform:translateY(0);
    transform:translateY(0)
  }
}
@keyframes slideUpAndFade {
  0% {
    opacity:0;
    -webkit-transform:translateY(2px);
    transform:translateY(2px)
  }
  to {
    opacity:1;
    -webkit-transform:translateY(0);
    transform:translateY(0)
  }
}
.radix-side-bottom\:animate-slideUpAndFade[data-side=bottom] {
  -webkit-animation:slideUpAndFade .4s cubic-bezier(.16,1,.3,1);
  animation:slideUpAndFade .4s cubic-bezier(.16,1,.3,1)
}
@-webkit-keyframes slideRightAndFade {
  0% {
    opacity:0;
    -webkit-transform:translateX(-2px);
    transform:translateX(-2px)
  }
  to {
    opacity:1;
    -webkit-transform:translateX(0);
    transform:translateX(0)
  }
}
@keyframes slideRightAndFade {
  0% {
    opacity:0;
    -webkit-transform:translateX(-2px);
    transform:translateX(-2px)
  }
  to {
    opacity:1;
    -webkit-transform:translateX(0);
    transform:translateX(0)
  }
}
.radix-side-left\:animate-slideRightAndFade[data-side=left] {
  -webkit-animation:slideRightAndFade .4s cubic-bezier(.16,1,.3,1);
  animation:slideRightAndFade .4s cubic-bezier(.16,1,.3,1)
}
@keyframes slideLeftAndFade {
  0% {
    opacity:0;
    -webkit-transform:translateX(2px);
    transform:translateX(2px)
  }
  to {
    opacity:1;
    -webkit-transform:translateX(0);
    transform:translateX(0)
  }
}
.radix-side-right\:animate-slideLeftAndFade[data-side=right] {
  -webkit-animation:slideLeftAndFade .4s cubic-bezier(.16,1,.3,1);
  animation:slideLeftAndFade .4s cubic-bezier(.16,1,.3,1)
}
@keyframes slideDownAndFade {
  0% {
    opacity:0;
    -webkit-transform:translateY(-2px);
    transform:translateY(-2px)
  }
  to {
    opacity:1;
    -webkit-transform:translateY(0);
    transform:translateY(0)
  }
}
.radix-side-top\:animate-slideDownAndFade[data-side=top] {
  -webkit-animation:slideDownAndFade .4s cubic-bezier(.16,1,.3,1);
  animation:slideDownAndFade .4s cubic-bezier(.16,1,.3,1)
}

.azure-bg-color {
  background: linear-gradient(0.375turn, #61bde2, #4389d0);
}

@keyframes slowPulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.slow-pulse {
  animation: slowPulse 2.5s ease-in-out infinite;
}

.scrollbar-gutter-stable {
  scrollbar-gutter: stable;
}

/* Styles for Chrome scrollbar */
.chrome-scrollbar::-webkit-scrollbar {
  width: 12px;  /* Increase the width of the scrollbar */
}

.chrome-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);  /* Color of the scroll thumb */
  border-radius: 6px;  /* Rounded corners on the scroll thumb */
  border: 2px solid transparent;  /* Creates padding around scroll thumb */
  background-clip: padding-box;  /* Prevents background color from leaking outside the border */
}

.chrome-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.3);  /* Darker color when hovering */
}

.chrome-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;  /* Color of the tracking area */
}

.sp-preview-container {
  @apply flex h-full w-full grow flex-col justify-center;
}

.sp-preview {
  @apply flex h-full w-full grow flex-col justify-center;
}

.sp-preview-iframe {
  @apply grow;
}

.sp-wrapper {
  @apply flex h-full w-full grow flex-col justify-center;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  20%, 40%, 60%, 80% { transform: translateX(3px); }
}

.shake {
  animation: shake 0.5s cubic-bezier(.36,.07,.19,.97) both;
}

div[role="tabpanel"][data-state="active"][data-orientation="horizontal"][aria-labelledby^="radix-"][id^="radix-"][id$="-content-preview"] {
  scrollbar-gutter: stable !important;
  background-color: rgba(205, 205, 205, 0.66) !important;
}

div[role="tabpanel"][data-state="active"][data-orientation="horizontal"][aria-labelledby^="radix-"][id^="radix-"][id$="-content-preview"]::-webkit-scrollbar {
  width: 12px !important;
}

div[role="tabpanel"][data-state="active"][data-orientation="horizontal"][aria-labelledby^="radix-"][id^="radix-"][id$="-content-preview"]::-webkit-scrollbar-thumb {
  background-color: rgba(56, 56, 56) !important;
  border-radius: 6px !important;
  border: 2px solid transparent !important;
  background-clip: padding-box !important;
}

div[role="tabpanel"][data-state="active"][data-orientation="horizontal"][aria-labelledby^="radix-"][id^="radix-"][id$="-content-preview"]::-webkit-scrollbar-track {
  background-color: transparent !important;
}

.cm-content:focus {
  outline: none !important;
}

p.whitespace-pre-wrap a, li a {
  color: #0066cc;
  text-decoration: underline;
  font-weight: bold;
}

.dark p.whitespace-pre-wrap a, .dark li a {
  color: #52a0ff;
}