import React from 'react';
import { useLocalize } from '~/hooks';

// Interface for the landing intro component props
interface LandingIntroProps {
  onGetStarted: () => void;
  className?: string;
}

/**
 * LandingIntro Component
 *
 * Displays the introductory section with title, description, and CTA button
 * Reusable component that can be used in different contexts
 */
const LandingIntro: React.FC<LandingIntroProps> = ({ onGetStarted, className = '' }) => {
  const localize = useLocalize();

  return (
    <div className={`landing-content-container order-1 lg:order-2 ${className}`}>
      {/* Text Content Container - Consistent width for all text elements */}
      <div className="mx-auto max-w-lg space-y-6 lg:mx-0">
        {/* Title */}
        <h1 className="font-roboto text-landing-title font-normal text-landing-text-primary">
          {localize('com_landing_title')}
        </h1>

        {/* Description */}
        <p className="font-roboto text-landing-description font-medium text-landing-gray">
          {localize('com_landing_description')}
        </p>

        {/* CTA Button */}
        <div className="pt-4">
          <button
            onClick={onGetStarted}
            className="btn-landing w-full rounded-[20px] border border-landing-border-accent bg-landing-bg-accent font-roboto text-landing-text-inverse transition-all duration-200 hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-landing-border-accent focus:ring-offset-2"
            aria-label={localize('com_landing_cta_button')}
          >
            {localize('com_landing_cta_button')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default LandingIntro;
