import React from 'react';
import { useTranslation } from 'react-i18next';
import { type NavTabItem } from '~/common/types';

interface NavTabProps {
  tabs: NavTabItem[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const NavTab: React.FC<NavTabProps> = ({ tabs, activeTab, onTabChange }) => {
  const { t } = useTranslation();

  return (
    <div className="flex h-full items-center">
      {tabs.map((tab) => {
        const isActive = activeTab === tab.id;
        return (
          <button
            key={tab.id}
            onClick={() => onTabChange(tab.id)}
            className={`relative flex h-full items-center gap-1 px-4 font-roboto text-base font-medium leading-5 text-slate-800 ${
              isActive
                ? 'bg-gray-50'
                : 'bg-white hover:bg-gray-100'
            }`}
          >
            {/* Top indicator line for selected state */}
            {isActive && (
              <div className="absolute left-0 right-0 top-0 h-1 flex-shrink-0 bg-teal-600" />
            )}
            <img src={tab.icon} alt={t(tab.textKey)} className="h-6 w-6" />
            <span>{t(tab.textKey)}</span>
          </button>
        );
      })}
    </div>
  );
};

export default NavTab;
