import React from 'react';
import DisplayUsernameMessages from './DisplayUsernameMessages';
import DeleteAccount from './DeleteAccount';
import Avatar from './Avatar';
import { useAuthContext } from '~/hooks';

function Account() {
  const user = useAuthContext();

  return (
    <div className="flex flex-col gap-3 p-1 text-sm text-text-primary">
      <div className="pb-3">
        <DisplayUsernameMessages />
      </div>
      <div className="pb-3">
        <Avatar />
      </div>
      {/* 2FA is now managed through Keycloak */}
      {user?.user?.provider === 'keycloak' && (
        <div className="pb-3">
          <div className="rounded-md border border-border-light p-4">
            <h3 className="mb-2 font-medium">Two-Factor Authentication</h3>
            <p className="text-sm text-text-secondary">
              Two-factor authentication is managed through Keycloak. 
              Please configure 2FA in your Keycloak account settings.
            </p>
          </div>
        </div>
      )}
      <div className="pb-3">
        <DeleteAccount />
      </div>
    </div>
  );
}

export default React.memo(Account);
