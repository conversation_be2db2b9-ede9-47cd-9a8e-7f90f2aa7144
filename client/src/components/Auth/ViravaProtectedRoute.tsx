import { ReactNode } from 'react';
import { Navigate } from 'react-router-dom';
import { useAuthContext } from '~/hooks';

interface ViravaProtectedRouteProps {
  children: ReactNode;
}

function ViravaProtectedRoute({ children }: ViravaProtectedRouteProps) {
  const { isAuthenticated } = useAuthContext();

  if (isAuthenticated) {
    return <>{children}</>;
  }

  // Redirect to landing page if not authenticated
  return <Navigate to="/" replace />;
}

export default ViravaProtectedRoute;
