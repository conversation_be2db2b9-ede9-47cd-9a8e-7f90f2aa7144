import 'regenerator-runtime/runtime';
import { createRoot } from 'react-dom/client';
import axios from 'axios';

import './locales/i18n';
import './style.css';
import './mobile.css';
import 'katex/dist/katex.min.css';
import 'katex/dist/contrib/copy-tex.js';

import App from './App';
import { ApiErrorBoundaryProvider } from './hooks/ApiErrorBoundaryContext';
import { authConfig, authService } from './utils/virava';

let isRefreshing = false;
let refreshSubscribers = [];

const subscribeTokenRefresh = (cb) => {
  refreshSubscribers.push(cb);
};

const onTokenRefreshed = (token) => {
  refreshSubscribers.map((cb) => cb(token));
  refreshSubscribers = [];
};

const setupRequestInterceptor = () => {
  axios.interceptors.request.use((config) => {
    try {
      const token = authService.getAccessTokenRaw();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch {
      // No token - continue without Authorization header
    }
    return config;
  });
};

const setupResponseInterceptor = () => {
  axios.interceptors.response.use(
    (response) => response,
    async (error) => {
      const originalRequest = error.config;

      if (
        (error.response?.status === 401 || error.response?.status === 403) &&
        !originalRequest._retry
      ) {
        originalRequest._retry = true;

        if (isRefreshing) {
          return new Promise((resolve) => {
            subscribeTokenRefresh((token) => {
              if (token) {
                originalRequest.headers.Authorization = `Bearer ${token}`;
                resolve(axios(originalRequest));
              } else {
                resolve(Promise.reject(error));
              }
            });
          });
        }

        isRefreshing = true;

        try {
          const refreshed = await authService.updateToken();

          if (refreshed) {
            const newToken = authService.getAccessTokenRaw();
            if (newToken) {
              isRefreshing = false;
              onTokenRefreshed(newToken);
              originalRequest.headers.Authorization = `Bearer ${newToken}`;
              return axios(originalRequest);
            }
          }

          throw new Error('Token refresh returned false');
        } catch (refreshError) {
          isRefreshing = false;
          onTokenRefreshed(null);

          if (window.location.pathname !== '/') {
            window.location.href = '/';
          }
          return Promise.reject(refreshError);
        }
      }

      return Promise.reject(error);
    },
  );
};

const initializeApp = async () => {
  console.log('🚀 Initializing authentication service...');
  console.log('Auth config:', authConfig);

  try {
    await authService.init(authConfig);
    console.log('✅ Auth service initialized successfully');
    setupRequestInterceptor();
    setupResponseInterceptor();
  } catch (error) {
    console.error('❌ Failed to initialize auth service:', error);
    console.error('Auth config used:', authConfig);
    console.error('Error details:', {
      message: error.message,
      stack: error.stack,
      name: error.name,
    });
    throw error; // Re-throw to be caught by the main catch block
  }
};

const container = document.getElementById('root');
const root = createRoot(container);

initializeApp()
  .then(() => {
    console.log('✅ App initialization complete, rendering React app');
    root.render(
      <ApiErrorBoundaryProvider>
        <App />
      </ApiErrorBoundaryProvider>,
    );
  })
  .catch((err) => {
    console.error('💥 CRITICAL: Failed to initialize application');
    console.error('Error:', err);
    console.error('Environment variables available:', {
      VITE_CLIENT_ID: import.meta.env.VITE_CLIENT_ID,
      VITE_BASE_URL: import.meta.env.VITE_BASE_URL,
      VITE_REALM: import.meta.env.VITE_REALM,
      MODE: import.meta.env.MODE,
      DEV: import.meta.env.DEV,
      PROD: import.meta.env.PROD,
    });

    // Render error state instead of failing silently
    root.render(
      <div
        style={{
          padding: '20px',
          backgroundColor: '#fee',
          border: '1px solid #fcc',
          margin: '20px',
          borderRadius: '8px',
          fontFamily: 'monospace',
        }}
      >
        <h2 style={{ color: '#c33' }}>Authentication Service Failed to Initialize</h2>
        <p>
          <strong>Error:</strong> {err.message}
        </p>
        <details>
          <summary>Configuration Details</summary>
          <pre>
            {JSON.stringify(
              {
                VITE_CLIENT_ID: import.meta.env.VITE_CLIENT_ID || 'MISSING',
                VITE_BASE_URL: import.meta.env.VITE_BASE_URL || 'MISSING',
                VITE_REALM: import.meta.env.VITE_REALM || 'MISSING',
              },
              null,
              2,
            )}
          </pre>
        </details>
        <p>
          Please check the browser console for more details and verify your environment variables.
        </p>
      </div>,
    );
  });

export { authService };
