import { useCallback } from 'react';
import { useAuthContext } from '~/hooks';

export default function useViravaAuthRedirect() {
  const { user, isAuthenticated, login } = useAuthContext();

  const handleLogin = useCallback(() => {
    if (!isAuthenticated) {
      // Instead of navigating to login page, trigger Keycloak login directly
      login();
    }
  }, [isAuthenticated, login]);

  return {
    user,
    isAuthenticated,
    handleLogin,
  };
}