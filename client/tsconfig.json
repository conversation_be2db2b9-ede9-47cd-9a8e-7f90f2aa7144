{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": false, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": false, "module": "ESNext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "noImplicitAny": false, "noEmit": false, "declaration": true, "declarationDir": "./types", "jsx": "preserve", "baseUrl": "..", "paths": {"~/*": ["./client/src/*"], "test/*": ["./client/test/*"], "*": ["./client/*", "../node_modules/*"], "librechat-data-provider/*": ["./packages/data-provider/*"]}}, "types": ["node", "jest", "@testing-library/jest-dom"], "exclude": ["node_modules", "vite.config.ts"], "include": ["src/**/*", "test/**/*", "../e2e/**/*", "test/setupTests.js", "env.d.ts", "../config/translations/**/*.ts", "../packages/client/src/hooks/useDelayedRender.tsx"]}