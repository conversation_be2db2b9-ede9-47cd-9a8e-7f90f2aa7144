{"compilerOptions": {"declaration": true, "declarationDir": "./dist/types", "module": "esnext", "noImplicitAny": true, "outDir": "./types", "target": "es2015", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "lib": ["es2017", "dom", "ES2021.String"], "allowJs": true, "skipLibCheck": true, "esModuleInterop": true, "strict": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "sourceMap": true, "baseUrl": ".", "paths": {"~/*": ["./src/*"]}}, "ts-node": {"experimentalSpecifierResolution": "node", "transpileOnly": true, "esm": true}, "exclude": ["node_modules", "dist", "types"], "include": ["src/**/*", "types/index.d.ts", "types/react-query/index.d.ts"]}