apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "natcode.fullname" $ }}
  labels:
    {{- include "natcode.labels" . | nindent 4 }}
spec:
  replicas: {{ .Values.replicaCount }}
  {{- if .Values.updateStrategy }}
  strategy: {{- toYaml .Values.updateStrategy | nindent 4 }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "natcode.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{ if .Values.natcode.configYamlContent }}checksum/config: {{ include (print $.Template.BasePath "/configmap.yaml") . | sha256sum }}{{ end }}
        checksum/configEnv: {{ include (print $.Template.BasePath "/configmap-env.yaml") . | sha256sum }}
        {{- with .Values.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "natcode.labels" . | nindent 8 }}
        {{- with .Values.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ include "natcode.fullname" $ }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          lifecycle:
            {{- toYaml .Values.lifecycle | nindent 12 }}
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          volumeMounts:
          {{- if or .Values.natcode.configYamlContent .Values.natcode.existingConfigYaml }}
          - name: config-yaml
            mountPath: "/app/natcode.yaml"
            subPath: "natcode.yaml"
            readOnly: true
          {{- end }}
          {{- if .Values.natcode.imageVolume.enabled }}
          - name: image-volume
            mountPath: "/app/client/public/images"
          {{- end }}
          {{- if .Values.volumeMounts }}
          {{- toYaml .Values.volumeMounts | nindent 10 }}
          {{- end }}
          envFrom:
          - configMapRef:
              name: {{ include "natcode.fullname" $ }}-configenv
          {{- if .Values.global.natcode.existingSecretName }}
          - secretRef:
              name: {{ .Values.global.natcode.existingSecretName }}
              optional: true
          {{- end }}
      volumes:
      {{- if or .Values.natcode.configYamlContent .Values.natcode.existingConfigYaml }}
      - name: config-yaml
        configMap:
        {{- if .Values.natcode.existingConfigYaml }}
          name: {{ .Values.natcode.existingConfigYaml }}
        {{- else if .Values.natcode.configYamlContent }}
          name: {{ include "natcode.fullname" $ }}-config
        {{- end }}
      {{- end }}
      {{- if .Values.natcode.imageVolume.enabled }}
      - name: image-volume
        persistentVolumeClaim:
          claimName: {{ include "natcode.fullname" $ }}-images
      {{- end }}
      {{- if .Values.volumes }}
      {{- toYaml .Values.volumes | nindent 6 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
