kind: ConfigMap
apiVersion: v1
metadata:
  name: {{ include "natcode.fullname" $ }}-configenv
data:
  {{- if (index .Values "natcode-rag-api" "enabled") }}
  RAG_API_URL: http://{{ include "rag.fullname" (index .Subcharts "natcode-rag-api") | lower }}.{{ .Release.Namespace | lower }}.svc.cluster.local:8000 
  {{- end }}
  {{- if and (not (dig "configEnv" "MEILI_HOST" "" .Values.natcode)) .Values.meilisearch.enabled }}
  MEILI_HOST: http://{{ include "meilisearch.fullname" .Subcharts.meilisearch }}.{{ .Release.Namespace | lower }}.svc.cluster.local:7700
  {{- end }}
  {{- if and (not (dig "configEnv" "MONGO_URI" "" .Values.natcode)) .Values.mongodb.enabled }}
  MONGO_URI: mongodb://{{ include "mongodb.service.nameOverride" .Subcharts.mongodb }}.{{ .Release.Namespace | lower }}.svc.cluster.local:27017/natcode
  {{- end }}
  {{- if .Values.natcode.configEnv }}
  {{- toYaml .Values.natcode.configEnv | nindent 2 }}
  {{- end }}