{{- if .Values.natcode.imageVolume.enabled }}
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: {{ include "natcode.fullname" $ }}-images
spec:
  accessModes:
    - {{ .Values.natcode.imageVolume.accessModes }}
{{- if .Values.natcode.imageVolume.storageClassName }}
  storageClassName: {{ .Values.natcode.imageVolume.storageClassName }}
{{- end }}          
  volumeMode: Filesystem
  resources:
    requests:
      storage: {{ .Values.natcode.imageVolume.size }}
{{- end }}