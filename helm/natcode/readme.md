# LibreChat Helm Chart
This Librechat Helm Chart provides an easy, light weight template to deploy LibreChat on Kubernetes

## Variables
In this Chart, LibreChat will only work with environment Variables. You can Specify Vars and Secret using an existing Secret (This can be generated by [creating an Env File and converting it to a Kubernetes Secret](https://kubernetes.io/docs/reference/generated/kubectl/kubectl-commands#-em-secret-em-) `--from-env-file`)  

## Setup

1. Generate Variables
Generate `CREDS_KEY`, `JWT_SECRET`, `JWT_REFRESH_SECRET`  and `MEILI_MASTER_KEY`  using `openssl rand -hex 32` and `CREDS_IV` using openssl rand -hex 16.
place them in a secret like this (If you want to change the secret name, remember to change it in your helm values):
```yaml
apiVersion: v1
kind: Secret
metadata:
  name: librechat-credentials-env
  namespace: <librechat-chart-namespace>
type: Opaque
stringData:
  CREDS_KEY: <generated value>
  JWT_SECRET: <generated value>
  JWT_REFRESH_SECRET: <generated value>
  MEILI_MASTER_KEY: <generated value>
```
2. Add Credentials to the Secret
Dependant of the Model you want to use, [create Credentials in your provider](https://docs.librechat.ai/install/configuration/ai_setup.html) and add them to the Secret:
```yaml
apiVersion: v1
kind: Secret
. . . .

  OPENAI_API_KEY: <your secret value>
```

3. Apply the Secret to the Cluster

4. Fill out values.yaml and apply the Chart to the Cluster