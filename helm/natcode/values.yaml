replicaCount: 1

global:
   # existing Secret for all envs/ only Passwords. Can be locally generated with: kubectl create secret generic librechat-secret-envs --from-env-file=.env.example --dry-run=client -o yaml > secret-envs.yaml
   # For better maintainabillity, you can put all vars directly in the config Section and only overwrite Secrets with this if nessesary.
   # Required Values:
   # - MEILI_MASTER_KEY
  natcode:
    existingSecretName: "natcode-credentials-env"
    # Used for Setting the Right Key, can be something like AZURE_API_KEY, if Azure OpenAI is used
    existingSecretApiKey: OPENAI_API_KEY

natcode:
  configEnv:
    ANTHROPIC_MODELS: claude-3-opus-20240229
    PLUGIN_MODELS: gpt-4,gpt-4-turbo-preview,gpt-4-0125-preview,gpt-4-1106-preview,gpt-4-0613,gpt-3.5-turbo,gpt-3.5-turbo-0125,gpt-3.5-turbo-1106,gpt-3.5-turbo-0613
    # IMPORTANT -- GENERATE your own: openssl rand -hex 32 and openssl rand -hex 16 for CREDS_IV. Best Practise: Put into Secret. See global.librechat.existingSecretName
    # Set Config Params here
    # ENV_NAME: env-value

    # existing Secret for all envs/ only Passwords. Can be locally generated with: kubectl create secret generic librechat-secret-envs --from-env-file=.env.example --dry-run=client -o yaml > secret-envs.yaml
    # For better maintainabillity, you can put all vars directly in the config Section and only overwrite Secrets with this if nessesary.
    # Required Values:
    # - MEILI_MASTER_KEY
  existingSecretName: "natcode-credentials-env"

  # For adding a custom config yaml-file you can set the contents in this var. See https://www.librechat.ai/docs/configuration/librechat_yaml/example
  configYamlContent: ""
  # configYamlContent: |
  #   version: 1.0.8

  #   cache: true

  #   interface:
  #     # Privacy policy settings
  #     privacyPolicy:
  #       externalUrl: 'https://librechat.ai/privacy-policy'
  #       openNewTab: true

  #     # Terms of service
  #     termsOfService:
  #       externalUrl: 'https://librechat.ai/tos'
  #       openNewTab: true

  #   registration:
  #     socialLogins: ["discord", "facebook", "github", "google", "openid"] 
  #   endpoints:
  #     azureOpenAI:
  #      # Endpoint-level configuration
  #      titleModel: "gpt-4o"
  #      plugins: true
  #      assistants: true
  #      groups:
  #           Group-level configuration
  #         - group: "my-resource-sweden"
  #           apiKey: "${SWEDEN_API_KEY}"
  #           instanceName: "my-resource-sweden"
  #           deploymentName: gpt-4-1106-preview
  #           version: "2024-03-01-preview"
  #           assistants: true
  #           # Model-level configuration
  #           models:
  #             gpt-4o: true
  #     custom:
  #       # OpenRouter.ai
  #       - name: "OpenRouter"
  #         apiKey: "${OPENROUTER_KEY}"
  #         baseURL: "https://openrouter.ai/api/v1"
  #         models:
  #           default: ["openai/gpt-3.5-turbo"]
  #           fetch: true
  #         titleConvo: true
  #         titleModel: "gpt-3.5-turbo"
  #         summarize: false
  #         summaryModel: "gpt-3.5-turbo"
  #         forcePrompt: false
  #         modelDisplayLabel: "OpenRouter"

  # name of existing Yaml configmap, key must be natcode.yaml
  existingConfigYaml: ""

  # Volume used to store image Files uploaded to the Web UI
  imageVolume:
    enabled: true
    size: 10Gi
    accessModes: ReadWriteOnce
    storageClassName: azurefile-custom

# only lite RAG is supported
natcode-rag-api:
  enabled: true
  # can be azure, openai, huggingface or huggingfacetei
  embeddingsProvider: openai

image:
  repository: cleverpine.azurecr.io/natcode
  tag: latest
  pullPolicy: IfNotPresent

imagePullSecrets:
  - name: regcred
# nameOverride: ""
# fullnameOverride: ""

lifecycle: {}
# # base for adding a custom banner // see https://github.com/danny-avila/LibreChat/pull/3952 for an example
#   postStart:
#     exec:
#       command: ["/bin/sh", "-c", "npm run update-banner <displayFrom(Format: yyyy-mm-ddTHH:MM:SSZ)> <displayTo(Format: yyyy-mm-ddTHH:MM:SSZ)> <message> <isPublic(true/false)>"]



podAnnotations: {}
podLabels: {}

podSecurityContext:
  fsGroup: 2000

securityContext:
  capabilities:
    drop:
    - ALL
  # readOnlyRootFilesystem: true # not supported yet
  runAsNonRoot: true
  runAsUser: 1000

service:
  type: ClusterIP
  port: 3080
  annotations: {}

ingress:
  enabled: true
  className: "nginx"
  annotations:
    kubernetes.io/ingress.class: nginx
    kubernetes.io/tls-acme: "true"
    cert-manager.io/cluster-issuer: letsencrypt
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/whitelist-source-range: **************/32, *************, ************/32, ************/32, ************/32, *************/32, *************/32
  hosts:
    - host: natcode-dev.thepineslab.net
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls:
    - secretName: natcode-ssl
      hosts:
        - natcode-dev.thepineslab.net

resources:
  limits:
    cpu: 300m
    memory: 1000Mi
  requests:
    cpu: 50m
    memory: 500Mi

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

livenessProbe: null # enable later
readinessProbe: null # enable later

volumes:
- name: api-logs
  emptyDir: {}

volumeMounts:
- name: api-logs
  mountPath: /app/api/logs
#   readOnly: true

nodeSelector: {}

tolerations: []

affinity: {}

updateStrategy:
  type: Recreate

mongodb:
  enabled: false

meilisearch:
  enabled: true
  persistence:
    enabled: true
    storageClassName: azurefile-custom
    size: 10Gi
  image: 
    tag: "v1.7.3"
  auth:
    # Use an existing Kubernetes secret for the MEILI_MASTER_KEY
    existingMasterKeySecret: "natcode-credentials-env"