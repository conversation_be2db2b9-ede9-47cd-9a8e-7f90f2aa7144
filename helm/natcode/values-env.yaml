natcode:
  configEnv:
    ANTHROPIC_MODELS: claude-3-opus-20240229
    PLUGIN_MODELS: gpt-4,gpt-4-turbo-preview,gpt-4-0125-preview,gpt-4-1106-preview,gpt-4-0613,gpt-3.5-turbo,gpt-3.5-turbo-0125,gpt-3.5-turbo-1106,gpt-3.5-turbo-0613

    ALLOW_REGISTRATION: "true"
    ALLOW_SOCIAL_LOGIN: "true"
    TRUST_PROXY: "$(TRUST_PROXY)"

    MONGO_URI: "$(MONGO_URI)"
    MEILI_MASTER_KEY: "$(MEILI_MASTER_KEY)"
    MEILI_HOST: "$(MEILI_HOST)"
    MEILI_NO_ANALYTICS: "$(MEILI_NO_ANALYTICS)"
    SEARCH: "$(SEARCH)"

    CREDS_IV: "$(CREDS_IV)"
    CREDS_KEY: "$(CREDS_KEY)"
    JWT_SECRET: "$(JWT_SECRET)"
    JWT_REFRESH_SECRET: "$(JWT_REFRESH_SECRET)"

    DOMAIN_CLIENT: "$(DOMAIN_CLIENT)"
    DOMAIN_SERVER: "$(DOMAIN_SERVER)"

    OPENID_AUTH_SERVER_URL: "$(OPENID_AUTH_SERVER_URL)"
    OPENID_CLIENT_ID: "$(OPENID_CLIENT_ID)"
    OPENID_CLIENT_SECRET: "$(OPENID_CLIENT_SECRET)"
    OPENID_CALLBACK_URL: "$(OPENID_CALLBACK_URL)"
    OPENID_REALM: "$(OPENID_REALM)"
    OPENID_REQUIRED_ROLE_TOKEN_KIND: "$(OPENID_REQUIRED_ROLE_TOKEN_KIND)"
    OPENID_ISSUER: "$(OPENID_ISSUER)"
    OPENID_SCOPE: "$(OPENID_SCOPE)"
    OPENID_SESSION_SECRET: "$(OPENID_SESSION_SECRET)"
    OPENID_USE_END_SESSION_ENDPOINT: "$(OPENID_USE_END_SESSION_ENDPOINT)"
    OPENID_USE_PKCE: "$(OPENID_USE_PKCE)"

    VITE_BASE_URL: "$(VITE_BASE_URL)"
    VITE_CLIENT_ID: "$(VITE_CLIENT_ID)"
    VITE_REALM: "$(VITE_REALM)"

    ANTHROPIC_API_KEY: "$(ANTHROPIC_API_KEY)"

    OPENROUTER_KEY: "$(OPENROUTER_KEY)"

    OPENAI_API_KEY: "$(OPENAI_API_KEY)"

    BAN_DURATION: "$(BAN_DURATION)"
    BAN_INTERVAL: "$(BAN_INTERVAL)"
    BAN_VIOLATIONS: "$(BAN_VIOLATIONS)"
    CONCURRENT_MESSAGE_MAX: "$(CONCURRENT_MESSAGE_MAX)"
    CONCURRENT_VIOLATION_SCORE: "$(CONCURRENT_VIOLATION_SCORE)"
    GID: "$(GID)"
    LIMIT_CONCURRENT_MESSAGES: "$(LIMIT_CONCURRENT_MESSAGES)"
    LIMIT_MESSAGE_IP: "$(LIMIT_MESSAGE_IP)"
    LIMIT_MESSAGE_USER: "$(LIMIT_MESSAGE_USER)"
    LOGIN_MAX: "$(LOGIN_MAX)"
    LOGIN_VIOLATION_SCORE: "$(LOGIN_VIOLATION_SCORE)"
    LOGIN_WINDOW: "$(LOGIN_WINDOW)"
    MESSAGE_IP_MAX: "$(MESSAGE_IP_MAX)"
    MESSAGE_IP_WINDOW: "$(MESSAGE_IP_WINDOW)"
    MESSAGE_USER_MAX: "$(MESSAGE_USER_MAX)"
    MESSAGE_USER_WINDOW: "$(MESSAGE_USER_WINDOW)"
    MESSAGE_VIOLATION_SCORE: "$(MESSAGE_VIOLATION_SCORE)"
    NON_BROWSER_VIOLATION_SCORE: "$(NON_BROWSER_VIOLATION_SCORE)"
    REGISTER_MAX: "$(REGISTER_MAX)"
    REGISTER_WINDOW: "$(REGISTER_WINDOW)"
    UID: "$(UID)"

    CONSOLE_JSON: "$(CONSOLE_JSON)"
    DEBUG_CONSOLE: "$(DEBUG_CONSOLE)"
    DEBUG_LOGGING: "$(DEBUG_LOGGING)"
    DEBUG_OPENAI: "$(DEBUG_OPENAI)"
    DEBUG_PLUGINS: "$(DEBUG_PLUGINS)"
    NO_INDEX: "$(NO_INDEX)"