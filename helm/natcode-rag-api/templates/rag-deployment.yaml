apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ include "rag.fullname" $ }}
  labels:
    {{- include "rag.labels" . | nindent 4 }}
    rag-component: rag
spec:
  replicas: {{ .Values.rag.replicaCount }}
  selector:
    matchLabels:
      {{- include "rag.selectorLabels" . | nindent 6 }}
  template:
    metadata:
      annotations:
        {{- with .Values.rag.podAnnotations }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
      labels:
        {{- include "rag.selectorLabels" . | nindent 8 }}
        {{- with .Values.rag.podLabels }}
        {{- toYaml . | nindent 8 }}
        {{- end }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ include "rag.fullname" $ }}-rag
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.registry }}/{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.service.port }}
              protocol: TCP
          livenessProbe:
            {{- toYaml .Values.livenessProbe | nindent 12 }}
          readinessProbe:
            {{- toYaml .Values.readinessProbe | nindent 12 }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          envFrom:
          - configMapRef:
              name: {{ include "rag.fullname" $ }}-config
          {{- if .Values.rag.existingSecret }}
          - secretRef:
              name: {{ .Values.rag.existingSecret }}
          {{- end }}
          env:
          {{- if .Values.postgresql.auth.existingSecret }}
          - name: POSTGRES_PASSWORD
            valueFrom:
              secretKeyRef:
                name: {{ .Values.postgresql.auth.existingSecret }}
                key: {{ .Values.postgresql.auth.existingSecretKey }}
          {{- end }}
          {{- if .Values.global.natcode.existingSecretName }}
          - name: OPENAI_API_KEY
            valueFrom:
              secretKeyRef:
                name: {{ .Values.global.natcode.existingSecretName }}
                key: {{ .Values.global.natcode.existingSecretApiKey }}
                optional: true
          {{- end }}
          {{- with .Values.volumeMounts }}
          volumeMounts:
            {{- toYaml . | nindent 12 }}
          {{- end }}
        {{- with .Values.extraContainers }}
        {{ toYaml . | nindent 8 }}
        {{- end }}


      volumes:
      {{- with .Values.volumes }}
      {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
