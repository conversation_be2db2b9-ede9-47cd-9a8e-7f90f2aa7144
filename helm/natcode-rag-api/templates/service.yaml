apiVersion: v1
kind: Service
metadata:
  name: {{ include "rag.fullname" . }}
  labels:
    {{- include "rag.labels" . | nindent 4 }}
  annotations:
  {{- toYaml .Values.service.annotations | nindent 4 }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: {{ .Values.service.port }}
      targetPort: http
      protocol: TCP
      name: http
  selector:
    {{- include "rag.selectorLabels" . | nindent 4 }}
