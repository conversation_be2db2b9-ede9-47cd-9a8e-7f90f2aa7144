rag:
  enabled: true
  existingSecret: natcode-vectordb
  configEnv:
    EMBEDDINGS_PROVIDER: openai

image:
  repository: danny-avila/librechat-rag-api-dev-lite # there is rag-api-dev and rag-api-dev-lite. currently only lite is documented
  registry: ghcr.io
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: latest

postgresql:
  enabled: false
  auth:
    existingSecret: natcode-vectordb
    existingSecretKey: POSTGRES_PASSWORD

imagePullSecrets: []
nameOverride: ''
fullnameOverride: ''

podAnnotations: {}
podLabels: {}

podSecurityContext: {} # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 8000
  annotations: {}


resources:
  limits:
    cpu: 100m
    memory: 128Mi
  requests:
    cpu: 100m
    memory: 128Mi

livenessProbe: null # enable later
#   httpGet:
#     path: /
#     port: http
readinessProbe: null # enable later
#   httpGet:
#     path: /
#     port: http

volumes: []

volumeMounts: []

nodeSelector: {}

tolerations: []

affinity: {}

extraContainers: {}