const Keycloak = require('keycloak-connect');
const { logger } = require('@librechat/data-schemas');

let _keycloak;

/**
 * Sets up Keycloak authentication middleware in bearer-only mode
 * This is for APIs that expect JWT Bearer tokens (like your Java applications)
 */
function setupKeycloak(app) {
  try {
    logger.info('[setupKeycloak] Initializing Keycloak Connect middleware in bearer-only mode...');

    // Validate required environment variables
    if (!process.env.OPENID_REALM) {
      throw new Error('OPENID_REALM environment variable is required');
    }
    if (!process.env.OPENID_AUTH_SERVER_URL) {
      throw new Error('OPENID_AUTH_SERVER_URL environment variable is required');
    }
    if (!process.env.OPENID_CLIENT_ID) {
      throw new Error('OPENID_CLIENT_ID environment variable is required');
    }

    // Create Keycloak configuration from environment variables
    const keycloakConfig = {
      realm: process.env.OPENID_REALM,
      'auth-server-url': process.env.OPENID_AUTH_SERVER_URL,
      'ssl-required': 'external',
      resource: process.env.OPENID_CLIENT_ID,
      'bearer-only': true,
      'confidential-port': 0,
    };

    logger.info('[setupKeycloak] Using configuration:', {
      realm: keycloakConfig.realm,
      authServerUrl: keycloakConfig['auth-server-url'],
      resource: keycloakConfig.resource,
    });

    // Initialize Keycloak in bearer-only mode with configuration from env vars
    _keycloak = new Keycloak({}, keycloakConfig);

    // Install minimal middleware for bearer token validation
    app.use(_keycloak.middleware());

    logger.info('[setupKeycloak] Keycloak Connect bearer-only middleware initialized successfully');
    return _keycloak;
  } catch (error) {
    logger.error(`[setupKeycloak] Failed to initialize Keycloak: ${error.message}`);
    throw error;
  }
}

/**
 * Get the Keycloak instance
 * @returns {Keycloak} The Keycloak instance
 */
function getKeycloak() {
  if (!_keycloak) {
    throw new Error('Keycloak not initialized. Call setupKeycloak first.');
  }
  return _keycloak;
}

module.exports = {
  setupKeycloak,
  getKeycloak,
};
