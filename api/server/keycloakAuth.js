const { setupKeycloak } = require('~/strategies');
const { logger } = require('~/config');

/**
 * Configure Keycloak authentication
 * @param {Express.Application} app
 */
const configureKeycloakAuth = async (app) => {
  logger.info('Configuring Keycloak authentication...');

  if (
    process.env.OPENID_CLIENT_ID &&
    process.env.OPENID_REALM &&
    process.env.OPENID_AUTH_SERVER_URL
  ) {
    logger.info('Setting up Keycloak authentication with keycloak-connect...');

    try {
      const keycloak = setupKeycloak(app);
      logger.info('Keycloak Connect middleware configured successfully');

      // Store keycloak instance for route protection
      app.locals.keycloak = keycloak;
    } catch (error) {
      logger.error('Failed to configure Keycloak Connect:', error);
      throw error;
    }
  } else {
    logger.error(
      'Keycloak configuration missing: OPENID_CLIENT_ID, OPENID_REALM, and OPENID_AUTH_SERVER_URL are required',
    );
    throw new Error('Keycloak configuration missing');
  }
};

module.exports = configureKeycloakAuth;
