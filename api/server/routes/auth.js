const express = require('express');

const { logoutController } = require('~/server/controllers/auth/LogoutController');
const { requireJwtAuth } = require('~/server/middleware');

const router = express.Router();

// Keycloak Authentication Only
router.post('/logout', requireJwtA<PERSON>, logoutController);

// Disable local authentication endpoints - redirecting to OIDC
router.post('/login', (req, res) => {
  res.status(400).json({
    message: 'Local authentication disabled. Please use OAuth login.',
    redirectTo: '/oauth/openid',
  });
});

router.post('/register', (req, res) => {
  res.status(400).json({
    message: 'Local registration disabled. Please register through Keycloak.',
  });
});

router.post('/requestPasswordReset', (req, res) => {
  res.status(400).json({
    message: 'Password reset disabled. Please use Keycloak password reset.',
  });
});

router.post('/resetPassword', (req, res) => {
  res.status(400).json({
    message: 'Password reset disabled. Please use Keycloak password reset.',
  });
});

// Keep refresh endpoint but it will be handled differently
router.post('/refresh', (req, res) => {
  res.status(400).json({
    message: 'Token refresh handled by Keycloak. Please re-authenticate.',
  });
});

// 2FA endpoints disabled - handled by Keycloak
router.get('/2fa/enable', (req, res) => {
  res.status(400).json({
    message: '2FA is managed through Keycloak. Please configure in your Keycloak account.',
  });
});

router.post('/2fa/verify', (req, res) => {
  res.status(400).json({
    message: '2FA verification handled by Keycloak.',
  });
});

router.post('/2fa/verify-temp', (req, res) => {
  res.status(400).json({
    message: '2FA verification handled by Keycloak.',
  });
});

router.post('/2fa/confirm', (req, res) => {
  res.status(400).json({
    message: '2FA is managed through Keycloak.',
  });
});

router.post('/2fa/disable', (req, res) => {
  res.status(400).json({
    message: '2FA is managed through Keycloak.',
  });
});

router.post('/2fa/backup/regenerate', (req, res) => {
  res.status(400).json({
    message: '2FA backup codes are managed through Keycloak.',
  });
});

module.exports = router;
