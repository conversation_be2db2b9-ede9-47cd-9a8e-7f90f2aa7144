// file deepcode ignore NoRateLimitingForLogin: Rate limiting is handled by the `loginLimiter` middleware
const express = require('express');
const {
  checkBan,
  logHeaders,
  loginLimiter,
  checkDomainAllowed,
} = require('~/server/middleware');
const { logger } = require('~/config');

const router = express.Router();

const domains = {
  client: process.env.DOMAIN_CLIENT,
  server: process.env.DOMAIN_SERVER,
};

router.use(logHeaders);
router.use(loginLimiter);

/**
 * Keycloak OAuth Callback Handler
 * This endpoint may be used for OAuth callback flows if needed in the future
 */
const keycloakCallbackHandler = async (req, res) => {
  try {
    await checkDomainAllowed(req, res);
    await checkBan(req, res);
    if (req.banned) {
      return;
    }

    logger.info('[keycloakCallbackHandler] Processing Keycloak callback');
    // For now, just redirect to frontend - frontend handles Keycloak tokens directly
    res.redirect(`${domains.client}?auth=callback`);
  } catch (err) {
    logger.error('Error in Keycloak callback handler:', err);
    res.redirect(`${domains.client}/login?error=auth_failed`);
  }
};

/**
 * OAuth error handler
 */
router.get('/error', (req, res) => {
  logger.error('Error in OAuth authentication:', {
    message: req.query.error || 'Unknown error',
  });

  // Redirect to login page with auth_failed parameter
  res.redirect(`${domains.client}/login?redirect=false`);
});

/**
 * Keycloak callback route (placeholder for future use if needed)
 */
router.get('/openid/callback', keycloakCallbackHandler);

module.exports = router;
