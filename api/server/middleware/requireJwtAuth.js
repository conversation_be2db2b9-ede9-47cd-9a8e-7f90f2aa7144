const { logger } = require('@librechat/data-schemas');
const keycloakUserHandler = require('./keycloakUserHandler');

/**
 * Keycloak authentication middleware using keycloak-connect
 * This handles authentication + user creation/management
 */
const requireJwtAuth = (req, res, next) => {
  // Get Keycloak instance from app locals
  const keycloak = req.app.locals.keycloak;

  if (!keycloak) {
    logger.error(
      '[requireJwtAuth] Keycloak instance not found. Make sure Keycloak is properly initialized.',
    );
    return res.status(500).json({
      message: 'Authentication service not available',
    });
  }

  logger.debug(`[requireJwtAuth] Protecting ${req.method} ${req.path} with Keycloak`);

  // First, authenticate with Keycloak
  try {
    return keycloak.protect()(req, res, (err) => {
      if (err) {
        logger.error(`[requireJwtAuth] Keycloak authentication error: ${err.message}`);
        logger.error(`[requireJwtAuth] Error stack: ${err.stack}`);
        return res.status(401).json({
          message: 'Authentication required. Please provide a valid Bearer token.',
          error: err.message,
        });
      }

      logger.debug(`[requireJwtAuth] Keycloak authentication successful`);

      // After successful authentication, handle user creation/update
      keycloakUserHandler(req, res, next);
    });
  } catch (error) {
    logger.error(`[requireJwtAuth] Error calling keycloak.protect(): ${error.message}`);
    return res.status(500).json({
      message: 'Authentication service error',
      error: error.message,
    });
  }
};

module.exports = requireJwtAuth;
