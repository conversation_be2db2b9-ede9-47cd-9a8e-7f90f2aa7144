const { logger } = require('@librechat/data-schemas');
const keycloakUserHandler = require('./keycloakUserHandler');

// This middleware does not require authentication,
// but if the user is authenticated, it will set the user object.
const optionalJwtAuth = (req, res, next) => {
  // Get Keycloak instance from app locals
  const keycloak = req.app.locals.keycloak;

  if (!keycloak) {
    logger.debug('[optionalJwtAuth] Keycloak instance not found, continuing without auth');
    return next();
  }

  // Check if there's an Authorization header
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    // No token provided, continue without authentication
    return next();
  }

  logger.debug(`[optionalJwtAuth] Optional Keycloak auth for ${req.method} ${req.path}`);

  // Try to authenticate with Key<PERSON>loak (don't fail if invalid)
  return keycloak.protect()(req, res, (err) => {
    if (err) {
      // Authentication failed, but this is optional - continue without user
      logger.debug(
        `[optionalJwtAuth] Authentication failed, continuing without user: ${err.message}`,
      );
      return next();
    }

    // Authentication succeeded, now handle user creation/update
    keycloakUserHandler(req, res, (userErr) => {
      if (userErr) {
        logger.error(`[optionalJwtAuth] User handler error: ${userErr.message}`);
        return next(); // Continue without user on error
      }
      next(); // Continue with authenticated user
    });
  });
};

module.exports = optionalJwtAuth;
