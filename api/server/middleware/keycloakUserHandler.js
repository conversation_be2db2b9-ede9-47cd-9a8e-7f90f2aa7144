const { logger } = require('@librechat/data-schemas');
const { findUser, createUser, updateUser } = require('~/models');
const { getBalanceConfig } = require('~/server/services/Config');
const { SystemRoles } = require('librechat-data-provider');

/**
 * Middleware to handle user creation/update after Keycloak authentication
 * This ensures every authenticated user exists in our database with proper roles
 */
const keycloakUserHandler = async (req, res, next) => {
  try {
    // Check if user was authenticated by Keycloak
    if (!req.kauth || !req.kauth.grant) {
      logger.debug('[keycloakUserHandler] No Keycloak authentication found');
      return next();
    }

    const token = req.kauth.grant.access_token;
    const claims = token.content;

    logger.debug(`[keycloakUserHandler] Processing user with sub: ${claims.sub}`);

    // Look for existing user by Keycloak ID
    let user = await findUser({ keycloakId: claims.sub });

    if (!user) {
      // If not found by keycloakId, try by email (for migration)
      user = await findUser({ email: claims.email });

      if (user) {
        // Update existing user with Keycloak info
        logger.info(`[keycloakUserHandler] Migrating existing user ${claims.email} to Keycloak`);
        user = await updateUser(user._id, {
          keycloakId: claims.sub,
          provider: 'keycloak',
          lastLogin: new Date(),
        });
      } else {
        // Create new user
        logger.info(`[keycloakUserHandler] Creating new user from Keycloak: ${claims.email}`);

        try {
          const userData = {
            provider: 'keycloak',
            keycloakId: claims.sub,
            username: claims.preferred_username || claims.email || 'unknown',
            email: claims.email || '',
            emailVerified: claims.email_verified || false,
            name: claims.name || claims.preferred_username || 'Unknown User',
            role: SystemRoles.USER, // Default role: basic_user
            lastLogin: new Date(),
          };

          const balanceConfig = await getBalanceConfig();
          user = await createUser(userData, balanceConfig, true, true);

          logger.info(
            `[keycloakUserHandler] New user created: ${user.email} with role: ${user.role}`,
          );
        } catch (createError) {
          // Handle race condition - another request might have created the user
          if (createError.code === 11000) {
            logger.debug(
              `[keycloakUserHandler] User creation race condition detected, looking up existing user: ${claims.email}`,
            );
            // Try to find the user again (it was just created by another request)
            user = await findUser({ $or: [{ keycloakId: claims.sub }, { email: claims.email }] });
            if (!user) {
              throw new Error('User creation failed and user not found after race condition');
            }
            // Update with Keycloak info if needed
            if (!user.keycloakId) {
              user = await updateUser(user._id, {
                keycloakId: claims.sub,
                provider: 'keycloak',
                lastLogin: new Date(),
              });
            }
            logger.info(
              `[keycloakUserHandler] Found existing user after race condition: ${user.email}`,
            );
          } else {
            throw createError; // Re-throw non-duplicate errors
          }
        }
      }
    } else {
      // Update last login time for existing Keycloak users
      user = await updateUser(user._id, { lastLogin: new Date() });
    }

    // Attach user to request for downstream middleware
    req.user = {
      id: user._id.toString(),
      _id: user._id,
      keycloakId: user.keycloakId,
      username: user.username,
      email: user.email,
      name: user.name,
      role: user.role || SystemRoles.USER,
      provider: user.provider,
      emailVerified: user.emailVerified,
      lastLogin: user.lastLogin,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt,
    };

    logger.debug(`[keycloakUserHandler] User attached to request: ${user.email} (${user.role})`);
    next();
  } catch (error) {
    logger.error(`[keycloakUserHandler] Error processing user: ${error.message}`);
    return res.status(500).json({
      message: 'Error processing user authentication',
      error: error.message,
    });
  }
};

module.exports = keycloakUserHandler;
