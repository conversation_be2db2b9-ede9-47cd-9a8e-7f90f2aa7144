#!/bin/bash

# Debug script for Kubernetes environment variables
# This script helps diagnose environment variable issues in AKS deployment

set -e

echo "🔍 Kubernetes Environment Variables Debug Script"
echo "================================================"

# Function to check if kubectl is available
check_kubectl() {
    if ! command -v kubectl &> /dev/null; then
        echo "❌ kubectl is not installed or not in PATH"
        exit 1
    fi
    echo "✅ kubectl is available"
}

# Function to get pod name
get_pod_name() {
    local app_label=${1:-"natcode"}
    echo "🔍 Looking for pods with label app=$app_label..."
    
    POD_NAME=$(kubectl get pods -l app=$app_label -o jsonpath='{.items[0].metadata.name}' 2>/dev/null || echo "")
    
    if [ -z "$POD_NAME" ]; then
        echo "❌ No pods found with label app=$app_label"
        echo "Available pods:"
        kubectl get pods
        exit 1
    fi
    
    echo "✅ Found pod: $POD_NAME"
}

# Function to check environment variables in pod
check_env_vars() {
    echo ""
    echo "🔍 Checking environment variables in pod: $POD_NAME"
    echo "================================================"
    
    echo "📋 All environment variables:"
    kubectl exec $POD_NAME -- env | sort
    
    echo ""
    echo "🔑 Keycloak-related environment variables:"
    kubectl exec $POD_NAME -- env | grep -E "(VITE_|OPENID_|KEYCLOAK)" | sort || echo "No Keycloak env vars found"
    
    echo ""
    echo "🎯 Specific VITE variables:"
    for var in VITE_CLIENT_ID VITE_BASE_URL VITE_REALM; do
        value=$(kubectl exec $POD_NAME -- printenv $var 2>/dev/null || echo "NOT_SET")
        if [ "$value" = "NOT_SET" ]; then
            echo "❌ $var: NOT SET"
        else
            echo "✅ $var: $value"
        fi
    done
}

# Function to check pod logs for initialization errors
check_logs() {
    echo ""
    echo "📋 Recent pod logs (last 50 lines):"
    echo "===================================="
    kubectl logs $POD_NAME --tail=50
    
    echo ""
    echo "🔍 Searching for authentication-related errors:"
    echo "==============================================="
    kubectl logs $POD_NAME | grep -i -E "(auth|keycloak|service not initialized|failed to initialize)" || echo "No authentication errors found in logs"
}

# Function to check configmap and secrets
check_config() {
    echo ""
    echo "🔍 Checking ConfigMaps and Secrets:"
    echo "==================================="
    
    echo "ConfigMaps:"
    kubectl get configmaps | grep -E "(natcode|librechat)" || echo "No relevant ConfigMaps found"
    
    echo ""
    echo "Secrets:"
    kubectl get secrets | grep -E "(natcode|librechat)" || echo "No relevant Secrets found"
    
    # Try to show the secret content (keys only, not values)
    SECRET_NAME=$(kubectl get secrets | grep -E "(natcode|librechat)" | head -1 | awk '{print $1}' || echo "")
    if [ ! -z "$SECRET_NAME" ]; then
        echo ""
        echo "Keys in secret $SECRET_NAME:"
        kubectl get secret $SECRET_NAME -o jsonpath='{.data}' | jq -r 'keys[]' 2>/dev/null || echo "Could not read secret keys"
    fi
}

# Function to test connectivity to Keycloak
test_keycloak_connectivity() {
    echo ""
    echo "🌐 Testing Keycloak connectivity from pod:"
    echo "=========================================="
    
    # Get the VITE_BASE_URL from the pod
    KEYCLOAK_URL=$(kubectl exec $POD_NAME -- printenv VITE_BASE_URL 2>/dev/null || echo "")
    
    if [ -z "$KEYCLOAK_URL" ]; then
        echo "❌ VITE_BASE_URL not set, cannot test connectivity"
        return
    fi
    
    echo "Testing connectivity to: $KEYCLOAK_URL"
    
    # Test basic connectivity
    kubectl exec $POD_NAME -- curl -s -o /dev/null -w "%{http_code}" "$KEYCLOAK_URL" 2>/dev/null || echo "❌ Failed to connect to Keycloak"
    
    # Test realm endpoint
    REALM=$(kubectl exec $POD_NAME -- printenv VITE_REALM 2>/dev/null || echo "")
    if [ ! -z "$REALM" ]; then
        REALM_URL="${KEYCLOAK_URL}realms/${REALM}"
        echo "Testing realm endpoint: $REALM_URL"
        kubectl exec $POD_NAME -- curl -s -o /dev/null -w "%{http_code}" "$REALM_URL" 2>/dev/null || echo "❌ Failed to connect to realm endpoint"
    fi
}

# Main execution
main() {
    local app_label=${1:-"natcode"}
    
    check_kubectl
    get_pod_name $app_label
    check_env_vars
    check_logs
    check_config
    test_keycloak_connectivity
    
    echo ""
    echo "🎯 Summary and Next Steps:"
    echo "========================="
    echo "1. Check if all VITE_* variables are properly set"
    echo "2. Verify Keycloak server is accessible from the pod"
    echo "3. Check browser console for detailed error messages"
    echo "4. Ensure environment variables are injected at runtime, not just build time"
    echo ""
    echo "If VITE_* variables are missing, check your Helm values or Kubernetes manifests"
    echo "to ensure they're properly configured in the deployment."
}

# Run the script
main "$@"
