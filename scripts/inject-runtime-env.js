#!/usr/bin/env node

/**
 * Runtime Environment Variable Injection Script
 * 
 * This script helps inject environment variables at runtime for containerized deployments
 * where VITE_* variables need to be available in the browser but are set at deployment time.
 * 
 * Usage: node scripts/inject-runtime-env.js
 */

const fs = require('fs');
const path = require('path');

// Configuration
const DIST_DIR = path.join(__dirname, '..', 'client', 'dist');
const INDEX_HTML_PATH = path.join(DIST_DIR, 'index.html');
const ENV_JS_PATH = path.join(DIST_DIR, 'runtime-env.js');

// Environment variables to inject
const ENV_VARS = [
  'VITE_CLIENT_ID',
  'VITE_BASE_URL', 
  'VITE_REALM'
];

/**
 * Generate runtime environment JavaScript
 */
function generateRuntimeEnv() {
  const envConfig = {};
  
  ENV_VARS.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      envConfig[varName] = value;
      console.log(`✅ ${varName}: ${value}`);
    } else {
      console.warn(`⚠️  ${varName}: NOT SET`);
      envConfig[varName] = '';
    }
  });

  const jsContent = `
// Runtime Environment Configuration
// Generated at: ${new Date().toISOString()}
window.__RUNTIME_ENV__ = ${JSON.stringify(envConfig, null, 2)};

// Override import.meta.env for compatibility
if (typeof window !== 'undefined') {
  window.__VITE_ENV__ = window.__RUNTIME_ENV__;
}

console.log('🔧 Runtime environment variables loaded:', window.__RUNTIME_ENV__);
`;

  return jsContent;
}

/**
 * Inject script tag into index.html
 */
function injectScriptTag() {
  if (!fs.existsSync(INDEX_HTML_PATH)) {
    console.error(`❌ index.html not found at: ${INDEX_HTML_PATH}`);
    return false;
  }

  let htmlContent = fs.readFileSync(INDEX_HTML_PATH, 'utf8');
  
  // Check if script is already injected
  if (htmlContent.includes('runtime-env.js')) {
    console.log('✅ Runtime env script already injected');
    return true;
  }

  // Inject script tag before closing head tag
  const scriptTag = '  <script src="/runtime-env.js"></script>\n</head>';
  htmlContent = htmlContent.replace('</head>', scriptTag);

  fs.writeFileSync(INDEX_HTML_PATH, htmlContent);
  console.log('✅ Injected runtime-env.js script tag into index.html');
  return true;
}

/**
 * Create runtime environment file
 */
function createRuntimeEnvFile() {
  const jsContent = generateRuntimeEnv();
  
  // Ensure dist directory exists
  if (!fs.existsSync(DIST_DIR)) {
    console.error(`❌ Dist directory not found: ${DIST_DIR}`);
    console.log('Please run the build process first: npm run frontend');
    return false;
  }

  fs.writeFileSync(ENV_JS_PATH, jsContent);
  console.log(`✅ Created runtime environment file: ${ENV_JS_PATH}`);
  return true;
}

/**
 * Main execution
 */
function main() {
  console.log('🔧 Runtime Environment Variable Injection');
  console.log('==========================================');
  
  console.log('\n📋 Environment Variables:');
  ENV_VARS.forEach(varName => {
    const value = process.env[varName];
    if (value) {
      console.log(`✅ ${varName}: ${value}`);
    } else {
      console.log(`❌ ${varName}: NOT SET`);
    }
  });

  console.log('\n🔨 Processing...');
  
  if (!createRuntimeEnvFile()) {
    process.exit(1);
  }
  
  if (!injectScriptTag()) {
    process.exit(1);
  }

  console.log('\n✅ Runtime environment injection complete!');
  console.log('\n📝 Next steps:');
  console.log('1. Update your frontend code to use window.__RUNTIME_ENV__ as fallback');
  console.log('2. Ensure this script runs after build but before serving');
  console.log('3. In Kubernetes, run this as an init container or startup script');
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = {
  generateRuntimeEnv,
  createRuntimeEnvFile,
  injectScriptTag,
  ENV_VARS
};
