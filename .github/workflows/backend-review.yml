name: Backend Unit Tests
on:
  pull_request:
    branches:
      - main
      - dev
      - release/*
    paths:
      - 'api/**'
      - 'packages/**'
jobs:
  tests_Backend:
    name: Run Backend unit tests
    timeout-minutes: 60
    runs-on: ubuntu-latest
    env:
      MONGO_URI: ${{ secrets.MONGO_URI }}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
      JWT_SECRET: ${{ secrets.JWT_SECRET }}
      CREDS_KEY: ${{ secrets.CREDS_KEY }}
      CREDS_IV: ${{ secrets.CREDS_IV }}
      BAN_VIOLATIONS: ${{ secrets.BAN_VIOLATIONS }}
      BAN_DURATION: ${{ secrets.BAN_DURATION }}
      BAN_INTERVAL: ${{ secrets.BAN_INTERVAL }}
      NODE_ENV: CI
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js 20.x
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Data Provider Package
        run: npm run build:data-provider

      - name: Install Data Schemas Package
        run: npm run build:data-schemas

      - name: Install API Package
        run: npm run build:api

      - name: Create empty auth.json file
        run: |
          mkdir -p api/data
          echo '{}' > api/data/auth.json

      - name: Check for Circular dependency in rollup
        working-directory: ./packages/data-provider
        run: |
          output=$(npm run rollup:api)
          echo "$output"
          if echo "$output" | grep -q "Circular dependency"; then
            echo "Error: Circular dependency detected!"
            exit 1
          fi

      - name: Prepare .env.test file
        run: cp api/test/.env.test.example api/test/.env.test

      - name: Run unit tests
        run: cd api && npm run test:ci

      - name: Run librechat-data-provider unit tests
        run: cd packages/data-provider && npm run test:ci

      - name: Run @librechat/data-schemas unit tests
        run: cd packages/data-schemas && npm run test:ci

      - name: Run @librechat/api unit tests
        run: cd packages/api && npm run test:ci