name: Update Test Server

on:
  workflow_run:
    workflows: ["Docker Dev Branch Images Build"]
    types:
      - completed
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: |
      github.repository == 'danny-avila/LibreChat' &&
      (github.event_name == 'workflow_dispatch' || 
       (github.event.workflow_run.conclusion == 'success' && github.event.workflow_run.head_branch == 'dev'))
    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Install SSH Key
      uses: shimataro/ssh-key-action@v2
      with:
        key: ${{ secrets.DO_SSH_PRIVATE_KEY }}
        known_hosts: ${{ secrets.DO_KNOWN_HOSTS }}

    - name: Run update script on DigitalOcean Droplet
      env:
        DO_HOST: ${{ secrets.DO_HOST }}
        DO_USER: ${{ secrets.DO_USER }}
      run: |
        ssh -o StrictHostKeyChecking=no ${DO_USER}@${DO_HOST} << EOF
        sudo -i -u danny bash << 'EEOF'
        cd ~/LibreChat && \
        git fetch origin main && \
        sudo npm run stop:deployed && \
        sudo docker images --format "{{.Repository}}:{{.ID}}" | grep -E "lc-dev|librechat" | cut -d: -f2 | xargs -r sudo docker rmi -f || true && \
        sudo npm run update:deployed && \
        git checkout dev && \
        git pull origin dev && \
        git checkout do-deploy && \
        git rebase dev && \
        sudo npm run start:deployed && \
        echo "Update completed. Application should be running now."
        EEOF
        EOF
